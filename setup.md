# 花语小铺 Vue 3 管理后台 - 完整安装指南

本指南将帮助您完整地设置花语小铺的 Vue 3 管理后台系统。

## 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Vue 3 Admin   │    │  Spring Boot    │    │     MySQL       │
│   Frontend      │◄──►│    Backend      │◄──►│   Database      │
│  (Port: 3000)   │    │  (Port: 8080)   │    │  (Port: 3306)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │
         │                       │
         ▼                       ▼
┌─────────────────┐    ┌─────────────────┐
│  WeChat Mini    │    │   Admin APIs    │
│   Program       │    │   JWT Auth      │
│   Frontend      │    │   File Upload   │
└─────────────────┘    └─────────────────┘
```

## 前置要求

### 软件环境
- **Node.js**: >= 16.0.0
- **Java**: JDK 8 或更高版本
- **MySQL**: 5.7 或更高版本
- **Maven**: 3.6 或更高版本

### 开发工具（推荐）
- **IDE**: IntelliJ IDEA / Eclipse / VS Code
- **数据库工具**: Navicat / MySQL Workbench
- **API 测试**: Postman / Apifox

## 第一步：数据库设置

### 1.1 创建数据库

```sql
-- 创建数据库
CREATE DATABASE flower_shop CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE flower_shop;
```

### 1.2 导入基础数据

```bash
# 导入主数据库结构和数据
mysql -u root -p flower_shop < flower_shop.sql

# 导入管理员用户表
mysql -u root -p flower_shop < admin_users.sql
```

### 1.3 验证数据库

```sql
-- 检查表是否创建成功
SHOW TABLES;

-- 检查管理员账号
SELECT * FROM admin_users;
```

## 第二步：后端设置

### 2.1 配置数据库连接

编辑 `springBoot/src/main/resources/application.yml`：

```yaml
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *****************************************************************************************************************************************************
    username: root
    password: 你的数据库密码

# JWT 配置
jwt:
  secret: flower-admin-secret-key-2025
  expiration: 86400  # 24小时
```

### 2.2 安装依赖并启动

```bash
# 进入后端目录
cd springBoot

# 安装依赖
mvn clean install

# 启动应用
mvn spring-boot:run
```

### 2.3 验证后端服务

访问以下地址验证服务：

- 健康检查: http://localhost:8080/api/test/db
- API 文档: http://localhost:8080/api (如果配置了 Swagger)

## 第三步：前端设置

### 3.1 安装依赖

```bash
# 进入前端目录
cd Vue

# 安装依赖
npm install
# 或使用 yarn
yarn install
```

### 3.2 启动开发服务器

```bash
# 启动开发服务器
npm run dev
# 或使用 yarn
yarn dev
```

### 3.3 访问管理后台

打开浏览器访问: http://localhost:3000

**默认登录账号:**
- 用户名: `admin`
- 密码: `123456`

## 第四步：功能验证

### 4.1 登录测试

1. 访问 http://localhost:3000
2. 使用默认账号登录
3. 验证是否能正常进入仪表盘

### 4.2 数据管理测试

1. **用户管理**: 查看微信小程序用户列表
2. **商品管理**: 添加/编辑/删除商品
3. **分类管理**: 管理商品分类
4. **订单管理**: 查看和管理订单

### 4.3 API 接口测试

使用 Postman 测试关键接口：

```bash
# 管理员登录
POST http://localhost:8080/api/admin/login
Content-Type: application/json

{
  "username": "admin",
  "password": "123456"
}

# 获取统计数据（需要 Bearer Token）
GET http://localhost:8080/api/admin/stats
Authorization: Bearer YOUR_JWT_TOKEN
```

## 第五步：生产部署

### 5.1 前端构建

```bash
# 构建生产版本
cd Vue
npm run build

# 构建产物在 dist/ 目录
```

### 5.2 后端打包

```bash
# 打包 Spring Boot 应用
cd springBoot
mvn clean package

# JAR 文件在 target/ 目录
```

### 5.3 Nginx 配置

```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    # 前端静态文件
    location / {
        root /path/to/Vue/dist;
        index index.html;
        try_files $uri $uri/ /index.html;
    }
    
    # 后端 API 代理
    location /api {
        proxy_pass http://localhost:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## 故障排除

### 常见问题

#### 1. 数据库连接失败
```
错误: Access denied for user 'root'@'localhost'
解决: 检查数据库用户名密码，确保 MySQL 服务正在运行
```

#### 2. 前端无法访问后端 API
```
错误: Network Error 或 CORS 错误
解决: 检查后端是否启动，确认端口号正确
```

#### 3. JWT Token 验证失败
```
错误: Token无效
解决: 检查 JWT secret 配置，确保前后端一致
```

#### 4. 图片上传失败
```
错误: 文件上传接口不存在
解决: 确保实现了 /api/admin/upload/image 接口
```

### 日志查看

#### 后端日志
```bash
# 查看 Spring Boot 应用日志
tail -f springBoot/logs/application.log
```

#### 前端调试
```bash
# 开启浏览器开发者工具
F12 -> Console/Network 标签页
```

## 扩展功能

### 添加新的管理功能

1. **后端**: 在 `AdminController` 中添加新的接口
2. **前端**: 在 `src/views/` 中创建新页面
3. **路由**: 在 `src/router/index.js` 中添加路由
4. **菜单**: 在 `MainLayout.vue` 中添加菜单项

### 自定义主题

编辑 `src/styles/index.css` 修改全局样式，或使用 Element Plus 的主题定制功能。

## 技术支持

如果在安装过程中遇到问题，请检查：

1. **环境版本**: 确保 Node.js、Java、MySQL 版本符合要求
2. **端口占用**: 确保 3000、8080、3306 端口未被占用
3. **防火墙**: 确保相关端口已开放
4. **权限问题**: 确保有足够的文件读写权限

## 下一步

安装完成后，您可以：

1. 自定义管理后台界面
2. 添加更多管理功能
3. 集成更多第三方服务
4. 优化性能和安全性

祝您使用愉快！🌸
