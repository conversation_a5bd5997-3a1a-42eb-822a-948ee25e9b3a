@echo off
chcp 65001 >nul
echo === 花店后端项目部署脚本 ===

REM 设置变量
set APP_NAME=flower-shop
set JAR_NAME=flower-shop-1.0.0.jar
set APP_PORT=8080
set PROFILE=prod

REM 检查Java环境
echo 检查Java环境...
java -version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Java环境，请先安装Java 17
    pause
    exit /b 1
)

REM 停止现有进程
echo 停止现有进程...
for /f "tokens=2" %%i in ('tasklist /fi "imagename eq java.exe" /fo csv ^| findstr "%JAR_NAME%"') do (
    echo 发现运行中的进程，正在停止...
    taskkill /f /pid %%i >nul 2>&1
)

REM 创建日志目录
echo 创建日志目录...
if not exist logs mkdir logs

REM 备份旧版本
if exist %JAR_NAME% (
    echo 备份旧版本...
    for /f "tokens=1-3 delims=/ " %%a in ('date /t') do set mydate=%%c%%a%%b
    for /f "tokens=1-2 delims=: " %%a in ('time /t') do set mytime=%%a%%b
    move %JAR_NAME% %JAR_NAME%.backup.%mydate%_%mytime% >nul
)

REM 启动应用
echo 启动应用...
start /b java -jar %JAR_NAME% --spring.profiles.active=%PROFILE% > logs\startup.log 2>&1

REM 等待启动
echo 等待应用启动...
timeout /t 10 /nobreak >nul

REM 检查启动状态
tasklist /fi "imagename eq java.exe" | findstr "java.exe" >nul
if errorlevel 1 (
    echo 应用启动失败，请检查日志文件 logs\startup.log
    pause
    exit /b 1
) else (
    echo 应用启动成功！
    echo 端口: %APP_PORT%
    echo 访问地址: http://www.mxm.qiangs.xyz:%APP_PORT%/api
    echo 日志文件: logs\flower-shop.log
)

echo === 部署完成 ===
pause
