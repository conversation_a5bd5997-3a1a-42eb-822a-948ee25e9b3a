package com.flower.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 城市实体类
 */
@Data
@TableName("cities")
public class City {
    
    @TableId(type = IdType.AUTO)
    private Integer id;
    
    /**
     * 城市代码
     */
    private String code;
    
    /**
     * 城市名称
     */
    private String name;
    
    /**
     * 所属省份代码
     */
    @TableField("province_code")
    private String provinceCode;
    
    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
}
