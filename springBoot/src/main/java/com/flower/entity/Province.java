package com.flower.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 省份实体类
 */
@Data
@TableName("provinces")
public class Province {
    
    @TableId(type = IdType.AUTO)
    private Integer id;
    
    /**
     * 省份代码
     */
    private String code;
    
    /**
     * 省份名称
     */
    private String name;
    
    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
}
