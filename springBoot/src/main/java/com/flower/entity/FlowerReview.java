package com.flower.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 花卉评价实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("flower_reviews")
public class FlowerReview {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 花卉ID
     */
    private Long flowerId;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 评分（1-5星）
     */
    private Integer rating;

    /**
     * 评价内容
     */
    private String content;

    /**
     * 评价图片JSON数组
     */
    private String images;

    /**
     * 是否匿名：0否，1是
     */
    private Integer isAnonymous;

    /**
     * 状态：1正常，0隐藏
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
}
