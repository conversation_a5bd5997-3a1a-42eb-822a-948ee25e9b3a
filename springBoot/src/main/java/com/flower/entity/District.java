package com.flower.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 区县实体类
 */
@Data
@TableName("districts")
public class District {
    
    @TableId(type = IdType.AUTO)
    private Integer id;
    
    /**
     * 区县代码
     */
    private String code;
    
    /**
     * 区县名称
     */
    private String name;
    
    /**
     * 所属城市代码
     */
    @TableField("city_code")
    private String cityCode;
    
    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
}
