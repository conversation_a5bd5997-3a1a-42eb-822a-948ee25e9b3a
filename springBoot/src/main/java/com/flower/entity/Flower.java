package com.flower.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 花卉商品实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("flowers")
public class Flower {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 花卉名称
     */
    private String name;

    /**
     * 花卉描述
     */
    private String description;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 原价（用于显示折扣）
     */
    private BigDecimal originalPrice;

    /**
     * 分类ID
     */
    private Long categoryId;

    /**
     * 库存数量
     */
    private Integer stockQuantity;

    /**
     * 销售数量
     */
    private Integer salesCount;

    /**
     * 主图片链接
     */
    private String mainImage;

    /**
     * 附加图片JSON数组
     */
    private String images;

    /**
     * 标签（逗号分隔）
     */
    private String tags;

    /**
     * 花语寓意
     */
    private String flowerLanguage;

    /**
     * 养护说明
     */
    private String careInstructions;

    /**
     * 适用场合
     */
    private String occasion;

    /**
     * 主要颜色
     */
    private String color;

    /**
     * 规格描述
     */
    private String size;

    /**
     * 是否精选：1精选，0普通
     */
    private Integer isFeatured;

    /**
     * 状态：1上架，0下架
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
}
