package com.flower;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * 花语小铺微信小程序后端应用
 */
@SpringBootApplication
@MapperScan("com.flower.mapper")
public class FlowerShopApplication {

    public static void main(String[] args) {
        SpringApplication.run(FlowerShopApplication.class, args);
        System.out.println("=================================");
        System.out.println("花语小铺后端服务启动成功！");
        System.out.println("API接口地址: http://localhost:8080/api");
        System.out.println("=================================");
    }
}
