package com.flower.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.bouncycastle.jce.provider.BouncyCastleProvider;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.Security;
import java.util.Base64;

/**
 * 微信小程序数据解密工具类
 */
@Slf4j
public class WeChatDecryptUtil {

    static {
        // 添加BouncyCastle作为安全提供者
        Security.addProvider(new BouncyCastleProvider());
    }

    /**
     * 解密微信小程序数据
     *
     * @param encryptedData 加密的数据
     * @param sessionKey    会话密钥
     * @param iv            初始向量
     * @return 解密后的数据
     */
    public static String decrypt(String encryptedData, String sessionKey, String iv) {
        try {
            // Base64解码
            byte[] dataByte = Base64.getDecoder().decode(encryptedData);
            byte[] keyByte = Base64.getDecoder().decode(sessionKey);
            byte[] ivByte = Base64.getDecoder().decode(iv);

            // 创建AES解密器
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS7Padding", "BC");
            SecretKeySpec spec = new SecretKeySpec(keyByte, "AES");
            IvParameterSpec ivSpec = new IvParameterSpec(ivByte);
            cipher.init(Cipher.DECRYPT_MODE, spec, ivSpec);

            // 解密
            byte[] resultByte = cipher.doFinal(dataByte);
            String result = new String(resultByte, StandardCharsets.UTF_8);

            log.info("微信数据解密成功");
            return result;
        } catch (Exception e) {
            log.error("微信数据解密失败", e);
            throw new RuntimeException("数据解密失败", e);
        }
    }

    /**
     * 解密手机号信息
     *
     * @param encryptedData 加密的数据
     * @param sessionKey    会话密钥
     * @param iv            初始向量
     * @return 手机号信息
     */
    public static JSONObject decryptPhoneNumber(String encryptedData, String sessionKey, String iv) {
        try {
            String decryptedData = decrypt(encryptedData, sessionKey, iv);
            JSONObject phoneInfo = JSON.parseObject(decryptedData);
            
            log.info("手机号解密成功: {}", phoneInfo.getString("phoneNumber"));
            return phoneInfo;
        } catch (Exception e) {
            log.error("手机号解密失败", e);
            throw new RuntimeException("手机号解密失败", e);
        }
    }

    /**
     * 解密用户信息
     *
     * @param encryptedData 加密的数据
     * @param sessionKey    会话密钥
     * @param iv            初始向量
     * @return 用户信息
     */
    public static JSONObject decryptUserInfo(String encryptedData, String sessionKey, String iv) {
        try {
            String decryptedData = decrypt(encryptedData, sessionKey, iv);
            JSONObject userInfo = JSON.parseObject(decryptedData);
            
            log.info("用户信息解密成功: {}", userInfo.getString("nickName"));
            return userInfo;
        } catch (Exception e) {
            log.error("用户信息解密失败", e);
            throw new RuntimeException("用户信息解密失败", e);
        }
    }
}
