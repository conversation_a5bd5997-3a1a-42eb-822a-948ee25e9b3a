package com.flower.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 文件上传配置类
 */
@Configuration
@ConfigurationProperties(prefix = "file.upload")
public class FileUploadConfig {

    /**
     * 轮播图上传路径
     */
    private String swiperPath = "src/main/resources/image/swiper/";

    /**
     * 图片访问URL前缀
     */
    private String imageUrlPrefix = "/image/swiper/";

    /**
     * 服务器基础URL
     */
    private String serverBaseUrl = "https://mxm.qiangs.xyz/api";
    // private String serverBaseUrl = "http://localhost:8080/api";
    /**
     * 允许的图片格式
     */
    private String[] allowedImageTypes = {"jpg", "jpeg", "png", "gif"};

    /**
     * 最大文件大小（字节）
     */
    private long maxFileSize = 2 * 1024 * 1024; // 2MB

    public String getSwiperPath() {
        return swiperPath;
    }

    public void setSwiperPath(String swiperPath) {
        this.swiperPath = swiperPath;
    }

    public String getImageUrlPrefix() {
        return imageUrlPrefix;
    }

    public void setImageUrlPrefix(String imageUrlPrefix) {
        this.imageUrlPrefix = imageUrlPrefix;
    }

    public String[] getAllowedImageTypes() {
        return allowedImageTypes;
    }

    public void setAllowedImageTypes(String[] allowedImageTypes) {
        this.allowedImageTypes = allowedImageTypes;
    }

    public long getMaxFileSize() {
        return maxFileSize;
    }

    public void setMaxFileSize(long maxFileSize) {
        this.maxFileSize = maxFileSize;
    }

    public String getServerBaseUrl() {
        return serverBaseUrl;
    }

    public void setServerBaseUrl(String serverBaseUrl) {
        this.serverBaseUrl = serverBaseUrl;
    }

    /**
     * 获取完整的上传路径
     */
    public String getFullSwiperPath() {
        return System.getProperty("user.dir") + "/" + swiperPath;
    }

    /**
     * 获取完整的图片访问URL
     */
    public String getFullImageUrl(String filename) {
        return serverBaseUrl + imageUrlPrefix + filename;
    }
}
