package com.flower.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import java.io.File;

/**
 * Web配置类
 */
@Configuration
public class WebConfig implements WebMvcConfigurer {

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 获取JAR文件所在目录
        String jarPath = System.getProperty("user.dir");
        String userImagePath = "file:" + jarPath + "/image/user-image/";

        System.out.println("配置用户头像静态资源路径: " + userImagePath);

        // 配置商品图片静态资源访问
        String shopImagePath = "file:" + jarPath + "/image/shop-image/";
        System.out.println("配置商品图片静态资源路径: " + shopImagePath);

        // 配置管理员头像静态资源访问
        String adminImagePath = "file:" + jarPath + "/image/admin-image/";
        System.out.println("配置管理员头像静态资源路径: " + adminImagePath);

        // 配置轮播图静态资源访问
        String swiperImagePath = "file:" + jarPath + "/image/swiper/";
        System.out.println("配置轮播图静态资源路径: " + swiperImagePath);

        // 配置旧的商品图片路径格式（兼容数据库中的旧路径）
        registry.addResourceHandler("/image/flower/**")
                .addResourceLocations(shopImagePath)
                .addResourceLocations("classpath:/image/shop-image/");

        // 为每个资源处理器添加classpath作为备选路径
        registry.addResourceHandler("/image/user-image/**")
                .addResourceLocations(userImagePath)
                .addResourceLocations("classpath:/image/user-image/");

        registry.addResourceHandler("/image/shop-image/**")
                .addResourceLocations(shopImagePath)
                .addResourceLocations("classpath:/image/shop-image/");

        registry.addResourceHandler("/image/admin-image/**")
                .addResourceLocations(adminImagePath)
                .addResourceLocations("classpath:/image/admin-image/");

        registry.addResourceHandler("/image/swiper/**")
                .addResourceLocations(swiperImagePath)
                .addResourceLocations("classpath:/image/swiper/");
    }
}
