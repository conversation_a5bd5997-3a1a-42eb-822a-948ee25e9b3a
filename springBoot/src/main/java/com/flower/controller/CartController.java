package com.flower.controller;

import com.flower.common.Result;
import com.flower.entity.CartItem;
import com.flower.service.CartService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 购物车控制器
 */
@Slf4j
@RestController
@RequestMapping("/cart")
@CrossOrigin(origins = "*")
public class CartController {

    @Autowired
    private CartService cartService;

    /**
     * 添加到购物车
     */
    @PostMapping("/add")
    public Result<CartItem> addToCart(@RequestBody Map<String, Object> params) {
        try {
            Long userId = Long.valueOf(params.get("userId").toString());
            Long flowerId = Long.valueOf(params.get("flowerId").toString());
            Integer quantity = Integer.valueOf(params.get("quantity").toString());

            CartItem cartItem = cartService.addToCart(userId, flowerId, quantity);
            return Result.success("添加到购物车成功", cartItem);
        } catch (Exception e) {
            log.error("添加到购物车失败", e);
            return Result.error("添加到购物车失败");
        }
    }

    /**
     * 更新购物车商品
     */
    @PutMapping("/update")
    public Result<CartItem> updateCartItem(@RequestBody Map<String, Object> params) {
        try {
            Long userId = Long.valueOf(params.get("userId").toString());
            Long flowerId = Long.valueOf(params.get("flowerId").toString());
            Integer quantity = Integer.valueOf(params.get("quantity").toString());

            CartItem cartItem = cartService.updateCartItem(userId, flowerId, quantity);
            return Result.success("购物车更新成功", cartItem);
        } catch (Exception e) {
            log.error("更新购物车失败", e);
            return Result.error("更新购物车失败");
        }
    }

    /**
     * 从购物车移除商品
     */
    @DeleteMapping("/remove")
    public Result<Boolean> removeFromCart(@RequestBody Map<String, Object> params) {
        try {
            Long userId = Long.valueOf(params.get("userId").toString());
            Long flowerId = Long.valueOf(params.get("flowerId").toString());

            Boolean success = cartService.removeFromCart(userId, flowerId);
            return Result.success("从购物车移除成功", success);
        } catch (Exception e) {
            log.error("从购物车移除失败", e);
            return Result.error("从购物车移除失败");
        }
    }

    /**
     * 获取用户购物车列表
     */
    @GetMapping("/list/{userId}")
    public Result<List<CartItem>> getCartItems(@PathVariable Long userId) {
        try {
            List<CartItem> cartItems = cartService.getUserCartItems(userId);
            return Result.success(cartItems);
        } catch (Exception e) {
            log.error("获取购物车列表失败", e);
            return Result.error("获取购物车列表失败");
        }
    }

    /**
     * 批量从购物车移除商品
     */
    @DeleteMapping("/batch-remove")
    public Result<Integer> batchRemoveFromCart(@RequestBody Map<String, Object> params) {
        try {
            Long userId = Long.valueOf(params.get("userId").toString());
            @SuppressWarnings("unchecked")
            List<Object> flowerIdObjects = (List<Object>) params.get("flowerIds");

            // 转换为Long列表
            List<Long> flowerIds = flowerIdObjects.stream()
                    .map(obj -> Long.valueOf(obj.toString()))
                    .collect(java.util.stream.Collectors.toList());

            Integer removedCount = cartService.batchRemoveFromCart(userId, flowerIds);
            return Result.success("批量移除成功", removedCount);
        } catch (Exception e) {
            log.error("批量移除失败", e);
            return Result.error("批量移除失败");
        }
    }

    /**
     * 清空购物车
     */
    @DeleteMapping("/clear/{userId}")
    public Result<Boolean> clearCart(@PathVariable Long userId) {
        try {
            Boolean success = cartService.clearCart(userId);
            return Result.success("购物车清空成功", success);
        } catch (Exception e) {
            log.error("清空购物车失败", e);
            return Result.error("清空购物车失败");
        }
    }

    /**
     * 获取购物车商品数量
     */
    @GetMapping("/count/{userId}")
    public Result<Integer> getCartItemCount(@PathVariable Long userId) {
        try {
            Integer count = cartService.getCartItemCount(userId);
            return Result.success(count);
        } catch (Exception e) {
            log.error("获取购物车数量失败", e);
            return Result.error("获取购物车数量失败");
        }
    }
}
