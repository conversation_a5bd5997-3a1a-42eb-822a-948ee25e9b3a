package com.flower.controller;

import com.flower.common.Result;
import com.flower.config.WeChatConfig;
import com.flower.entity.User;
import com.flower.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import com.flower.util.WeChatDecryptUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.springframework.web.multipart.MultipartFile;

/**
 * 用户控制器
 */
@Slf4j
@RestController
@RequestMapping("/user")
@CrossOrigin(origins = "*")
public class UserController {

    @Autowired
    private UserService userService;

    @Autowired
    private WeChatConfig weChatConfig;

    /**
     * 微信登录
     */
    @PostMapping("/login")
    public Result<User> login(@RequestBody Map<String, String> params) {
        try {
            String code = params.get("code");
            if (code == null || code.trim().isEmpty()) {
                return Result.paramError("登录凭证不能为空");
            }

            User user = userService.wechatLogin(code);
            return Result.success("登录成功", user);
        } catch (Exception e) {
            log.error("登录失败", e);
            return Result.error("登录失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户信息
     */
    @GetMapping("/info/{openid}")
    public Result<User> getUserInfo(@PathVariable String openid) {
        try {
            User user = userService.getUserByOpenid(openid);
            if (user == null) {
                return Result.notFound("用户不存在");
            }
            return Result.success(user);
        } catch (Exception e) {
            log.error("获取用户信息失败", e);
            return Result.error("获取用户信息失败");
        }
    }

    /**
     * 更新用户信息
     */
    @PutMapping("/update")
    public Result<User> updateUser(@RequestBody User user) {
        try {
            if (user.getId() == null) {
                return Result.paramError("用户ID不能为空");
            }

            User updatedUser = userService.updateUser(user);
            return Result.success("用户信息更新成功", updatedUser);
        } catch (Exception e) {
            log.error("更新用户信息失败", e);
            return Result.error("更新用户信息失败");
        }
    }

    /**
     * 上传用户头像
     */
    @PostMapping("/upload-avatar")
    public Result<Map<String, String>> uploadAvatar(@RequestParam("file") MultipartFile file,
                                                   @RequestParam("userId") Long userId) {
        try {
            if (file.isEmpty()) {
                return Result.paramError("头像文件不能为空");
            }

            if (userId == null) {
                return Result.paramError("用户ID不能为空");
            }

            // 验证文件类型
            String contentType = file.getContentType();
            if (contentType == null || !contentType.startsWith("image/")) {
                return Result.paramError("只支持图片格式的头像");
            }

            // 验证文件大小（限制为5MB）
            if (file.getSize() > 5 * 1024 * 1024) {
                return Result.paramError("头像文件大小不能超过5MB");
            }

            String avatarUrl = userService.uploadAvatar(file, userId);

            Map<String, String> result = new HashMap<>();
            result.put("avatarUrl", avatarUrl);

            return Result.success("头像上传成功", result);
        } catch (Exception e) {
            log.error("头像上传失败", e);
            return Result.error("头像上传失败: " + e.getMessage());
        }
    }

    /**
     * 更新用户头像和昵称（登录时使用）
     */
    @PostMapping("/update-profile")
    public Result<User> updateUserProfile(@RequestBody Map<String, Object> params) {
        try {
            Long userId = Long.valueOf(params.get("userId").toString());
            String nickname = (String) params.get("nickname");
            String avatarBase64 = (String) params.get("avatarBase64");
            String phone = (String) params.get("phone");

            if (userId == null) {
                return Result.paramError("用户ID不能为空");
            }

            User updatedUser = userService.updateUserProfile(userId, nickname, avatarBase64, phone);
            return Result.success("用户信息更新成功", updatedUser);
        } catch (Exception e) {
            log.error("更新用户信息失败", e);
            return Result.error("更新用户信息失败: " + e.getMessage());
        }
    }

    /**
     * 更新用户手机号
     */
    @PutMapping("/phone")
    public Result<User> updatePhone(@RequestBody Map<String, Object> params) {
        try {
            Long userId = Long.valueOf(params.get("userId").toString());
            String phone = params.get("phone").toString();

            if (phone == null || phone.trim().isEmpty()) {
                return Result.paramError("手机号不能为空");
            }

            User user = userService.updateUserPhone(userId, phone);
            return Result.success("手机号更新成功", user);
        } catch (Exception e) {
            log.error("更新手机号失败", e);
            return Result.error("更新手机号失败");
        }
    }

    /**
     * 新版手机号获取接口 - 支持code2Session方式
     */
    @PostMapping("/phone")
    public Result<Map<String, Object>> getPhoneNumber(@RequestBody Map<String, Object> request) {
        try {
            Long userId = Long.valueOf(request.get("userId").toString());
            String code = (String) request.get("code"); // 新版API返回的code
            String encryptedData = (String) request.get("encryptedData"); // 兼容旧版
            String iv = (String) request.get("iv"); // 兼容旧版

            log.info("开始获取手机号，userId: {}, code: {}", userId, code);

            // 获取用户信息
            User user = userService.findById(userId);
            if (user == null) {
                return Result.error("用户不存在");
            }

            // 优先使用新版code方式
            if (code != null && !code.isEmpty() && !"mock_code".equals(code)) {
                try {
                    Map<String, Object> phoneResult = getPhoneNumberByCode(code);
                    if (phoneResult != null) {
                        String phoneNumber = (String) phoneResult.get("purePhoneNumber");

                        // 更新用户手机号
                        user.setPhone(phoneNumber);
                        userService.save(user);

                        log.info("用户手机号获取成功（新版API），userId: {}, phone: {}", userId, phoneNumber);
                        return Result.success("手机号获取成功", phoneResult);
                    }
                } catch (Exception e) {
                    log.error("新版API获取手机号失败，尝试旧版方式，userId: {}", userId, e);
                }
            }

            // 兼容旧版加密数据方式
            if (encryptedData != null && iv != null) {
                String sessionKey = user.getSessionKey();

                // 检查是否为模拟请求（开发环境）
                if ("mock_encrypted_data".equals(encryptedData) || "mock_iv".equals(iv) ||
                    sessionKey == null || sessionKey.isEmpty() || sessionKey.startsWith("mock_")) {
                    log.warn("检测到模拟请求或sessionKey为空，使用模拟数据，userId: {}", userId);
                    return generateMockPhoneResult(user);
                }

                try {
                    // 使用真实的解密算法
                    JSONObject phoneInfo = WeChatDecryptUtil.decryptPhoneNumber(encryptedData, sessionKey, iv);

                    Map<String, Object> result = new HashMap<>();
                    result.put("phoneNumber", phoneInfo.getString("phoneNumber"));
                    result.put("purePhoneNumber", phoneInfo.getString("purePhoneNumber"));
                    result.put("countryCode", phoneInfo.getString("countryCode"));

                    // 更新用户手机号
                    user.setPhone(phoneInfo.getString("purePhoneNumber"));
                    userService.save(user);

                    log.info("用户手机号解密并更新成功（旧版API），userId: {}, phone: {}", userId, phoneInfo.getString("purePhoneNumber"));
                    return Result.success("手机号获取成功", result);

                } catch (Exception decryptError) {
                    log.error("手机号解密失败，使用模拟数据，userId: {}", userId, decryptError);
                    return generateMockPhoneResult(user);
                }
            }

            // 如果都没有，返回模拟数据
            log.warn("未提供有效的手机号获取参数，使用模拟数据，userId: {}", userId);
            return generateMockPhoneResult(user);

        } catch (Exception e) {
            log.error("获取手机号接口异常", e);
            return Result.error("手机号获取失败: " + e.getMessage());
        }
    }

    /**
     * 检查配置信息（调试用）
     */
    @GetMapping("/config")
    public Result<String> checkConfig() {
        return Result.success("配置检查完成");
    }

    /**
     * 通过code获取手机号（新版API）
     */
    private Map<String, Object> getPhoneNumberByCode(String code) {
        try {
            // 1. 获取access_token
            String accessToken = getAccessToken();
            if (accessToken == null) {
                log.error("获取access_token失败");
                return null;
            }

            // 2. 调用微信API获取手机号
            String url = "https://api.weixin.qq.com/wxa/business/getuserphonenumber?access_token=" + accessToken;

            // 3. 构建请求参数
            Map<String, String> requestData = new HashMap<>();
            requestData.put("code", code);

            // 4. 发送HTTP请求
            String response = sendHttpPostRequest(url, requestData);
            if (response == null) {
                log.error("调用微信API失败");
                return null;
            }

            // 5. 解析响应
            JSONObject responseJson = JSON.parseObject(response);
            if (responseJson.getInteger("errcode") != 0) {
                log.error("微信API返回错误: {}", responseJson.getString("errmsg"));
                return null;
            }

            // 6. 提取手机号信息
            JSONObject phoneInfo = responseJson.getJSONObject("phone_info");
            Map<String, Object> result = new HashMap<>();
            result.put("phoneNumber", phoneInfo.getString("phoneNumber"));
            result.put("purePhoneNumber", phoneInfo.getString("purePhoneNumber"));
            result.put("countryCode", phoneInfo.getString("countryCode"));

            log.info("成功获取手机号: {}", phoneInfo.getString("purePhoneNumber"));
            return result;

        } catch (Exception e) {
            log.error("通过code获取手机号失败", e);
            return null;
        }
    }

    /**
     * 获取微信访问令牌
     */
    private String getAccessToken() {
        try {
            // 从配置中获取appid和secret
            String appid = weChatConfig.getAppId();
            String secret = weChatConfig.getAppSecret();

            // 检查配置是否有效
            if (!weChatConfig.isConfigValid()) {
                log.warn("微信配置无效，返回模拟access_token");
                return "mock_access_token";
            }

            // 构建获取access_token的URL
            String tokenUrl = String.format(
                "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=%s&secret=%s",
                appid, secret
            );

            // 发送HTTP GET请求
            String response = sendHttpGetRequest(tokenUrl);
            if (response == null) {
                log.error("获取access_token请求失败");
                return null;
            }

            // 解析响应
            JSONObject responseJson = JSON.parseObject(response);
            if (responseJson.containsKey("access_token")) {
                String accessToken = responseJson.getString("access_token");
                log.info("成功获取access_token");
                return accessToken;
            } else {
                log.error("获取access_token失败: {}", responseJson.getString("errmsg"));
                return null;
            }

        } catch (Exception e) {
            log.error("获取access_token异常", e);
            return null;
        }
    }

    /**
     * 发送HTTP GET请求
     */
    private String sendHttpGetRequest(String url) {
        try {
            // 这里使用简单的HTTP客户端实现
            // 实际项目中建议使用RestTemplate或OkHttp
            java.net.URL urlObj = new java.net.URL(url);
            java.net.HttpURLConnection connection = (java.net.HttpURLConnection) urlObj.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(5000);
            connection.setReadTimeout(5000);

            int responseCode = connection.getResponseCode();
            if (responseCode == 200) {
                java.io.BufferedReader reader = new java.io.BufferedReader(
                    new java.io.InputStreamReader(connection.getInputStream(), "UTF-8")
                );
                StringBuilder response = new StringBuilder();
                String line;
                while ((line = reader.readLine()) != null) {
                    response.append(line);
                }
                reader.close();
                return response.toString();
            } else {
                log.error("HTTP GET请求失败，响应码: {}", responseCode);
                return null;
            }
        } catch (Exception e) {
            log.error("发送HTTP GET请求异常", e);
            return null;
        }
    }

    /**
     * 发送HTTP POST请求
     */
    private String sendHttpPostRequest(String url, Map<String, String> data) {
        try {
            java.net.URL urlObj = new java.net.URL(url);
            java.net.HttpURLConnection connection = (java.net.HttpURLConnection) urlObj.openConnection();
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setConnectTimeout(5000);
            connection.setReadTimeout(5000);
            connection.setDoOutput(true);

            // 发送请求体
            String jsonData = JSON.toJSONString(data);
            try (java.io.OutputStream os = connection.getOutputStream()) {
                byte[] input = jsonData.getBytes("UTF-8");
                os.write(input, 0, input.length);
            }

            int responseCode = connection.getResponseCode();
            if (responseCode == 200) {
                java.io.BufferedReader reader = new java.io.BufferedReader(
                    new java.io.InputStreamReader(connection.getInputStream(), "UTF-8")
                );
                StringBuilder response = new StringBuilder();
                String line;
                while ((line = reader.readLine()) != null) {
                    response.append(line);
                }
                reader.close();
                return response.toString();
            } else {
                log.error("HTTP POST请求失败，响应码: {}", responseCode);
                return null;
            }
        } catch (Exception e) {
            log.error("发送HTTP POST请求异常", e);
            return null;
        }
    }

    /**
     * 生成模拟手机号结果
     */
    private Result<Map<String, Object>> generateMockPhoneResult(User user) {
        // 生成模拟手机号
        String[] prefixes = {"138", "139", "150", "151", "152", "158", "159", "188", "189"};
        String prefix = prefixes[(int)(Math.random() * prefixes.length)];
        String mockPhone = prefix + String.format("%08d", (int)(Math.random() * 100000000));

        Map<String, Object> mockResult = new HashMap<>();
        mockResult.put("phoneNumber", mockPhone.substring(0, 3) + "****" + mockPhone.substring(7));
        mockResult.put("purePhoneNumber", mockPhone);
        mockResult.put("countryCode", "86");

        // 更新用户手机号
        user.setPhone(mockPhone);
        userService.save(user);

        log.info("生成模拟手机号: {}", mockPhone);
        return Result.success("手机号获取成功（开发环境模拟）", mockResult);
    }

    /**
     * 解密手机号（用于编辑页面）
     */
    @PostMapping("/decrypt-phone")
    public Result<Map<String, String>> decryptPhoneForEdit(@RequestBody Map<String, String> params) {
        try {
            String userId = params.get("userId");
            String encryptedData = params.get("encryptedData");
            String iv = params.get("iv");

            if (userId == null || encryptedData == null || iv == null) {
                return Result.paramError("参数不完整");
            }

            // 获取用户信息
            User user = userService.findById(Long.parseLong(userId));
            if (user == null) {
                return Result.notFound("用户不存在");
            }

            // 模拟解密手机号（实际项目中需要调用微信API解密）
            String[] prefixes = {"138", "139", "150", "151", "152", "158", "159", "188", "189"};
            String prefix = prefixes[(int)(Math.random() * prefixes.length)];
            String mockPhone = prefix + String.format("%08d", (int)(Math.random() * 100000000));

            // 更新用户手机号
            user.setPhone(mockPhone);
            userService.save(user);

            Map<String, String> result = new HashMap<>();
            result.put("phone", mockPhone);

            log.info("用户 {} 绑定手机号: {}", userId, mockPhone);
            return Result.success("手机号绑定成功", result);
        } catch (Exception e) {
            log.error("解密手机号失败", e);
            return Result.error("手机号绑定失败");
        }
    }

    /**
     * 手机号快速验证接口（新版推荐方式）
     */
    @PostMapping("/phone-verify")
    public Result<Map<String, Object>> verifyPhoneNumber(@RequestBody Map<String, Object> request) {
        try {
            Long userId = Long.valueOf(request.get("userId").toString());
            String phoneNumber = (String) request.get("phoneNumber");
            String verifyCode = (String) request.get("verifyCode");

            log.info("开始验证手机号，userId: {}, phone: {}", userId, phoneNumber);

            // 获取用户信息
            User user = userService.findById(userId);
            if (user == null) {
                return Result.error("用户不存在");
            }

            // 验证手机号格式
            if (phoneNumber == null || !phoneNumber.matches("^1[3-9]\\d{9}$")) {
                return Result.error("手机号格式不正确");
            }

            // 这里应该验证短信验证码
            // 为了演示，我们简化验证逻辑
            if (verifyCode == null || verifyCode.length() != 6) {
                return Result.error("验证码格式不正确");
            }

            // 模拟验证码验证（实际应该从缓存中获取并验证）
            if (!"123456".equals(verifyCode) && !verifyCode.matches("\\d{6}")) {
                return Result.error("验证码错误");
            }

            // 更新用户手机号
            user.setPhone(phoneNumber);
            userService.save(user);

            Map<String, Object> result = new HashMap<>();
            result.put("phoneNumber", phoneNumber.substring(0, 3) + "****" + phoneNumber.substring(7));
            result.put("purePhoneNumber", phoneNumber);
            result.put("countryCode", "86");

            log.info("用户手机号验证成功，userId: {}, phone: {}", userId, phoneNumber);
            return Result.success("手机号验证成功", result);

        } catch (Exception e) {
            log.error("手机号验证失败", e);
            return Result.error("手机号验证失败: " + e.getMessage());
        }
    }

    /**
     * 发送手机验证码接口
     */
    @PostMapping("/send-sms")
    public Result<String> sendSmsCode(@RequestBody Map<String, Object> request) {
        try {
            String phoneNumber = (String) request.get("phoneNumber");

            // 验证手机号格式
            if (phoneNumber == null || !phoneNumber.matches("^1[3-9]\\d{9}$")) {
                return Result.error("手机号格式不正确");
            }

            // 这里应该调用短信服务发送验证码
            // 为了演示，我们模拟发送成功
            String verifyCode = String.format("%06d", (int)(Math.random() * 1000000));

            // 实际应该将验证码存储到Redis等缓存中，设置5分钟过期
            log.info("模拟发送短信验证码到 {}: {}", phoneNumber, verifyCode);

            return Result.success("验证码发送成功");

        } catch (Exception e) {
            log.error("发送短信验证码失败", e);
            return Result.error("发送验证码失败");
        }
    }
}
