package com.flower.controller;

import com.flower.common.PageResult;
import com.flower.common.Result;
import com.flower.entity.Order;
import com.flower.entity.OrderItem;
import com.flower.service.OrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 订单控制器
 */
@Slf4j
@RestController
@RequestMapping("/order")
@CrossOrigin(origins = "*")
public class OrderController {

    @Autowired
    private OrderService orderService;

    /**
     * 从购物车创建订单
     */
    @PostMapping("/create/cart")
    public Result<Order> createOrderFromCart(@RequestBody Map<String, Object> params) {
        try {
            Long userId = Long.valueOf(params.get("userId").toString());
            String recipientName = params.get("recipientName").toString();
            String recipientPhone = params.get("recipientPhone").toString();
            String recipientAddress = params.get("recipientAddress").toString();
            String deliveryNotes = (String) params.get("deliveryNotes");
            String remark = (String) params.get("remark");

            Order order = orderService.createOrderFromCart(userId, recipientName, recipientPhone,
                                                         recipientAddress, deliveryNotes, remark);
            return Result.success("订单创建成功", order);
        } catch (Exception e) {
            log.error("从购物车创建订单失败", e);
            return Result.error("创建订单失败: " + e.getMessage());
        }
    }

    /**
     * 从指定商品创建订单
     */
    @PostMapping("/create")
    public Result<Order> createOrder(@RequestBody Map<String, Object> params) {
        try {
            Long userId = Long.valueOf(params.get("userId").toString());
            @SuppressWarnings("unchecked")
            List<Long> flowerIds = (List<Long>) params.get("flowerIds");
            @SuppressWarnings("unchecked")
            List<Integer> quantities = (List<Integer>) params.get("quantities");
            String recipientName = params.get("recipientName").toString();
            String recipientPhone = params.get("recipientPhone").toString();
            String recipientAddress = params.get("recipientAddress").toString();
            String deliveryNotes = (String) params.get("deliveryNotes");
            String remark = (String) params.get("remark");

            Order order = orderService.createOrder(userId, flowerIds, quantities, recipientName,
                                                 recipientPhone, recipientAddress, deliveryNotes, remark);
            return Result.success("订单创建成功", order);
        } catch (Exception e) {
            log.error("创建订单失败", e);
            return Result.error("创建订单失败: " + e.getMessage());
        }
    }

    /**
     * 获取订单详情
     */
    @GetMapping("/detail/{orderId}")
    public Result<Order> getOrderDetail(@PathVariable Long orderId) {
        try {
            Order order = orderService.getOrderById(orderId);
            if (order == null) {
                return Result.notFound("订单不存在");
            }
            return Result.success(order);
        } catch (Exception e) {
            log.error("获取订单详情失败", e);
            return Result.error("获取订单详情失败");
        }
    }

    /**
     * 获取订单商品列表
     */
    @GetMapping("/items/{orderId}")
    public Result<List<OrderItem>> getOrderItems(@PathVariable Long orderId) {
        try {
            List<OrderItem> orderItems = orderService.getOrderItems(orderId);
            return Result.success(orderItems);
        } catch (Exception e) {
            log.error("获取订单商品失败", e);
            return Result.error("获取订单商品失败");
        }
    }

    /**
     * 获取用户订单列表
     */
    @GetMapping("/list/{userId}")
    public Result<PageResult<Order>> getUserOrders(
            @PathVariable Long userId,
            @RequestParam(defaultValue = "1") Long current,
            @RequestParam(defaultValue = "10") Long size,
            @RequestParam(required = false) Integer status) {
        try {
            PageResult<Order> result = orderService.getUserOrders(userId, current, size, status);
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取用户订单失败", e);
            return Result.error("获取用户订单失败");
        }
    }

    /**
     * 取消订单
     */
    @PutMapping("/cancel")
    public Result<Boolean> cancelOrder(@RequestBody Map<String, Object> params) {
        try {
            Long orderId = Long.valueOf(params.get("orderId").toString());
            Long userId = Long.valueOf(params.get("userId").toString());

            Boolean success = orderService.cancelOrder(orderId, userId);
            return Result.success("订单取消成功", success);
        } catch (Exception e) {
            log.error("取消订单失败", e);
            return Result.error("取消订单失败");
        }
    }

    /**
     * 支付订单（模拟支付）
     */
    @PutMapping("/pay")
    public Result<Boolean> payOrder(@RequestBody Map<String, Object> params) {
        try {
            Long orderId = Long.valueOf(params.get("orderId").toString());
            String paymentMethod = params.get("paymentMethod").toString();

            Boolean success = orderService.payOrder(orderId, paymentMethod);
            return Result.success("支付成功", success);
        } catch (Exception e) {
            log.error("支付失败", e);
            return Result.error("支付失败: " + e.getMessage());
        }
    }

    /**
     * 创建直接下单订单
     */
    @PostMapping("/create-direct")
    public Result<Order> createDirectOrder(@RequestBody Map<String, Object> request) {
        try {
            Long userId = Long.valueOf(request.get("userId").toString());
            Integer deliveryType = Integer.valueOf(request.get("deliveryType").toString());

            // 解析商品信息
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> items = (List<Map<String, Object>>) request.get("items");
            List<Long> flowerIds = items.stream()
                    .map(item -> Long.valueOf(item.get("flowerId").toString()))
                    .collect(java.util.stream.Collectors.toList());
            List<Integer> quantities = items.stream()
                    .map(item -> Integer.valueOf(item.get("quantity").toString()))
                    .collect(java.util.stream.Collectors.toList());

            String recipientName = (String) request.get("recipientName");
            String recipientPhone = (String) request.get("recipientPhone");
            String recipientAddress = (String) request.get("recipientAddress");
            String deliveryTime = (String) request.get("deliveryTime");
            String pickupName = (String) request.get("pickupName");
            String pickupPhone = (String) request.get("pickupPhone");
            String pickupTime = (String) request.get("pickupTime");
            String remark = (String) request.get("remark");
            String backupAddress = (String) request.get("backupAddress");

            Order order = orderService.createDirectOrder(userId, flowerIds, quantities, deliveryType,
                    recipientName, recipientPhone, recipientAddress, deliveryTime, pickupName, pickupPhone,
                    pickupTime, remark, backupAddress);

            return Result.success("下单成功", order);
        } catch (Exception e) {
            log.error("创建直接订单失败", e);
            return Result.error("下单失败: " + e.getMessage());
        }
    }

    /**
     * 确认收货
     */
    @PutMapping("/confirm-receipt")
    public Result<Boolean> confirmReceipt(@RequestBody Map<String, Object> params) {
        try {
            Long orderId = Long.valueOf(params.get("orderId").toString());
            Long userId = Long.valueOf(params.get("userId").toString());

            Order order = orderService.getOrderById(orderId);
            if (order == null) {
                return Result.notFound("订单不存在");
            }

            if (!order.getUserId().equals(userId)) {
                return Result.error("无权操作此订单");
            }

            if (order.getDeliveryStatus() != 2) {
                return Result.error("订单未送达，无法确认收货");
            }

            if (order.getPaymentStatus() != 1) {
                return Result.error("订单未支付，无法确认收货");
            }

            // 更新订单状态为已完成
            orderService.updateOrderStatus(orderId, 4);
            return Result.success("确认收货成功", true);
        } catch (Exception e) {
            log.error("确认收货失败", e);
            return Result.error("确认收货失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户订单统计
     */
    @GetMapping("/stats/{userId}")
    public Result<Map<String, Integer>> getOrderStats(@PathVariable Long userId) {
        try {
            Map<String, Integer> stats = orderService.getOrderStatsByUserId(userId);
            return Result.success("获取订单统计成功", stats);
        } catch (Exception e) {
            log.error("获取订单统计失败", e);
            return Result.error("获取订单统计失败: " + e.getMessage());
        }
    }
}
