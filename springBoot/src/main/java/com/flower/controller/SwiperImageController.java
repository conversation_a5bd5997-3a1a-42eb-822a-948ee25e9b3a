package com.flower.controller;

import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * 轮播图图片访问控制器
 */
@RestController
@RequestMapping("/image/swiper")
@CrossOrigin
public class SwiperImageController {

    /**
     * 获取轮播图图片
     */
    @GetMapping("/{filename:.+}")
    public ResponseEntity<Resource> getSwiperImage(@PathVariable String filename) {
        try {
            // 获取JAR文件所在目录
            String projectPath = System.getProperty("user.dir");
            String imagePath = projectPath + "/image/swiper/" + filename;
            
            System.out.println("请求轮播图: " + filename);
            System.out.println("完整路径: " + imagePath);
            
            File imageFile = new File(imagePath);
            
            if (!imageFile.exists()) {
                System.out.println("文件不存在: " + imagePath);
                return ResponseEntity.notFound().build();
            }
            
            // 创建资源
            Resource resource = new FileSystemResource(imageFile);
            
            // 确定内容类型
            String contentType = "application/octet-stream";
            try {
                Path path = Paths.get(imagePath);
                contentType = Files.probeContentType(path);
                if (contentType == null) {
                    // 根据文件扩展名确定类型
                    String extension = filename.substring(filename.lastIndexOf(".") + 1).toLowerCase();
                    switch (extension) {
                        case "jpg":
                        case "jpeg":
                            contentType = "image/jpeg";
                            break;
                        case "png":
                            contentType = "image/png";
                            break;
                        case "gif":
                            contentType = "image/gif";
                            break;
                        default:
                            contentType = "application/octet-stream";
                    }
                }
            } catch (Exception e) {
                System.out.println("无法确定内容类型: " + e.getMessage());
            }
            
            System.out.println("返回图片，内容类型: " + contentType);
            
            return ResponseEntity.ok()
                    .contentType(MediaType.parseMediaType(contentType))
                    .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"" + filename + "\"")
                    .body(resource);
                    
        } catch (Exception e) {
            System.err.println("获取轮播图失败: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.internalServerError().build();
        }
    }
}
