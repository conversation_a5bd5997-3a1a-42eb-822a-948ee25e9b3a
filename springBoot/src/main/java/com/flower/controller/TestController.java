package com.flower.controller;

import com.flower.common.Result;
import com.flower.entity.User;
import com.flower.entity.Flower;
import com.flower.entity.UserFavorite;
import com.flower.entity.CartItem;
import com.flower.mapper.UserMapper;
import com.flower.mapper.FlowerMapper;
import com.flower.mapper.UserFavoriteMapper;
import com.flower.mapper.CartItemMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 测试控制器 - 用于调试数据库连接和基本功能
 */
@Slf4j
@RestController
@RequestMapping("/test")
@CrossOrigin(origins = "*")
public class TestController {

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private FlowerMapper flowerMapper;

    @Autowired
    private UserFavoriteMapper userFavoriteMapper;

    @Autowired
    private CartItemMapper cartItemMapper;

    /**
     * 测试数据库连接
     */
    @GetMapping("/db")
    public Result<Map<String, Object>> testDatabase() {
        try {
            Map<String, Object> result = new HashMap<>();
            
            // 测试用户表
            long userCount = userMapper.selectCount(null);
            result.put("userCount", userCount);
            
            // 测试花卉表
            long flowerCount = flowerMapper.selectCount(null);
            result.put("flowerCount", flowerCount);
            
            // 测试收藏表
            long favoriteCount = userFavoriteMapper.selectCount(null);
            result.put("favoriteCount", favoriteCount);
            
            // 测试购物车表
            long cartCount = cartItemMapper.selectCount(null);
            result.put("cartCount", cartCount);
            
            result.put("message", "数据库连接正常");
            
            return Result.success("数据库测试成功", result);
        } catch (Exception e) {
            log.error("数据库测试失败", e);
            return Result.error("数据库测试失败: " + e.getMessage());
        }
    }

    /**
     * 测试用户查询
     */
    @GetMapping("/users")
    public Result<List<User>> testUsers() {
        try {
            List<User> users = userMapper.selectList(null);
            return Result.success("用户查询成功", users);
        } catch (Exception e) {
            log.error("用户查询失败", e);
            return Result.error("用户查询失败: " + e.getMessage());
        }
    }

    /**
     * 测试花卉查询
     */
    @GetMapping("/flowers")
    public Result<List<Flower>> testFlowers() {
        try {
            List<Flower> flowers = flowerMapper.selectList(null);
            return Result.success("花卉查询成功", flowers);
        } catch (Exception e) {
            log.error("花卉查询失败", e);
            return Result.error("花卉查询失败: " + e.getMessage());
        }
    }

    /**
     * 测试收藏功能
     */
    @PostMapping("/favorite")
    public Result<String> testFavorite(@RequestParam Long userId, @RequestParam Long flowerId) {
        try {
            // 检查用户是否存在
            User user = userMapper.selectById(userId);
            if (user == null) {
                return Result.error("用户不存在");
            }

            // 检查花卉是否存在
            Flower flower = flowerMapper.selectById(flowerId);
            if (flower == null) {
                return Result.error("花卉不存在");
            }

            // 测试插入收藏
            UserFavorite favorite = new UserFavorite();
            favorite.setUserId(userId);
            favorite.setFlowerId(flowerId);
            favorite.setCreatedAt(java.time.LocalDateTime.now());

            int result = userFavoriteMapper.insert(favorite);
            if (result > 0) {
                return Result.success("收藏测试成功");
            } else {
                return Result.error("收藏插入失败");
            }
        } catch (Exception e) {
            log.error("收藏测试失败", e);
            return Result.error("收藏测试失败: " + e.getMessage());
        }
    }

    /**
     * 测试购物车功能
     */
    @PostMapping("/cart")
    public Result<String> testCart(@RequestParam Long userId, @RequestParam Long flowerId, @RequestParam Integer quantity) {
        try {
            // 检查用户是否存在
            User user = userMapper.selectById(userId);
            if (user == null) {
                return Result.error("用户不存在");
            }

            // 检查花卉是否存在
            Flower flower = flowerMapper.selectById(flowerId);
            if (flower == null) {
                return Result.error("花卉不存在");
            }

            // 测试插入购物车
            CartItem cartItem = new CartItem();
            cartItem.setUserId(userId);
            cartItem.setFlowerId(flowerId);
            cartItem.setQuantity(quantity);
            cartItem.setCreatedAt(java.time.LocalDateTime.now());
            cartItem.setUpdatedAt(java.time.LocalDateTime.now());

            int result = cartItemMapper.insert(cartItem);
            if (result > 0) {
                return Result.success("购物车测试成功");
            } else {
                return Result.error("购物车插入失败");
            }
        } catch (Exception e) {
            log.error("购物车测试失败", e);
            return Result.error("购物车测试失败: " + e.getMessage());
        }
    }
}
