package com.flower.controller;

import com.flower.common.Result;
import com.flower.entity.UserFavorite;
import com.flower.entity.Flower;
import com.flower.service.FavoriteService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 收藏控制器
 */
@Slf4j
@RestController
@RequestMapping("/favorite")
@CrossOrigin(origins = "*")
public class FavoriteController {

    @Autowired
    private FavoriteService favoriteService;

    /**
     * 添加到收藏
     */
    @PostMapping("/add")
    public Result<UserFavorite> addToFavorites(@RequestBody Map<String, Object> params) {
        try {
            Long userId = Long.valueOf(params.get("userId").toString());
            Long flowerId = Long.valueOf(params.get("flowerId").toString());

            UserFavorite favorite = favoriteService.addToFavorites(userId, flowerId);
            if (favorite == null) {
                return Result.error("已在收藏列表中");
            }
            return Result.success("添加收藏成功", favorite);
        } catch (Exception e) {
            log.error("添加收藏失败", e);
            return Result.error("添加收藏失败");
        }
    }

    /**
     * 取消收藏
     */
    @DeleteMapping("/remove")
    public Result<Boolean> removeFromFavorites(@RequestBody Map<String, Object> params) {
        try {
            Long userId = Long.valueOf(params.get("userId").toString());
            Long flowerId = Long.valueOf(params.get("flowerId").toString());

            Boolean success = favoriteService.removeFromFavorites(userId, flowerId);
            return Result.success("取消收藏成功", success);
        } catch (Exception e) {
            log.error("取消收藏失败", e);
            return Result.error("取消收藏失败");
        }
    }

    /**
     * 检查是否已收藏
     */
    @GetMapping("/check")
    public Result<Boolean> isFavorite(@RequestParam Long userId, @RequestParam Long flowerId) {
        try {
            Boolean isFavorite = favoriteService.isFavorite(userId, flowerId);
            return Result.success(isFavorite);
        } catch (Exception e) {
            log.error("检查收藏状态失败", e);
            return Result.error("检查收藏状态失败");
        }
    }

    /**
     * 切换收藏状态
     */
    @PostMapping("/toggle")
    public Result<Boolean> toggleFavorite(@RequestBody Map<String, Object> params) {
        try {
            Long userId = Long.valueOf(params.get("userId").toString());
            Long flowerId = Long.valueOf(params.get("flowerId").toString());

            Boolean isFavorite = favoriteService.toggleFavorite(userId, flowerId);
            String message = isFavorite ? "添加收藏成功" : "取消收藏成功";
            return Result.success(message, isFavorite);
        } catch (Exception e) {
            log.error("切换收藏状态失败", e);
            return Result.error("操作失败");
        }
    }

    /**
     * 获取用户收藏列表
     */
    @GetMapping("/list/{userId}")
    public Result<List<UserFavorite>> getUserFavorites(@PathVariable Long userId) {
        try {
            List<UserFavorite> favorites = favoriteService.getUserFavorites(userId);
            return Result.success(favorites);
        } catch (Exception e) {
            log.error("获取收藏列表失败", e);
            return Result.error("获取收藏列表失败");
        }
    }

    /**
     * 获取用户收藏的花卉详情列表
     */
    @GetMapping("/flowers/{userId}")
    public Result<List<Flower>> getUserFavoriteFlowers(@PathVariable Long userId) {
        try {
            List<Flower> flowers = favoriteService.getUserFavoriteFlowers(userId);
            return Result.success(flowers);
        } catch (Exception e) {
            log.error("获取收藏花卉列表失败", e);
            return Result.error("获取收藏花卉列表失败");
        }
    }

    /**
     * 获取用户收藏数量
     */
    @GetMapping("/count/{userId}")
    public Result<Integer> getFavoriteCount(@PathVariable Long userId) {
        try {
            Integer count = favoriteService.getFavoriteCount(userId);
            return Result.success(count);
        } catch (Exception e) {
            log.error("获取收藏数量失败", e);
            return Result.error("获取收藏数量失败");
        }
    }
}
