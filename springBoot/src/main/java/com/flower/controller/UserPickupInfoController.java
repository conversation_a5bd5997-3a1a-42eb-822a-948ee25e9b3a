package com.flower.controller;

import com.flower.common.Result;
import com.flower.entity.UserPickupInfo;
import com.flower.service.UserPickupInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 用户自取信息控制器
 */
@Slf4j
@RestController
@RequestMapping("/pickup-info")
@CrossOrigin(origins = "*")
public class UserPickupInfoController {

    @Autowired
    private UserPickupInfoService userPickupInfoService;

    /**
     * 获取用户自取信息
     */
    @GetMapping("/{userId}")
    public Result<UserPickupInfo> getUserPickupInfo(@PathVariable Long userId) {
        try {
            UserPickupInfo pickupInfo = userPickupInfoService.getByUserId(userId);
            return Result.success(pickupInfo);
        } catch (Exception e) {
            log.error("获取自取信息失败", e);
            return Result.error("获取自取信息失败");
        }
    }

    /**
     * 保存或更新自取信息
     */
    @PostMapping("/save")
    public Result<UserPickupInfo> savePickupInfo(@RequestBody UserPickupInfo pickupInfo) {
        try {
            UserPickupInfo saved = userPickupInfoService.saveOrUpdate(pickupInfo);
            return Result.success("保存成功", saved);
        } catch (Exception e) {
            log.error("保存自取信息失败", e);
            return Result.error("保存失败: " + e.getMessage());
        }
    }

    /**
     * 删除自取信息
     */
    @DeleteMapping("/{userId}")
    public Result<String> deletePickupInfo(@PathVariable Long userId) {
        try {
            userPickupInfoService.deleteByUserId(userId);
            return Result.success("删除成功");
        } catch (Exception e) {
            log.error("删除自取信息失败", e);
            return Result.error("删除失败");
        }
    }
}
