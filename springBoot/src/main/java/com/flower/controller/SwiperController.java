package com.flower.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.flower.common.Result;
import com.flower.entity.Swiper;
import com.flower.service.SwiperService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 轮播图控制器
 */
@RestController
@RequestMapping("/swiper")
@CrossOrigin
public class SwiperController {
    
    @Autowired
    private SwiperService swiperService;
    
    /**
     * 分页查询轮播图
     */
    @GetMapping("/page")
    public Result<Page<Swiper>> getPage(
            @RequestParam(defaultValue = "1") int pageNum,
            @RequestParam(defaultValue = "10") int pageSize,
            @RequestParam(required = false) String title,
            @RequestParam(required = false) Integer status) {
        
        Page<Swiper> page = swiperService.getPage(pageNum, pageSize, title, status);
        return Result.success(page);
    }
    
    /**
     * 获取启用状态的轮播图（小程序端使用）
     */
    @GetMapping("/active")
    public Result<List<Swiper>> getActiveSwipers() {
        List<Swiper> swipers = swiperService.getActiveSwipers();
        return Result.success(swipers);
    }
    
    /**
     * 根据ID查询轮播图
     */
    @GetMapping("/{id}")
    public Result<Swiper> getById(@PathVariable Integer id) {
        Swiper swiper = swiperService.getById(id);
        return Result.success(swiper);
    }
    
    /**
     * 新增轮播图
     */
    @PostMapping
    public Result<String> save(@RequestBody Swiper swiper) {
        swiper.setCreateTime(LocalDateTime.now());
        swiper.setUpdateTime(LocalDateTime.now());
        if (swiper.getStatus() == null) {
            swiper.setStatus(1);
        }
        if (swiper.getSortOrder() == null) {
            swiper.setSortOrder(0);
        }
        
        boolean success = swiperService.save(swiper);
        return success ? Result.success("新增成功") : Result.error("新增失败");
    }
    
    /**
     * 更新轮播图
     */
    @PutMapping
    public Result<String> update(@RequestBody Swiper swiper) {
        swiper.setUpdateTime(LocalDateTime.now());
        boolean success = swiperService.updateById(swiper);
        return success ? Result.success("更新成功") : Result.error("更新失败");
    }
    
    /**
     * 删除轮播图
     */
    @DeleteMapping("/{id}")
    public Result<String> delete(@PathVariable Integer id) {
        boolean success = swiperService.removeById(id);
        return success ? Result.success("删除成功") : Result.error("删除失败");
    }
    
    /**
     * 批量删除轮播图
     */
    @DeleteMapping("/batch")
    public Result<String> deleteBatch(@RequestBody List<Integer> ids) {
        boolean success = swiperService.removeByIds(ids);
        return success ? Result.success("批量删除成功") : Result.error("批量删除失败");
    }
    
    /**
     * 上传轮播图图片
     */
    @PostMapping("/upload")
    public Result<String> uploadImage(@RequestParam("file") MultipartFile file) {
        try {
            String imageUrl = swiperService.uploadImage(file);
            return Result.success(imageUrl);
        } catch (Exception e) {
            return Result.error("图片上传失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新轮播图状态
     */
    @PutMapping("/status")
    public Result<String> updateStatus(@RequestParam Integer id, @RequestParam Integer status) {
        boolean success = swiperService.updateStatus(id, status);
        return success ? Result.success("状态更新成功") : Result.error("状态更新失败");
    }
    
    /**
     * 更新排序顺序
     */
    @PutMapping("/sort")
    public Result<String> updateSortOrder(@RequestParam Integer id, @RequestParam Integer sortOrder) {
        boolean success = swiperService.updateSortOrder(id, sortOrder);
        return success ? Result.success("排序更新成功") : Result.error("排序更新失败");
    }
}
