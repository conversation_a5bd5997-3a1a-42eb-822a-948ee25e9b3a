package com.flower.controller;

import com.flower.common.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 文件上传控制器
 */
@Slf4j
@RestController
@RequestMapping("/admin/upload")
@CrossOrigin(origins = "*")
public class FileUploadController {

    @Value("${server.port:8080}")
    private String serverPort;

    @Value("${server.servlet.context-path:/api}")
    private String contextPath;

    // 商品图片上传路径 - 使用JAR同级目录
    private static final String SHOP_IMAGE_PATH = System.getProperty("user.dir") + "/image/shop-image/";
    
    // 允许的图片格式
    private static final Set<String> ALLOWED_EXTENSIONS = Set.of("jpg", "jpeg", "png", "gif", "webp");
    
    // 最大文件大小 (5MB)
    private static final long MAX_FILE_SIZE = 5 * 1024 * 1024;

    /**
     * 上传商品图片 (兼容旧接口)
     */
    @PostMapping("/image")
    public Result<Map<String, Object>> uploadImage(@RequestParam("file") MultipartFile file) {
        return uploadShopImage(file);
    }

    /**
     * 上传商品图片
     */
    @PostMapping("/shop-image")
    public Result<Map<String, Object>> uploadShopImage(@RequestParam("file") MultipartFile file) {
        try {
            // 验证文件
            String validationError = validateFile(file);
            if (validationError != null) {
                return Result.paramError(validationError);
            }

            // 生成文件名
            String originalFilename = file.getOriginalFilename();
            String extension = getFileExtension(originalFilename);
            String newFileName = generateFileName(extension);

            // 确保目录存在
            File uploadDir = new File(SHOP_IMAGE_PATH);
            if (!uploadDir.exists()) {
                boolean created = uploadDir.mkdirs();
                if (!created) {
                    log.error("创建上传目录失败: {}", SHOP_IMAGE_PATH);
                    return Result.error("创建上传目录失败");
                }
            }

            // 保存文件
            File destFile = new File(uploadDir, newFileName);
            file.transferTo(destFile);

            // 生成完整的访问URL
            String imageUrl = generateImageUrl(newFileName);

            // 返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("fileName", newFileName);
            result.put("originalName", originalFilename);
            result.put("url", imageUrl);
            result.put("size", file.getSize());
            result.put("uploadTime", LocalDateTime.now());

            log.info("商品图片上传成功: {} -> {}", originalFilename, imageUrl);
            return Result.success("图片上传成功", result);

        } catch (IOException e) {
            log.error("图片上传失败", e);
            return Result.error("图片上传失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("图片上传异常", e);
            return Result.error("图片上传异常: " + e.getMessage());
        }
    }

    /**
     * 批量上传商品图片
     */
    @PostMapping("/shop-images")
    public Result<List<Map<String, Object>>> uploadShopImages(@RequestParam("files") MultipartFile[] files) {
        if (files == null || files.length == 0) {
            return Result.paramError("请选择要上传的图片");
        }

        if (files.length > 10) {
            return Result.paramError("一次最多只能上传10张图片");
        }

        List<Map<String, Object>> results = new ArrayList<>();
        List<String> errors = new ArrayList<>();

        for (int i = 0; i < files.length; i++) {
            MultipartFile file = files[i];
            try {
                // 验证文件
                String validationError = validateFile(file);
                if (validationError != null) {
                    errors.add("第" + (i + 1) + "张图片: " + validationError);
                    continue;
                }

                // 生成文件名
                String originalFilename = file.getOriginalFilename();
                String extension = getFileExtension(originalFilename);
                String newFileName = generateFileName(extension);

                // 确保目录存在
                File uploadDir = new File(SHOP_IMAGE_PATH);
                if (!uploadDir.exists()) {
                    uploadDir.mkdirs();
                }

                // 保存文件
                File destFile = new File(uploadDir, newFileName);
                file.transferTo(destFile);

                // 生成完整的访问URL
                String imageUrl = generateImageUrl(newFileName);

                // 添加到结果列表
                Map<String, Object> result = new HashMap<>();
                result.put("fileName", newFileName);
                result.put("originalName", originalFilename);
                result.put("url", imageUrl);
                result.put("size", file.getSize());
                result.put("uploadTime", LocalDateTime.now());

                results.add(result);

            } catch (Exception e) {
                log.error("第{}张图片上传失败", i + 1, e);
                errors.add("第" + (i + 1) + "张图片上传失败: " + e.getMessage());
            }
        }

        if (!errors.isEmpty()) {
            String errorMessage = String.join("; ", errors);
            if (results.isEmpty()) {
                return Result.error("所有图片上传失败: " + errorMessage);
            } else {
                log.warn("部分图片上传失败: {}", errorMessage);
            }
        }

        String message = String.format("成功上传 %d 张图片", results.size());
        if (!errors.isEmpty()) {
            message += String.format("，%d 张失败", errors.size());
        }

        return Result.success(message, results);
    }

    /**
     * 删除商品图片
     */
    @DeleteMapping("/shop-image")
    public Result<String> deleteShopImage(@RequestParam("fileName") String fileName) {
        try {
            if (fileName == null || fileName.trim().isEmpty()) {
                return Result.paramError("文件名不能为空");
            }

            // 安全检查：防止路径遍历攻击
            if (fileName.contains("..") || fileName.contains("/") || fileName.contains("\\")) {
                return Result.paramError("文件名格式不正确");
            }

            File file = new File(SHOP_IMAGE_PATH + fileName);
            if (!file.exists()) {
                return Result.notFound("图片文件不存在");
            }

            boolean deleted = file.delete();
            if (deleted) {
                log.info("商品图片删除成功: {}", fileName);
                return Result.success("图片删除成功");
            } else {
                log.error("商品图片删除失败: {}", fileName);
                return Result.error("图片删除失败");
            }

        } catch (Exception e) {
            log.error("删除图片异常", e);
            return Result.error("删除图片异常: " + e.getMessage());
        }
    }

    /**
     * 验证上传的文件
     */
    private String validateFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            return "请选择要上传的图片";
        }

        // 检查文件大小
        if (file.getSize() > MAX_FILE_SIZE) {
            return "图片大小不能超过5MB";
        }

        // 检查文件扩展名
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || originalFilename.trim().isEmpty()) {
            return "文件名不能为空";
        }

        String extension = getFileExtension(originalFilename);
        if (!ALLOWED_EXTENSIONS.contains(extension.toLowerCase())) {
            return "只支持 jpg、jpeg、png、gif、webp 格式的图片";
        }

        // 检查MIME类型
        String contentType = file.getContentType();
        if (contentType == null || !contentType.startsWith("image/")) {
            return "请上传图片文件";
        }

        return null; // 验证通过
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String filename) {
        if (filename == null || filename.isEmpty()) {
            return "";
        }
        int lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex == -1 || lastDotIndex == filename.length() - 1) {
            return "";
        }
        return filename.substring(lastDotIndex + 1);
    }

    /**
     * 生成唯一的文件名
     */
    private String generateFileName(String extension) {
        // 使用时间戳 + UUID + 扩展名生成唯一文件名
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        String uuid = UUID.randomUUID().toString().replace("-", "").substring(0, 8);
        return String.format("shop_%s_%s.%s", timestamp, uuid, extension);
    }

    /**
     * 生成完整的图片访问URL
     */
    private String generateImageUrl(String fileName) {
        // 生成完整的HTTPS URL - 使用生产环境域名
        return String.format("https://mxm.qiangs.xyz/api/image/shop-image/%s", fileName);
    }
}
