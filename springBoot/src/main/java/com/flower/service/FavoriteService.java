package com.flower.service;

import com.flower.entity.UserFavorite;
import com.flower.entity.Flower;

import java.util.List;

/**
 * 收藏服务接口
 */
public interface FavoriteService {

    /**
     * 添加到收藏
     * @param userId 用户ID
     * @param flowerId 花卉ID
     * @return 收藏记录
     */
    UserFavorite addToFavorites(Long userId, Long flowerId);

    /**
     * 取消收藏
     * @param userId 用户ID
     * @param flowerId 花卉ID
     * @return 成功标志
     */
    Boolean removeFromFavorites(Long userId, Long flowerId);

    /**
     * 切换收藏状态
     * @param userId 用户ID
     * @param flowerId 花卉ID
     * @return 操作后的收藏状态
     */
    Boolean toggleFavorite(Long userId, Long flowerId);

    /**
     * 检查是否已收藏
     * @param userId 用户ID
     * @param flowerId 花卉ID
     * @return 是否收藏标志
     */
    Boolean isFavorite(Long userId, Long flowerId);

    /**
     * 获取用户收藏列表
     * @param userId 用户ID
     * @return 收藏列表
     */
    List<UserFavorite> getUserFavorites(Long userId);

    /**
     * 获取用户收藏的花卉详情列表
     * @param userId 用户ID
     * @return 收藏的花卉列表
     */
    List<Flower> getUserFavoriteFlowers(Long userId);

    /**
     * 获取用户收藏数量
     * @param userId 用户ID
     * @return 收藏数量
     */
    Integer getFavoriteCount(Long userId);
}
