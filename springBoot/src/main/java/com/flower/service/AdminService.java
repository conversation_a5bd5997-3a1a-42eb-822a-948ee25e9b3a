package com.flower.service;

import com.flower.entity.AdminUser;

/**
 * 管理员服务接口
 */
public interface AdminService {
    
    /**
     * 管理员登录
     * @param username 用户名
     * @param password 密码
     * @return 管理员信息
     */
    AdminUser login(String username, String password);
    
    /**
     * 验证token
     * @param token JWT token
     * @return 管理员信息
     */
    AdminUser verifyToken(String token);
    
    /**
     * 生成JWT token
     * @param adminUser 管理员信息
     * @return JWT token
     */
    String generateToken(AdminUser adminUser);
    
    /**
     * 更新最后登录信息
     * @param adminId 管理员ID
     * @param ipAddress IP地址
     */
    void updateLastLogin(Long adminId, String ipAddress);
    
    /**
     * 记录操作日志
     * @param adminId 管理员ID
     * @param adminUsername 管理员用户名
     * @param action 操作类型
     * @param resource 操作资源
     * @param resourceId 资源ID
     * @param description 操作描述
     * @param ipAddress IP地址
     * @param userAgent 用户代理
     */
    void logAction(Long adminId, String adminUsername, String action, String resource, 
                   String resourceId, String description, String ipAddress, String userAgent);
}
