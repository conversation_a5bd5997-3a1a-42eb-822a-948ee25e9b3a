package com.flower.service.impl;

import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.flower.entity.User;
import com.flower.mapper.UserMapper;
import com.flower.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.Base64;
import java.util.UUID;

/**
 * 用户服务实现类
 */
@Slf4j
@Service
public class UserServiceImpl implements UserService {

    @Autowired
    private UserMapper userMapper;

    @Value("${wechat.miniprogram.app-id}")
    private String appId;

    @Value("${wechat.miniprogram.app-secret}")
    private String appSecret;

    @Value("${app.server.image-url-prefix}")
    private String imageUrlPrefix;

    @Override
    public User wechatLogin(String code) {
        log.info("开始微信登录，code: {}, appId: {}", code, appId);

        // 检查是否为开发环境的模拟登录
        if ("your_app_id_here".equals(appId) || "your_app_secret_here".equals(appSecret)) {
            log.warn("检测到开发环境配置，使用模拟登录");
            return mockWechatLogin(code);
        }

        // 构建微信API请求URL
        String url = String.format(
            "https://api.weixin.qq.com/sns/jscode2session?appid=%s&secret=%s&js_code=%s&grant_type=authorization_code",
            appId, appSecret, code
        );

        try {
            log.info("调用微信API: {}", url);
            String response = HttpUtil.get(url);
            JSONObject jsonObject = JSONUtil.parseObj(response);

            if (jsonObject.containsKey("errcode")) {
                log.error("微信登录错误: {}", response);
                log.warn("微信登录失败，尝试使用模拟登录");
                return mockWechatLogin(code);
            }

            String openid = jsonObject.getStr("openid");
            String unionid = jsonObject.getStr("unionid");
            String sessionKey = jsonObject.getStr("session_key");

            return createOrUpdateUser(openid, unionid, sessionKey);

        } catch (Exception e) {
            log.error("微信登录异常，使用模拟登录", e);
            return mockWechatLogin(code);
        }
    }

    /**
     * 模拟微信登录（开发环境使用）
     */
    private User mockWechatLogin(String code) {
        log.info("开始模拟微信登录，code: {}", code);

        // 基于code生成模拟的openid
        String mockOpenid = "mock_openid_" + Math.abs(code.hashCode());
        String mockUnionid = "mock_unionid_" + Math.abs(code.hashCode());

        log.info("模拟登录 - OpenID: {}, UnionID: {}", mockOpenid, mockUnionid);

        try {
            // 模拟sessionKey
            String mockSessionKey = "mock_session_key_" + Math.abs(code.hashCode());
            User user = createOrUpdateUser(mockOpenid, mockUnionid, mockSessionKey);
            log.info("模拟登录成功，用户ID: {}", user.getId());
            return user;
        } catch (Exception e) {
            log.error("模拟登录失败", e);
            throw new RuntimeException("模拟登录失败: " + e.getMessage());
        }
    }

    /**
     * 创建或更新用户
     */
    private User createOrUpdateUser(String openid, String unionid, String sessionKey) {
        // 检查用户是否存在
        User user = getUserByOpenid(openid);
        if (user == null) {
            // 创建新用户
            user = new User();
            user.setOpenid(openid);
            user.setUnionid(unionid);
            user.setSessionKey(sessionKey);
            user.setNickname("微信用户" + System.currentTimeMillis() % 10000);
            user.setStatus(1);
            user.setCreatedAt(LocalDateTime.now());
            user.setUpdatedAt(LocalDateTime.now());
            userMapper.insert(user);
            log.info("创建新用户: {}", openid);
        } else {
            // 更新sessionKey和最后登录时间
            user.setSessionKey(sessionKey);
            user.setUpdatedAt(LocalDateTime.now());
            userMapper.updateById(user);
            log.info("用户登录: {}", openid);
        }

        return user;
    }

    @Override
    public User getUserByOpenid(String openid) {
        try {
            // 检查参数
            if (openid == null || openid.trim().isEmpty()) {
                throw new IllegalArgumentException("OpenID不能为空");
            }

            LambdaQueryWrapper<User> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(User::getOpenid, openid);
            wrapper.eq(User::getStatus, 1);
            return userMapper.selectOne(wrapper);
        } catch (Exception e) {
            log.error("根据OpenID获取用户失败: {}", openid, e);
            throw new RuntimeException("获取用户信息失败: " + e.getMessage(), e);
        }
    }

    @Override
    public User updateUser(User user) {
        try {
            // 检查参数
            if (user == null || user.getId() == null) {
                throw new IllegalArgumentException("用户信息或用户ID不能为空");
            }

            user.setUpdatedAt(LocalDateTime.now());
            int result = userMapper.updateById(user);
            if (result > 0) {
                return userMapper.selectById(user.getId());
            } else {
                throw new RuntimeException("更新用户信息失败，用户不存在");
            }
        } catch (Exception e) {
            log.error("更新用户信息失败: {}", user != null ? user.getId() : "null", e);
            throw new RuntimeException("更新用户信息失败: " + e.getMessage(), e);
        }
    }

    @Override
    public User updateUserPhone(Long userId, String phone) {
        User user = userMapper.selectById(userId);
        if (user != null) {
            user.setPhone(phone);
            user.setUpdatedAt(LocalDateTime.now());
            userMapper.updateById(user);
        }
        return user;
    }

    @Override
    public User findById(Long userId) {
        return userMapper.selectById(userId);
    }

    @Override
    public User save(User user) {
        if (user.getId() == null) {
            user.setCreatedAt(LocalDateTime.now());
            user.setUpdatedAt(LocalDateTime.now());
            userMapper.insert(user);
        } else {
            user.setUpdatedAt(LocalDateTime.now());
            userMapper.updateById(user);
        }
        return user;
    }

    @Override
    public String uploadAvatar(MultipartFile file, Long userId) {
        try {
            // 创建用户头像目录 - 支持生产环境
            String uploadDir = getUploadDirectory();
            File dir = new File(uploadDir);
            if (!dir.exists()) {
                log.info("目录不存在，尝试创建: {}", uploadDir);
                boolean created = dir.mkdirs();
                if (!created) {
                    // 尝试获取更详细的错误信息
                    File parentDir = dir.getParentFile();
                    log.error("创建目录失败 - 目录: {}", uploadDir);
                    log.error("父目录存在: {}", parentDir != null && parentDir.exists());
                    log.error("父目录可写: {}", parentDir != null && parentDir.canWrite());
                    log.error("当前用户: {}", System.getProperty("user.name"));
                    throw new RuntimeException("无法创建上传目录: " + uploadDir +
                        " (父目录存在: " + (parentDir != null && parentDir.exists()) +
                        ", 父目录可写: " + (parentDir != null && parentDir.canWrite()) + ")");
                }
                log.info("目录创建成功: {}", uploadDir);
            } else {
                log.info("目录已存在: {}", uploadDir);
            }

            // 生成唯一文件名
            String originalFilename = file.getOriginalFilename();
            String extension = originalFilename != null ?
                originalFilename.substring(originalFilename.lastIndexOf(".")) : ".jpg";
            String filename = "avatar_" + userId + "_" + System.currentTimeMillis() + extension;

            // 保存文件
            Path filePath = Paths.get(uploadDir, filename);
            Files.copy(file.getInputStream(), filePath);

            // 构建访问URL - 返回完整的URL
            String relativePath = "/image/user-image/" + filename;
            String fullAvatarUrl = imageUrlPrefix + relativePath;

            // 更新用户头像URL
            User user = userMapper.selectById(userId);
            if (user != null) {
                // 删除旧头像文件（如果存在）
                deleteOldAvatar(user.getAvatarUrl(), uploadDir);

                user.setAvatarUrl(fullAvatarUrl);
                user.setUpdatedAt(LocalDateTime.now());
                userMapper.updateById(user);
            }

            log.info("用户头像上传成功，userId: {}, avatarUrl: {}", userId, fullAvatarUrl);
            return fullAvatarUrl;

        } catch (IOException e) {
            log.error("头像上传失败，userId: {}", userId, e);
            throw new RuntimeException("头像上传失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("头像上传失败，userId: {}", userId, e);
            throw new RuntimeException("头像上传失败: " + e.getMessage());
        }
    }

    @Override
    public User updateUserProfile(Long userId, String nickname, String avatarBase64, String phone) {
        try {
            User user = userMapper.selectById(userId);
            if (user == null) {
                throw new RuntimeException("用户不存在");
            }

            // 更新昵称
            if (nickname != null && !nickname.trim().isEmpty()) {
                user.setNickname(nickname.trim());
            }

            // 更新手机号
            if (phone != null && !phone.trim().isEmpty()) {
                // 验证手机号格式
                if (!phone.matches("^1[3-9]\\d{9}$")) {
                    throw new RuntimeException("手机号格式不正确");
                }
                user.setPhone(phone.trim());
            }

            // 处理头像Base64数据
            if (avatarBase64 != null && !avatarBase64.trim().isEmpty()) {
                String avatarUrl = saveBase64Avatar(avatarBase64, userId);
                user.setAvatarUrl(avatarUrl);
            }

            user.setUpdatedAt(LocalDateTime.now());
            userMapper.updateById(user);

            log.info("用户信息更新成功，userId: {}, nickname: {}, phone: {}", userId, nickname, phone);
            return user;

        } catch (Exception e) {
            log.error("更新用户信息失败，userId: {}", userId, e);
            throw new RuntimeException("更新用户信息失败: " + e.getMessage());
        }
    }

    /**
     * 保存Base64格式的头像
     */
    private String saveBase64Avatar(String avatarBase64, Long userId) {
        try {
            // 创建用户头像目录 - 支持生产环境
            String uploadDir = getUploadDirectory();
            File dir = new File(uploadDir);
            if (!dir.exists()) {
                log.info("Base64头像目录不存在，尝试创建: {}", uploadDir);
                boolean created = dir.mkdirs();
                if (!created) {
                    File parentDir = dir.getParentFile();
                    log.error("Base64头像目录创建失败 - 目录: {}", uploadDir);
                    log.error("父目录存在: {}", parentDir != null && parentDir.exists());
                    log.error("父目录可写: {}", parentDir != null && parentDir.canWrite());
                    throw new RuntimeException("无法创建上传目录: " + uploadDir +
                        " (父目录存在: " + (parentDir != null && parentDir.exists()) +
                        ", 父目录可写: " + (parentDir != null && parentDir.canWrite()) + ")");
                }
                log.info("Base64头像目录创建成功: {}", uploadDir);
            }

            // 解析Base64数据
            String base64Data = avatarBase64;
            if (avatarBase64.contains(",")) {
                base64Data = avatarBase64.split(",")[1];
            }

            byte[] imageBytes = Base64.getDecoder().decode(base64Data);

            // 生成文件名
            String filename = "avatar_" + userId + "_" + System.currentTimeMillis() + ".jpg";
            Path filePath = Paths.get(uploadDir, filename);

            // 删除旧头像文件
            User existingUser = userMapper.selectById(userId);
            if (existingUser != null) {
                deleteOldAvatar(existingUser.getAvatarUrl(), uploadDir);
            }

            // 保存新头像
            Files.write(filePath, imageBytes);

            // 返回完整的URL
            String relativePath = "/image/user-image/" + filename;
            String fullAvatarUrl = imageUrlPrefix + relativePath;
            log.info("Base64头像保存成功，userId: {}, avatarUrl: {}", userId, fullAvatarUrl);
            return fullAvatarUrl;

        } catch (Exception e) {
            log.error("保存Base64头像失败，userId: {}", userId, e);
            throw new RuntimeException("保存头像失败: " + e.getMessage());
        }
    }

    /**
     * 获取上传目录路径
     */
    private String getUploadDirectory() {
        // 统一使用相对于JAR文件的路径，适用于开发和生产环境
        String jarPath = System.getProperty("user.dir");
        String uploadDir = jarPath + "/image/user-image";

        log.info("JAR文件路径: {}", jarPath);
        log.info("上传目录路径: {}", uploadDir);

        // 检查目录权限
        File dir = new File(uploadDir);
        log.info("目录是否存在: {}", dir.exists());
        log.info("目录是否可写: {}", dir.canWrite());
        log.info("目录是否可读: {}", dir.canRead());

        return uploadDir;
    }

    /**
     * 删除旧头像文件
     */
    private void deleteOldAvatar(String avatarUrl, String uploadDir) {
        if (avatarUrl != null && avatarUrl.contains("/image/user-image/")) {
            try {
                String oldFilename = avatarUrl.substring(avatarUrl.lastIndexOf("/") + 1);
                File oldFile = new File(uploadDir, oldFilename);
                if (oldFile.exists()) {
                    boolean deleted = oldFile.delete();
                    if (deleted) {
                        log.info("删除旧头像文件成功: {}", oldFilename);
                    } else {
                        log.warn("删除旧头像文件失败: {}", oldFilename);
                    }
                }
            } catch (Exception e) {
                log.warn("删除旧头像文件时出错: {}", e.getMessage());
            }
        }
    }
}
