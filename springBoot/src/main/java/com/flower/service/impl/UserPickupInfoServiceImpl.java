package com.flower.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.flower.entity.UserPickupInfo;
import com.flower.mapper.UserPickupInfoMapper;
import com.flower.service.UserPickupInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * 用户自取信息服务实现类
 */
@Service
public class UserPickupInfoServiceImpl implements UserPickupInfoService {

    @Autowired
    private UserPickupInfoMapper userPickupInfoMapper;

    @Override
    public UserPickupInfo getByUserId(Long userId) {
        LambdaQueryWrapper<UserPickupInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserPickupInfo::getUserId, userId);
        return userPickupInfoMapper.selectOne(wrapper);
    }

    @Override
    public UserPickupInfo saveOrUpdate(UserPickupInfo pickupInfo) {
        if (pickupInfo.getId() == null) {
            // 检查是否已存在
            UserPickupInfo existing = getByUserId(pickupInfo.getUserId());
            if (existing != null) {
                // 更新现有记录
                existing.setPickupName(pickupInfo.getPickupName());
                existing.setPickupPhone(pickupInfo.getPickupPhone());
                existing.setPickupTime(pickupInfo.getPickupTime());
                existing.setRemark(pickupInfo.getRemark());
                existing.setBackupAddress(pickupInfo.getBackupAddress());
                existing.setUpdatedAt(LocalDateTime.now());
                userPickupInfoMapper.updateById(existing);
                return existing;
            } else {
                // 新增记录
                pickupInfo.setCreatedAt(LocalDateTime.now());
                pickupInfo.setUpdatedAt(LocalDateTime.now());
                userPickupInfoMapper.insert(pickupInfo);
                return pickupInfo;
            }
        } else {
            // 更新记录
            pickupInfo.setUpdatedAt(LocalDateTime.now());
            userPickupInfoMapper.updateById(pickupInfo);
            return pickupInfo;
        }
    }

    @Override
    public void deleteByUserId(Long userId) {
        LambdaQueryWrapper<UserPickupInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserPickupInfo::getUserId, userId);
        userPickupInfoMapper.delete(wrapper);
    }
}
