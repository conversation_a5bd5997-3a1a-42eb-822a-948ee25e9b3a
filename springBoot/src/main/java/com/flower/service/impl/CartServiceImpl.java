package com.flower.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.flower.entity.CartItem;
import com.flower.mapper.CartItemMapper;
import com.flower.mapper.UserMapper;
import com.flower.mapper.FlowerMapper;
import com.flower.service.CartService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 购物车服务实现类
 */
@Service
public class CartServiceImpl implements CartService {

    @Autowired
    private CartItemMapper cartItemMapper;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private FlowerMapper flowerMapper;

    @Override
    public CartItem addToCart(Long userId, Long flowerId, Integer quantity) {
        try {
            // 检查参数
            if (userId == null || flowerId == null || quantity == null || quantity <= 0) {
                throw new IllegalArgumentException("参数不能为空且数量必须大于0");
            }

            // 检查用户是否存在
            if (userMapper.selectById(userId) == null) {
                throw new IllegalArgumentException("用户不存在，用户ID: " + userId);
            }

            // 检查花卉是否存在
            if (flowerMapper.selectById(flowerId) == null) {
                throw new IllegalArgumentException("花卉不存在，花卉ID: " + flowerId);
            }

            // 检查购物车中是否已存在该商品
            LambdaQueryWrapper<CartItem> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(CartItem::getUserId, userId);
            wrapper.eq(CartItem::getFlowerId, flowerId);

            CartItem existingItem = cartItemMapper.selectOne(wrapper);

            if (existingItem != null) {
                // 更新数量
                existingItem.setQuantity(existingItem.getQuantity() + quantity);
                existingItem.setUpdatedAt(LocalDateTime.now());
                int result = cartItemMapper.updateById(existingItem);
                if (result > 0) {
                    return existingItem;
                } else {
                    throw new RuntimeException("更新购物车失败");
                }
            } else {
                // 创建新的购物车商品
                CartItem cartItem = new CartItem();
                cartItem.setUserId(userId);
                cartItem.setFlowerId(flowerId);
                cartItem.setQuantity(quantity);
                cartItem.setCreatedAt(LocalDateTime.now());
                cartItem.setUpdatedAt(LocalDateTime.now());
                int result = cartItemMapper.insert(cartItem);
                if (result > 0) {
                    return cartItem;
                } else {
                    throw new RuntimeException("添加到购物车失败");
                }
            }
        } catch (Exception e) {
            throw new RuntimeException("添加到购物车失败: " + e.getMessage(), e);
        }
    }

    @Override
    public CartItem updateCartItem(Long userId, Long flowerId, Integer quantity) {
        LambdaQueryWrapper<CartItem> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CartItem::getUserId, userId);
        wrapper.eq(CartItem::getFlowerId, flowerId);
        
        CartItem cartItem = cartItemMapper.selectOne(wrapper);
        if (cartItem != null) {
            cartItem.setQuantity(quantity);
            cartItem.setUpdatedAt(LocalDateTime.now());
            cartItemMapper.updateById(cartItem);
        }
        return cartItem;
    }

    @Override
    public Boolean removeFromCart(Long userId, Long flowerId) {
        LambdaQueryWrapper<CartItem> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CartItem::getUserId, userId);
        wrapper.eq(CartItem::getFlowerId, flowerId);
        
        int result = cartItemMapper.delete(wrapper);
        return result > 0;
    }

    @Override
    public List<CartItem> getUserCartItems(Long userId) {
        LambdaQueryWrapper<CartItem> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CartItem::getUserId, userId);
        wrapper.orderByDesc(CartItem::getCreatedAt);
        return cartItemMapper.selectList(wrapper);
    }

    @Override
    public Boolean clearCart(Long userId) {
        LambdaQueryWrapper<CartItem> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CartItem::getUserId, userId);
        
        int result = cartItemMapper.delete(wrapper);
        return result >= 0;
    }

    @Override
    public Integer batchRemoveFromCart(Long userId, List<Long> flowerIds) {
        if (flowerIds == null || flowerIds.isEmpty()) {
            return 0;
        }

        LambdaQueryWrapper<CartItem> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CartItem::getUserId, userId)
               .in(CartItem::getFlowerId, flowerIds);

        int result = cartItemMapper.delete(wrapper);
        return result;
    }

    @Override
    public Integer getCartItemCount(Long userId) {
        LambdaQueryWrapper<CartItem> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CartItem::getUserId, userId);

        List<CartItem> items = cartItemMapper.selectList(wrapper);
        return items.stream().mapToInt(CartItem::getQuantity).sum();
    }
}
