package com.flower.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.flower.config.FileUploadConfig;
import com.flower.entity.Swiper;
import com.flower.mapper.SwiperMapper;
import com.flower.service.SwiperService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * 轮播图服务实现类
 */
@Service
public class SwiperServiceImpl extends ServiceImpl<SwiperMapper, Swiper> implements SwiperService {

    @Autowired
    private SwiperMapper swiperMapper;

    @Autowired
    private FileUploadConfig fileUploadConfig;

    @Override
    public Page<Swiper> getPage(int pageNum, int pageSize, String title, Integer status) {
        Page<Swiper> page = new Page<>(pageNum, pageSize);
        QueryWrapper<Swiper> queryWrapper = new QueryWrapper<>();

        if (StringUtils.hasText(title)) {
            queryWrapper.like("title", title);
        }
        if (status != null) {
            queryWrapper.eq("status", status);
        }

        queryWrapper.orderByAsc("sort_order").orderByDesc("create_time");

        return this.page(page, queryWrapper);
    }

    @Override
    public List<Swiper> getActiveSwipers() {
        return swiperMapper.getActiveSwipers();
    }

    @Override
    public String uploadImage(MultipartFile file) {
        if (file.isEmpty()) {
            throw new RuntimeException("上传文件不能为空");
        }

        // 获取原始文件名
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null) {
            throw new RuntimeException("文件名不能为空");
        }

        // 检查文件大小
        if (file.getSize() > fileUploadConfig.getMaxFileSize()) {
            throw new RuntimeException("文件大小不能超过2MB");
        }

        // 获取文件扩展名
        String extension = originalFilename.substring(originalFilename.lastIndexOf(".")).toLowerCase();

        // 检查文件类型
        boolean isValidType = false;
        for (String allowedType : fileUploadConfig.getAllowedImageTypes()) {
            if (extension.equals("." + allowedType)) {
                isValidType = true;
                break;
            }
        }
        if (!isValidType) {
            throw new RuntimeException("不支持的文件格式，请上传jpg、png、gif格式的图片");
        }

        // 生成新的文件名
        String newFilename = UUID.randomUUID().toString() + extension;

        // 创建上传目录
        File uploadDir = new File(fileUploadConfig.getFullSwiperPath());
        if (!uploadDir.exists()) {
            uploadDir.mkdirs();
        }

        // 保存文件
        File destFile = new File(uploadDir, newFilename);
        try {
            file.transferTo(destFile);
        } catch (IOException e) {
            throw new RuntimeException("文件上传失败: " + e.getMessage());
        }

        // 返回完整URL
        return fileUploadConfig.getFullImageUrl(newFilename);
    }

    @Override
    public boolean updateStatus(Integer id, Integer status) {
        Swiper swiper = new Swiper();
        swiper.setId(id);
        swiper.setStatus(status);
        swiper.setUpdateTime(LocalDateTime.now());
        return this.updateById(swiper);
    }

    @Override
    public boolean updateSortOrder(Integer id, Integer sortOrder) {
        Swiper swiper = new Swiper();
        swiper.setId(id);
        swiper.setSortOrder(sortOrder);
        swiper.setUpdateTime(LocalDateTime.now());
        return this.updateById(swiper);
    }
}
