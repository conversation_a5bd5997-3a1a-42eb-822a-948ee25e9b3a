package com.flower.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.flower.entity.UserAddress;
import com.flower.mapper.UserAddressMapper;
import com.flower.service.AddressService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 收货地址服务实现类
 */
@Service
public class AddressServiceImpl implements AddressService {

    @Autowired
    private UserAddressMapper addressMapper;

    @Override
    public List<UserAddress> getUserAddresses(Long userId) {
        LambdaQueryWrapper<UserAddress> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserAddress::getUserId, userId);
        wrapper.orderByDesc(UserAddress::getIsDefault);
        wrapper.orderByDesc(UserAddress::getCreatedAt);
        return addressMapper.selectList(wrapper);
    }

    @Override
    public UserAddress getAddressById(Long id) {
        return addressMapper.selectById(id);
    }

    @Override
    @Transactional
    public UserAddress addAddress(UserAddress address) {
        address.setCreatedAt(LocalDateTime.now());
        address.setUpdatedAt(LocalDateTime.now());
        
        // 如果设置为默认地址，先将其他地址设为非默认
        if (address.getIsDefault() != null && address.getIsDefault() == 1) {
            clearDefaultAddress(address.getUserId());
        }
        
        addressMapper.insert(address);
        return address;
    }

    @Override
    @Transactional
    public UserAddress updateAddress(UserAddress address) {
        address.setUpdatedAt(LocalDateTime.now());
        
        // 如果设置为默认地址，先将其他地址设为非默认
        if (address.getIsDefault() != null && address.getIsDefault() == 1) {
            clearDefaultAddress(address.getUserId());
        }
        
        addressMapper.updateById(address);
        return address;
    }

    @Override
    public Boolean deleteAddress(Long id, Long userId) {
        // 验证地址是否属于该用户
        LambdaQueryWrapper<UserAddress> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserAddress::getId, id);
        wrapper.eq(UserAddress::getUserId, userId);
        
        UserAddress address = addressMapper.selectOne(wrapper);
        if (address == null) {
            return false;
        }
        
        int result = addressMapper.deleteById(id);
        return result > 0;
    }

    @Override
    @Transactional
    public Boolean setDefaultAddress(Long id, Long userId) {
        // 验证地址是否属于该用户
        LambdaQueryWrapper<UserAddress> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserAddress::getId, id);
        wrapper.eq(UserAddress::getUserId, userId);
        
        UserAddress address = addressMapper.selectOne(wrapper);
        if (address == null) {
            return false;
        }
        
        // 先将该用户的所有地址设为非默认
        clearDefaultAddress(userId);
        
        // 设置指定地址为默认
        LambdaUpdateWrapper<UserAddress> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(UserAddress::getId, id);
        updateWrapper.set(UserAddress::getIsDefault, 1);
        updateWrapper.set(UserAddress::getUpdatedAt, LocalDateTime.now());
        
        int result = addressMapper.update(null, updateWrapper);
        return result > 0;
    }

    @Override
    public UserAddress getDefaultAddress(Long userId) {
        LambdaQueryWrapper<UserAddress> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserAddress::getUserId, userId);
        wrapper.eq(UserAddress::getIsDefault, 1);
        wrapper.last("LIMIT 1");
        return addressMapper.selectOne(wrapper);
    }

    /**
     * 清除用户的默认地址设置
     */
    private void clearDefaultAddress(Long userId) {
        LambdaUpdateWrapper<UserAddress> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(UserAddress::getUserId, userId);
        updateWrapper.set(UserAddress::getIsDefault, 0);
        updateWrapper.set(UserAddress::getUpdatedAt, LocalDateTime.now());
        addressMapper.update(null, updateWrapper);
    }
}
