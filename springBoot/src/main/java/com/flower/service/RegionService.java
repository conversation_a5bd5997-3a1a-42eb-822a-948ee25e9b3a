package com.flower.service;

import com.flower.entity.Province;
import com.flower.entity.City;
import com.flower.entity.District;
import java.util.List;
import java.util.Map;

/**
 * 地区服务接口
 */
public interface RegionService {
    
    /**
     * 获取所有省份
     */
    List<Province> getAllProvinces();
    
    /**
     * 根据省份代码获取城市列表
     */
    List<City> getCitiesByProvinceCode(String provinceCode);
    
    /**
     * 根据城市代码获取区县列表
     */
    List<District> getDistrictsByCityCode(String cityCode);
    
    /**
     * 获取完整的省市区数据结构（用于前端选择器）
     */
    Map<String, Object> getRegionData();
    
    /**
     * 根据省市区名称获取对应的代码
     */
    Map<String, String> getRegionCodes(String provinceName, String cityName, String districtName);

    /**
     * 清除所有地区相关缓存
     */
    void clearAllRegionCache();
}
