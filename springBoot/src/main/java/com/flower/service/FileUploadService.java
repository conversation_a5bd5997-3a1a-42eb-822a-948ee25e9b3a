package com.flower.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Map;

/**
 * 文件上传服务
 */
@Slf4j
@Service
public class FileUploadService {
    
    /**
     * 上传管理员头像
     * @param file 上传的文件
     * @param adminId 管理员ID
     * @return 上传结果
     */
    public Map<String, String> uploadAdminAvatar(MultipartFile file, Long adminId) throws IOException {
        // 验证文件
        validateImageFile(file);
        
        // 确定上传目录
        String uploadDir = getAdminImageUploadDir();
        createDirectoryIfNotExists(uploadDir);
        
        // 生成文件名
        String originalFilename = file.getOriginalFilename();
        String extension = getFileExtension(originalFilename);
        String filename = "avatar_" + adminId + "_" + System.currentTimeMillis() + extension;
        
        // 保存文件
        Path targetPath = Paths.get(uploadDir, filename);
        log.info("保存头像文件: {}", targetPath.toAbsolutePath());

        try {
            Files.copy(file.getInputStream(), targetPath);
            log.info("头像文件保存成功: {}", targetPath.toAbsolutePath());
        } catch (IOException e) {
            log.error("头像文件保存失败: {}", e.getMessage(), e);
            throw new IOException("文件保存失败: " + e.getMessage());
        }

        // 构建访问URL
        String avatarUrl = "https://mxm.qiangs.xyz/api/image/admin-image/" + filename;
        
        Map<String, String> result = new HashMap<>();
        result.put("url", avatarUrl);
        result.put("filename", filename);
        result.put("path", targetPath.toAbsolutePath().toString());
        
        return result;
    }
    
    /**
     * 获取管理员头像上传目录
     */
    private String getAdminImageUploadDir() {
        String jarPath = System.getProperty("user.dir");
        String uploadDir = jarPath + File.separator + "image" + File.separator + "admin-image";

        log.info("管理员头像上传目录: {}", uploadDir);
        return uploadDir;
    }

    /**
     * 获取管理员头像classpath目录（运行时目录）
     */
    private String getClasspathAdminImageDir() {
        // 对于JAR部署，不需要classpath目录，直接返回主目录
        String jarPath = System.getProperty("user.dir");
        String classpathDir = jarPath + File.separator + "image" + File.separator + "admin-image";

        log.info("管理员头像classpath目录: {}", classpathDir);
        return classpathDir;
    }
    
    /**
     * 创建目录（如果不存在）
     */
    private void createDirectoryIfNotExists(String dirPath) throws IOException {
        Path path = Paths.get(dirPath);
        if (!Files.exists(path)) {
            try {
                Files.createDirectories(path);
                log.info("创建目录成功: {}", path.toAbsolutePath());
            } catch (IOException e) {
                log.error("创建目录失败: {}", e.getMessage(), e);
                throw new IOException("创建上传目录失败: " + e.getMessage());
            }
        }
    }
    
    /**
     * 验证图片文件
     */
    private void validateImageFile(MultipartFile file) throws IOException {
        if (file.isEmpty()) {
            throw new IOException("文件不能为空");
        }
        
        String contentType = file.getContentType();
        if (contentType == null || !contentType.startsWith("image/")) {
            throw new IOException("只能上传图片文件");
        }
        
        // 检查文件大小 (2MB)
        if (file.getSize() > 2 * 1024 * 1024) {
            throw new IOException("文件大小不能超过2MB");
        }
        
        // 检查文件扩展名
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || originalFilename.trim().isEmpty()) {
            throw new IOException("文件名不能为空");
        }
        
        String extension = getFileExtension(originalFilename).toLowerCase();
        if (!extension.matches("\\.(jpg|jpeg|png)$")) {
            throw new IOException("只支持JPG、JPEG、PNG格式的图片");
        }
    }
    
    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String filename) {
        if (filename == null || filename.trim().isEmpty()) {
            return "";
        }
        
        int lastDotIndex = filename.lastIndexOf(".");
        if (lastDotIndex == -1) {
            return "";
        }
        
        return filename.substring(lastDotIndex);
    }
}
