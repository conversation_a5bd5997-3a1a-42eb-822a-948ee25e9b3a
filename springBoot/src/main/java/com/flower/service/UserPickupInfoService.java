package com.flower.service;

import com.flower.entity.UserPickupInfo;

/**
 * 用户自取信息服务接口
 */
public interface UserPickupInfoService {

    /**
     * 根据用户ID获取自取信息
     * @param userId 用户ID
     * @return 自取信息
     */
    UserPickupInfo getByUserId(Long userId);

    /**
     * 保存或更新自取信息
     * @param pickupInfo 自取信息
     * @return 保存后的自取信息
     */
    UserPickupInfo saveOrUpdate(UserPickupInfo pickupInfo);

    /**
     * 删除用户自取信息
     * @param userId 用户ID
     */
    void deleteByUserId(Long userId);
}
