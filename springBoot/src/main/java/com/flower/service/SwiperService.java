package com.flower.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.flower.entity.Swiper;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 轮播图服务接口
 */
public interface SwiperService extends IService<Swiper> {
    
    /**
     * 分页查询轮播图
     */
    Page<Swiper> getPage(int pageNum, int pageSize, String title, Integer status);
    
    /**
     * 获取启用状态的轮播图
     */
    List<Swiper> getActiveSwipers();
    
    /**
     * 上传轮播图图片
     */
    String uploadImage(MultipartFile file);
    
    /**
     * 更新轮播图状态
     */
    boolean updateStatus(Integer id, Integer status);
    
    /**
     * 更新排序顺序
     */
    boolean updateSortOrder(Integer id, Integer sortOrder);
}
