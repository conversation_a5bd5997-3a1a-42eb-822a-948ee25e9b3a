package com.flower.service;

import com.flower.common.PageResult;
import com.flower.entity.Category;
import com.flower.entity.Flower;
import com.flower.vo.FlowerVO;

import java.math.BigDecimal;
import java.util.List;

/**
 * 花卉商品服务接口
 */
public interface FlowerService {

    /**
     * 获取所有分类
     * @return 分类列表
     */
    List<Category> getAllCategories();

    /**
     * 分页获取花卉列表
     * @param current 当前页码
     * @param size 每页大小
     * @param categoryId 分类ID（可选）
     * @param keyword 搜索关键词（可选）
     * @param minPrice 最低价格（可选）
     * @param maxPrice 最高价格（可选）
     * @param excludeFeatured 是否排除精选商品（可选）
     * @return 分页花卉列表
     */
    PageResult<FlowerVO> getFlowersByPage(Long current, Long size, Long categoryId, String keyword,
                                         BigDecimal minPrice, BigDecimal maxPrice, Boolean excludeFeatured);

    /**
     * 根据ID获取花卉
     * @param id 花卉ID
     * @return 花卉信息
     */
    Flower getFlowerById(Long id);

    /**
     * 获取精选花卉
     * @param limit 限制数量
     * @param categoryId 分类ID（可选）
     * @param minPrice 最低价格（可选）
     * @param maxPrice 最高价格（可选）
     * @return 精选花卉列表
     */
    List<Flower> getFeaturedFlowers(Integer limit, Long categoryId, BigDecimal minPrice, BigDecimal maxPrice);

    /**
     * 搜索花卉
     * @param keyword 搜索关键词
     * @param current 当前页码
     * @param size 每页大小
     * @return 搜索结果
     */
    PageResult<FlowerVO> searchFlowers(String keyword, Long current, Long size);

    /**
     * 根据分类获取花卉
     * @param categoryId 分类ID
     * @param current 当前页码
     * @param size 每页大小
     * @return 花卉列表
     */
    PageResult<FlowerVO> getFlowersByCategory(Long categoryId, Long current, Long size);

    /**
     * 获取热门搜索关键词
     * @return 热门关键词列表
     */
    List<String> getHotKeywords();
}
