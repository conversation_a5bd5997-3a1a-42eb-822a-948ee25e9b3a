package com.flower.service;

import com.flower.common.PageResult;
import com.flower.entity.PriceCategory;

import java.util.List;

/**
 * 价格分类服务接口
 */
public interface PriceCategoryService {

    /**
     * 分页获取价格分类列表
     */
    PageResult<PriceCategory> getPriceCategoriesByPage(Long current, Long size, String keyword, Integer status);

    /**
     * 根据ID获取价格分类
     */
    PriceCategory getPriceCategoryById(Long id);

    /**
     * 创建价格分类
     */
    boolean createPriceCategory(PriceCategory priceCategory);

    /**
     * 更新价格分类
     */
    boolean updatePriceCategory(PriceCategory priceCategory);

    /**
     * 删除价格分类
     */
    boolean deletePriceCategory(Long id);

    /**
     * 更新价格分类状态
     */
    boolean updatePriceCategoryStatus(Long id, Integer status);

    /**
     * 获取所有启用的价格分类
     */
    List<PriceCategory> getAllActivePriceCategories();

    /**
     * 根据价格获取对应的价格分类
     */
    PriceCategory getPriceCategoryByPrice(java.math.BigDecimal price);
}
