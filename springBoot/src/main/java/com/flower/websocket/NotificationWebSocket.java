package com.flower.websocket;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.websocket.*;
import javax.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * WebSocket通知服务端点
 */
@Slf4j
@Component
@ServerEndpoint("/ws/notifications")
public class NotificationWebSocket {

    // 静态变量，用来记录当前在线连接数
    private static final AtomicInteger onlineCount = new AtomicInteger(0);
    
    // concurrent包的线程安全Set，用来存放每个客户端对应的WebSocket对象
    private static final ConcurrentHashMap<String, NotificationWebSocket> webSocketMap = new ConcurrentHashMap<>();
    
    // 与某个客户端的连接会话，需要通过它来给客户端发送数据
    private Session session;
    
    // 接收用户ID
    private String userId = "";
    
    private static final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 连接建立成功调用的方法
     */
    @OnOpen
    public void onOpen(Session session) {
        this.session = session;
        this.userId = session.getId(); // 使用session ID作为用户标识
        
        if (webSocketMap.containsKey(userId)) {
            webSocketMap.remove(userId);
            webSocketMap.put(userId, this);
        } else {
            webSocketMap.put(userId, this);
            addOnlineCount();
        }
        
        log.info("用户连接:{}, 当前在线人数为:{}", userId, getOnlineCount());
        
        try {
            sendMessage("连接成功");
        } catch (IOException e) {
            log.error("用户:{}, 网络异常!!!!!!", userId);
        }
    }

    /**
     * 连接关闭调用的方法
     */
    @OnClose
    public void onClose() {
        if (webSocketMap.containsKey(userId)) {
            webSocketMap.remove(userId);
            subOnlineCount();
        }
        log.info("用户退出:{}, 当前在线人数为:{}", userId, getOnlineCount());
    }

    /**
     * 收到客户端消息后调用的方法
     */
    @OnMessage
    public void onMessage(String message, Session session) {
        log.info("用户消息:{}, 报文:{}", userId, message);
        
        // 处理心跳消息
        if ("ping".equals(message)) {
            try {
                sendMessage("pong");
            } catch (IOException e) {
                log.error("发送心跳响应失败", e);
            }
        }
    }

    /**
     * 发生错误时调用
     */
    @OnError
    public void onError(Session session, Throwable error) {
        log.error("用户错误:{}, 原因:{}", userId, error.getMessage());
        error.printStackTrace();
    }

    /**
     * 实现服务器主动推送
     */
    public void sendMessage(String message) throws IOException {
        this.session.getBasicRemote().sendText(message);
    }

    /**
     * 发送自定义消息
     */
    public static void sendInfo(String message, String userId) throws IOException {
        log.info("发送消息到:{}, 报文:{}", userId, message);
        if (userId != null && webSocketMap.containsKey(userId)) {
            webSocketMap.get(userId).sendMessage(message);
        } else {
            log.error("用户:{}, 不在线！", userId);
        }
    }

    /**
     * 群发自定义消息
     */
    public static void sendToAll(String message) {
        log.info("群发消息:{}", message);
        for (NotificationWebSocket item : webSocketMap.values()) {
            try {
                item.sendMessage(message);
            } catch (IOException e) {
                log.error("群发消息失败", e);
            }
        }
    }

    /**
     * 发送新订单通知
     */
    public static void sendNewOrderNotification(Object orderData) {
        try {
            NotificationMessage message = new NotificationMessage();
            message.setType("NEW_ORDER");
            message.setData(orderData);
            message.setTimestamp(System.currentTimeMillis());
            
            String jsonMessage = objectMapper.writeValueAsString(message);
            sendToAll(jsonMessage);
            
            log.info("发送新订单通知: {}", jsonMessage);
        } catch (Exception e) {
            log.error("发送新订单通知失败", e);
        }
    }

    /**
     * 发送订单状态变更通知
     */
    public static void sendOrderStatusChangeNotification(Object orderData) {
        try {
            NotificationMessage message = new NotificationMessage();
            message.setType("ORDER_STATUS_CHANGE");
            message.setData(orderData);
            message.setTimestamp(System.currentTimeMillis());
            
            String jsonMessage = objectMapper.writeValueAsString(message);
            sendToAll(jsonMessage);
            
            log.info("发送订单状态变更通知: {}", jsonMessage);
        } catch (Exception e) {
            log.error("发送订单状态变更通知失败", e);
        }
    }

    /**
     * 发送库存预警通知
     */
    public static void sendLowStockNotification(Object stockData) {
        try {
            NotificationMessage message = new NotificationMessage();
            message.setType("LOW_STOCK");
            message.setData(stockData);
            message.setTimestamp(System.currentTimeMillis());
            
            String jsonMessage = objectMapper.writeValueAsString(message);
            sendToAll(jsonMessage);
            
            log.info("发送库存预警通知: {}", jsonMessage);
        } catch (Exception e) {
            log.error("发送库存预警通知失败", e);
        }
    }

    public static synchronized int getOnlineCount() {
        return onlineCount.get();
    }

    public static synchronized void addOnlineCount() {
        onlineCount.incrementAndGet();
    }

    public static synchronized void subOnlineCount() {
        onlineCount.decrementAndGet();
    }

    /**
     * 通知消息类
     */
    public static class NotificationMessage {
        private String type;
        private Object data;
        private Long timestamp;

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public Object getData() {
            return data;
        }

        public void setData(Object data) {
            this.data = data;
        }

        public Long getTimestamp() {
            return timestamp;
        }

        public void setTimestamp(Long timestamp) {
            this.timestamp = timestamp;
        }
    }
}
