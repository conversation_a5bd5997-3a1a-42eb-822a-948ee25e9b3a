package com.flower.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.flower.entity.Province;
import com.flower.entity.City;
import com.flower.entity.District;
import org.apache.ibatis.annotations.*;
import java.util.List;

/**
 * 地区数据Mapper
 */
@Mapper
public interface RegionMapper extends BaseMapper<Province> {
    
    /**
     * 获取所有省份
     */
    @Select("SELECT * FROM provinces ORDER BY CASE WHEN name = '新疆维吾尔自治区' THEN 0 ELSE 1 END, code")
    List<Province> getAllProvinces();
    
    /**
     * 根据省份代码获取城市列表
     */
    @Select("SELECT * FROM cities WHERE province_code = #{provinceCode} ORDER BY code")
    List<City> getCitiesByProvinceCode(String provinceCode);
    
    /**
     * 根据城市代码获取区县列表
     */
    @Select("SELECT * FROM districts WHERE city_code = #{cityCode} ORDER BY code")
    List<District> getDistrictsByCityCode(String cityCode);
    
    /**
     * 根据省份名称获取省份信息
     */
    @Select("SELECT * FROM provinces WHERE name = #{provinceName}")
    Province getProvinceByName(String provinceName);
    
    /**
     * 根据城市名称和省份代码获取城市信息
     */
    @Select("SELECT * FROM cities WHERE name = #{cityName} AND province_code = #{provinceCode}")
    City getCityByNameAndProvinceCode(String cityName, String provinceCode);
    
    /**
     * 根据区县名称和城市代码获取区县信息
     */
    @Select("SELECT * FROM districts WHERE name = #{districtName} AND city_code = #{cityCode}")
    District getDistrictByNameAndCityCode(String districtName, String cityCode);

    // ==================== 省份增删改查 ====================

    /**
     * 插入省份
     */
    @Insert("INSERT INTO provinces (code, name, created_at, updated_at) VALUES (#{code}, #{name}, NOW(), NOW())")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertProvince(Province province);

    /**
     * 更新省份
     */
    @Update("UPDATE provinces SET name = #{name}, updated_at = NOW() WHERE id = #{id}")
    int updateProvince(Province province);

    /**
     * 删除省份
     */
    @Delete("DELETE FROM provinces WHERE id = #{id}")
    int deleteProvince(Integer id);

    // ==================== 城市增删改查 ====================

    /**
     * 插入城市
     */
    @Insert("INSERT INTO cities (code, name, province_code, created_at, updated_at) VALUES (#{code}, #{name}, #{provinceCode}, NOW(), NOW())")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertCity(City city);

    /**
     * 更新城市
     */
    @Update("UPDATE cities SET name = #{name}, updated_at = NOW() WHERE id = #{id}")
    int updateCity(City city);

    /**
     * 删除城市
     */
    @Delete("DELETE FROM cities WHERE id = #{id}")
    int deleteCity(Integer id);

    // ==================== 区县增删改查 ====================

    /**
     * 插入区县
     */
    @Insert("INSERT INTO districts (code, name, city_code, created_at, updated_at) VALUES (#{code}, #{name}, #{cityCode}, NOW(), NOW())")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertDistrict(District district);

    /**
     * 更新区县
     */
    @Update("UPDATE districts SET name = #{name}, updated_at = NOW() WHERE id = #{id}")
    int updateDistrict(District district);

    /**
     * 删除区县
     */
    @Delete("DELETE FROM districts WHERE id = #{id}")
    int deleteDistrict(Integer id);
}
