package com.flower.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.flower.entity.Swiper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 轮播图Mapper接口
 */
@Mapper
public interface SwiperMapper extends BaseMapper<Swiper> {
    
    /**
     * 获取启用状态的轮播图，按排序顺序排列
     */
    @Select("SELECT * FROM swiper WHERE status = 1 ORDER BY sort_order ASC, create_time DESC")
    List<Swiper> getActiveSwipers();
    
    /**
     * 获取所有轮播图，按排序顺序排列
     */
    @Select("SELECT * FROM swiper ORDER BY sort_order ASC, create_time DESC")
    List<Swiper> getAllSwipers();
}
