package com.flower.vo;

import com.flower.entity.Flower;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 花卉视图对象，包含分类信息
 */
@Data
public class FlowerVO {

    private Long id;
    private String name;
    private String description;
    private BigDecimal price;
    private BigDecimal originalPrice;
    private Long categoryId;
    private Integer stockQuantity;
    private Integer salesCount;
    private String mainImage;
    private String images;
    private String tags;
    private String flowerLanguage;
    private String careInstructions;
    private String occasion;
    private String color;
    private String size;
    private Integer isFeatured;
    private Integer status;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

    /**
     * 分类名称
     */
    private String categoryName;
    
    /**
     * 从Flower对象创建FlowerVO
     */
    public static FlowerVO fromFlower(Flower flower) {
        FlowerVO vo = new FlowerVO();
        vo.setId(flower.getId());
        vo.setName(flower.getName());
        vo.setDescription(flower.getDescription());
        vo.setPrice(flower.getPrice());
        vo.setOriginalPrice(flower.getOriginalPrice());
        vo.setCategoryId(flower.getCategoryId());
        vo.setStockQuantity(flower.getStockQuantity());
        vo.setSalesCount(flower.getSalesCount());
        vo.setMainImage(flower.getMainImage());
        vo.setImages(flower.getImages());
        vo.setTags(flower.getTags());
        vo.setFlowerLanguage(flower.getFlowerLanguage());
        vo.setCareInstructions(flower.getCareInstructions());
        vo.setOccasion(flower.getOccasion());
        vo.setColor(flower.getColor());
        vo.setSize(flower.getSize());
        vo.setIsFeatured(flower.getIsFeatured());
        vo.setStatus(flower.getStatus());
        vo.setCreatedAt(flower.getCreatedAt());
        vo.setUpdatedAt(flower.getUpdatedAt());
        return vo;
    }
}
