{"name": "flower-admin", "version": "1.0.0", "description": "Vue 3 Admin System for Flower Shop WeChat Mini-Program", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore"}, "dependencies": {"vue": "^3.4.0", "vue-router": "^4.2.5", "pinia": "^2.1.7", "axios": "^1.6.0", "element-plus": "^2.4.4", "@element-plus/icons-vue": "^2.3.1", "echarts": "^5.4.3", "vue-echarts": "^6.6.1", "dayjs": "^1.11.10", "nprogress": "^0.2.0"}, "devDependencies": {"@vitejs/plugin-vue": "^4.5.0", "vite": "^5.0.0", "eslint": "^8.49.0", "eslint-plugin-vue": "^9.17.0", "@rushstack/eslint-patch": "^1.3.3", "unplugin-auto-import": "^0.17.2", "unplugin-vue-components": "^0.25.2"}}