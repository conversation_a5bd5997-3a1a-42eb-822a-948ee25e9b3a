<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片上传功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #fafafa;
        }
        .test-section h3 {
            color: #666;
            margin-top: 0;
        }
        .upload-area {
            border: 2px dashed #ccc;
            border-radius: 5px;
            padding: 20px;
            text-align: center;
            margin: 10px 0;
            cursor: pointer;
            transition: border-color 0.3s;
        }
        .upload-area:hover {
            border-color: #409eff;
        }
        .upload-area.dragover {
            border-color: #409eff;
            background-color: #f0f9ff;
        }
        input[type="file"] {
            display: none;
        }
        button {
            background-color: #409eff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #337ecc;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            background-color: #f0f9ff;
            border: 1px solid #409eff;
            color: #409eff;
        }
        .error {
            background-color: #fef0f0;
            border: 1px solid #f56c6c;
            color: #f56c6c;
        }
        .info {
            background-color: #f4f4f5;
            border: 1px solid #909399;
            color: #606266;
        }
        .image-preview {
            max-width: 200px;
            max-height: 200px;
            margin: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .progress {
            width: 100%;
            height: 20px;
            background-color: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-bar {
            height: 100%;
            background-color: #409eff;
            transition: width 0.3s;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌸 花卉管理系统 - 图片上传功能测试</h1>
        
        <!-- 登录测试 -->
        <div class="test-section">
            <h3>1. 管理员登录测试</h3>
            <button onclick="testLogin()">测试登录</button>
            <div id="loginResult" class="result" style="display: none;"></div>
        </div>

        <!-- 单张图片上传测试 -->
        <div class="test-section">
            <h3>2. 单张图片上传测试</h3>
            <div class="upload-area" onclick="document.getElementById('singleFile').click()">
                <p>点击选择图片或拖拽图片到此处</p>
                <p style="color: #999; font-size: 12px;">支持 jpg、png、gif 格式，大小不超过5MB</p>
            </div>
            <input type="file" id="singleFile" accept="image/*" onchange="handleSingleUpload(this)">
            <button onclick="testSingleUpload()" id="singleUploadBtn" disabled>上传图片</button>
            <div class="progress" id="singleProgress" style="display: none;">
                <div class="progress-bar" id="singleProgressBar"></div>
            </div>
            <div id="singleResult" class="result" style="display: none;"></div>
            <div id="singlePreview"></div>
        </div>

        <!-- 批量图片上传测试 -->
        <div class="test-section">
            <h3>3. 批量图片上传测试</h3>
            <div class="upload-area" onclick="document.getElementById('multipleFiles').click()">
                <p>点击选择多张图片或拖拽图片到此处</p>
                <p style="color: #999; font-size: 12px;">最多选择10张图片</p>
            </div>
            <input type="file" id="multipleFiles" accept="image/*" multiple onchange="handleMultipleUpload(this)">
            <button onclick="testBatchUpload()" id="batchUploadBtn" disabled>批量上传</button>
            <div class="progress" id="batchProgress" style="display: none;">
                <div class="progress-bar" id="batchProgressBar"></div>
            </div>
            <div id="batchResult" class="result" style="display: none;"></div>
            <div id="batchPreview"></div>
        </div>

        <!-- 商品创建测试 -->
        <div class="test-section">
            <h3>4. 商品创建测试（包含图片）</h3>
            <button onclick="testFlowerCreation()" id="flowerCreateBtn" disabled>创建测试商品</button>
            <div id="flowerResult" class="result" style="display: none;"></div>
        </div>

        <!-- 测试结果汇总 -->
        <div class="test-section">
            <h3>5. 测试结果汇总</h3>
            <button onclick="runAllTests()">运行所有测试</button>
            <div id="summaryResult" class="result" style="display: none;"></div>
        </div>
    </div>

    <script>
        // 全局变量
        let authToken = null;
        let selectedSingleFile = null;
        let selectedMultipleFiles = [];
        let uploadedImages = [];

        const API_BASE_URL = 'http://localhost:8080/api';

        // 显示结果
        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `result ${type}`;
            element.style.display = 'block';
        }

        // 显示进度
        function showProgress(progressId, barId, percent) {
            const progress = document.getElementById(progressId);
            const bar = document.getElementById(barId);
            progress.style.display = 'block';
            bar.style.width = percent + '%';
        }

        // 隐藏进度
        function hideProgress(progressId) {
            document.getElementById(progressId).style.display = 'none';
        }

        // 测试登录
        async function testLogin() {
            showResult('loginResult', '正在登录...', 'info');
            
            try {
                const response = await fetch(`${API_BASE_URL}/admin/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: '123456'
                    })
                });

                const data = await response.json();
                
                if (data.code === 200) {
                    authToken = data.data.token;
                    showResult('loginResult', `登录成功！\nToken: ${authToken.substring(0, 20)}...`, 'success');
                    
                    // 启用其他测试按钮
                    document.getElementById('singleUploadBtn').disabled = false;
                    document.getElementById('batchUploadBtn').disabled = false;
                    document.getElementById('flowerCreateBtn').disabled = false;
                } else {
                    showResult('loginResult', `登录失败: ${data.message}`, 'error');
                }
            } catch (error) {
                showResult('loginResult', `登录异常: ${error.message}`, 'error');
            }
        }

        // 处理单文件选择
        function handleSingleUpload(input) {
            selectedSingleFile = input.files[0];
            if (selectedSingleFile) {
                showResult('singleResult', `已选择文件: ${selectedSingleFile.name} (${(selectedSingleFile.size / 1024).toFixed(1)} KB)`, 'info');
                
                // 显示预览
                const reader = new FileReader();
                reader.onload = function(e) {
                    document.getElementById('singlePreview').innerHTML = 
                        `<img src="${e.target.result}" class="image-preview" alt="预览">`;
                };
                reader.readAsDataURL(selectedSingleFile);
            }
        }

        // 处理多文件选择
        function handleMultipleUpload(input) {
            selectedMultipleFiles = Array.from(input.files);
            if (selectedMultipleFiles.length > 0) {
                const totalSize = selectedMultipleFiles.reduce((sum, file) => sum + file.size, 0);
                showResult('batchResult', 
                    `已选择 ${selectedMultipleFiles.length} 个文件，总大小: ${(totalSize / 1024).toFixed(1)} KB`, 'info');
                
                // 显示预览
                const previewHtml = selectedMultipleFiles.map(file => {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        const img = document.createElement('img');
                        img.src = e.target.result;
                        img.className = 'image-preview';
                        img.alt = file.name;
                        document.getElementById('batchPreview').appendChild(img);
                    };
                    reader.readAsDataURL(file);
                    return '';
                }).join('');
                
                document.getElementById('batchPreview').innerHTML = '';
            }
        }

        // 测试单张图片上传
        async function testSingleUpload() {
            if (!authToken) {
                showResult('singleResult', '请先登录！', 'error');
                return;
            }

            if (!selectedSingleFile) {
                showResult('singleResult', '请先选择图片！', 'error');
                return;
            }

            showResult('singleResult', '正在上传...', 'info');
            showProgress('singleProgress', 'singleProgressBar', 0);

            try {
                const formData = new FormData();
                formData.append('file', selectedSingleFile);

                // 模拟进度
                let progress = 0;
                const progressInterval = setInterval(() => {
                    progress += Math.random() * 30;
                    if (progress > 90) progress = 90;
                    showProgress('singleProgress', 'singleProgressBar', progress);
                }, 200);

                const response = await fetch(`${API_BASE_URL}/admin/upload/image`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: formData
                });

                clearInterval(progressInterval);
                showProgress('singleProgress', 'singleProgressBar', 100);

                const data = await response.json();

                if (response.status === 200 && data.code === 200) {
                    uploadedImages.push(data.data);
                    showResult('singleResult', 
                        `上传成功！\n文件名: ${data.data.fileName}\n访问URL: ${data.data.url}\n文件大小: ${data.data.size} bytes`, 'success');
                } else {
                    showResult('singleResult', `上传失败: ${data.message || '未知错误'}`, 'error');
                }

                setTimeout(() => hideProgress('singleProgress'), 2000);

            } catch (error) {
                showResult('singleResult', `上传异常: ${error.message}`, 'error');
                hideProgress('singleProgress');
            }
        }

        // 测试批量图片上传
        async function testBatchUpload() {
            if (!authToken) {
                showResult('batchResult', '请先登录！', 'error');
                return;
            }

            if (selectedMultipleFiles.length === 0) {
                showResult('batchResult', '请先选择图片！', 'error');
                return;
            }

            showResult('batchResult', '正在批量上传...', 'info');
            showProgress('batchProgress', 'batchProgressBar', 0);

            try {
                const formData = new FormData();
                selectedMultipleFiles.forEach(file => {
                    formData.append('files', file);
                });

                // 模拟进度
                let progress = 0;
                const progressInterval = setInterval(() => {
                    progress += Math.random() * 20;
                    if (progress > 90) progress = 90;
                    showProgress('batchProgress', 'batchProgressBar', progress);
                }, 300);

                const response = await fetch(`${API_BASE_URL}/admin/upload/shop-images`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: formData
                });

                clearInterval(progressInterval);
                showProgress('batchProgress', 'batchProgressBar', 100);

                const data = await response.json();

                if (response.status === 200 && data.code === 200) {
                    uploadedImages.push(...data.data);
                    const resultText = `批量上传成功！\n成功上传 ${data.data.length} 张图片:\n` +
                        data.data.map((item, index) => `${index + 1}. ${item.fileName} -> ${item.url}`).join('\n');
                    showResult('batchResult', resultText, 'success');
                } else {
                    showResult('batchResult', `批量上传失败: ${data.message || '未知错误'}`, 'error');
                }

                setTimeout(() => hideProgress('batchProgress'), 2000);

            } catch (error) {
                showResult('batchResult', `批量上传异常: ${error.message}`, 'error');
                hideProgress('batchProgress');
            }
        }

        // 测试商品创建
        async function testFlowerCreation() {
            if (!authToken) {
                showResult('flowerResult', '请先登录！', 'error');
                return;
            }

            if (uploadedImages.length === 0) {
                showResult('flowerResult', '请先上传图片！', 'error');
                return;
            }

            showResult('flowerResult', '正在创建测试商品...', 'info');

            try {
                const flowerData = {
                    name: '测试商品（带图片）',
                    description: '这是一个包含图片的测试商品',
                    price: 99.99,
                    originalPrice: 129.99,
                    categoryId: 1,
                    stockQuantity: 100,
                    status: 1,
                    mainImage: uploadedImages[0].url
                };

                const response = await fetch(`${API_BASE_URL}/admin/flowers`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify(flowerData)
                });

                const data = await response.json();

                if (response.status === 200 && data.code === 200) {
                    showResult('flowerResult', 
                        `商品创建成功！\n商品ID: ${data.data.id}\n商品名称: ${data.data.name}\n主图URL: ${data.data.mainImage}`, 'success');
                } else {
                    showResult('flowerResult', `商品创建失败: ${data.message || '未知错误'}`, 'error');
                }

            } catch (error) {
                showResult('flowerResult', `商品创建异常: ${error.message}`, 'error');
            }
        }

        // 运行所有测试
        async function runAllTests() {
            showResult('summaryResult', '开始运行所有测试...', 'info');
            
            // 依次执行测试
            await testLogin();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            if (authToken) {
                // 创建测试文件
                const canvas = document.createElement('canvas');
                canvas.width = 200;
                canvas.height = 200;
                const ctx = canvas.getContext('2d');
                ctx.fillStyle = '#4CAF50';
                ctx.fillRect(0, 0, 200, 200);
                ctx.fillStyle = '#FFFFFF';
                ctx.font = '20px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('TEST', 100, 100);
                
                canvas.toBlob(async (blob) => {
                    selectedSingleFile = new File([blob], 'test-image.jpg', { type: 'image/jpeg' });
                    await testSingleUpload();
                    await new Promise(resolve => setTimeout(resolve, 1000));
                    
                    if (uploadedImages.length > 0) {
                        await testFlowerCreation();
                    }
                    
                    // 生成汇总报告
                    const summary = `
测试完成！
==========
✅ 管理员登录: ${authToken ? '成功' : '失败'}
✅ 图片上传: ${uploadedImages.length > 0 ? '成功' : '失败'}
✅ 商品创建: ${uploadedImages.length > 0 ? '成功' : '失败'}

上传的图片数量: ${uploadedImages.length}
测试结果: ${authToken && uploadedImages.length > 0 ? '全部通过 🎉' : '部分失败 ⚠️'}
                    `;
                    
                    showResult('summaryResult', summary, 
                        authToken && uploadedImages.length > 0 ? 'success' : 'error');
                }, 'image/jpeg', 0.8);
            }
        }

        // 拖拽上传支持
        document.addEventListener('DOMContentLoaded', function() {
            const uploadAreas = document.querySelectorAll('.upload-area');
            
            uploadAreas.forEach(area => {
                area.addEventListener('dragover', function(e) {
                    e.preventDefault();
                    this.classList.add('dragover');
                });
                
                area.addEventListener('dragleave', function(e) {
                    e.preventDefault();
                    this.classList.remove('dragover');
                });
                
                area.addEventListener('drop', function(e) {
                    e.preventDefault();
                    this.classList.remove('dragover');
                    
                    const files = e.dataTransfer.files;
                    if (files.length > 0) {
                        if (this.parentElement.querySelector('#singleFile')) {
                            // 单文件上传区域
                            selectedSingleFile = files[0];
                            handleSingleUpload({ files: [files[0]] });
                        } else {
                            // 多文件上传区域
                            selectedMultipleFiles = Array.from(files);
                            handleMultipleUpload({ files: files });
                        }
                    }
                });
            });
        });
    </script>
</body>
</html>
