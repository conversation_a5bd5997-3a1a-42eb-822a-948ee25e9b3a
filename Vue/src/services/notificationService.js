import { ref, reactive } from 'vue'
import { ElNotification, ElMessage } from 'element-plus'
import { useWebSocket } from '@vueuse/core'

// 通知状态管理
export const notificationState = reactive({
  notifications: [],
  unreadCount: 0,
  isConnected: false,
  lastOrderCount: 0
})

// WebSocket连接
let wsConnection = null

/**
 * 初始化通知服务
 */
export function initNotificationService() {
  // 请求浏览器通知权限
  requestNotificationPermission()
  
  // 初始化WebSocket连接
  initWebSocketConnection()
  
  // 定期检查新订单
  startOrderPolling()
}

/**
 * 请求浏览器通知权限
 */
function requestNotificationPermission() {
  if ('Notification' in window) {
    if (Notification.permission === 'default') {
      Notification.requestPermission().then(permission => {
        if (permission === 'granted') {
          console.log('浏览器通知权限已获取')
        }
      })
    }
  }
}

/**
 * 初始化WebSocket连接
 */
function initWebSocketConnection() {
  const wsUrl = import.meta.env.VITE_WS_URL || `wss://www.mxm.qiangs.xyz:8080/api/ws/notifications`
  
  wsConnection = useWebSocket(wsUrl, {
    onConnected: (ws) => {
      console.log('WebSocket连接已建立')
      notificationState.isConnected = true
      ElMessage.success('实时通知已连接')
    },
    onDisconnected: (ws, event) => {
      console.log('WebSocket连接已断开')
      notificationState.isConnected = false
      // 尝试重连
      setTimeout(() => {
        if (!notificationState.isConnected) {
          initWebSocketConnection()
        }
      }, 5000)
    },
    onError: (ws, event) => {
      console.error('WebSocket连接错误:', event)
      notificationState.isConnected = false
    },
    onMessage: (ws, event) => {
      try {
        // 检查消息是否为JSON格式
        if (event.data.startsWith('{') || event.data.startsWith('[')) {
          const message = JSON.parse(event.data)
          handleWebSocketMessage(message)
        } else {
          // 处理纯文本消息
          console.log('WebSocket文本消息:', event.data)
          if (event.data === '连接成功') {
            console.log('WebSocket连接已建立')
          }
        }
      } catch (error) {
        console.error('解析WebSocket消息失败:', error)
      }
    },
    heartbeat: {
      message: 'ping',
      interval: 30000,
      pongTimeout: 5000
    }
  })
}

/**
 * 处理WebSocket消息
 */
function handleWebSocketMessage(message) {
  switch (message.type) {
    case 'NEW_ORDER':
      handleNewOrderNotification(message.data)
      break
    case 'ORDER_STATUS_CHANGE':
      handleOrderStatusChangeNotification(message.data)
      break
    case 'LOW_STOCK':
      handleLowStockNotification(message.data)
      break
    default:
      console.log('未知消息类型:', message.type)
  }
}

/**
 * 处理新订单通知
 */
function handleNewOrderNotification(orderData) {
  const notification = {
    id: Date.now(),
    title: '新订单提醒',
    description: `订单号：${orderData.orderNo}，金额：¥${orderData.totalAmount}`,
    time: new Date(),
    read: false,
    icon: 'ShoppingCart',
    iconColor: '#409EFF',
    type: 'order',
    data: orderData
  }
  
  // 添加到通知列表
  addNotification(notification)
  
  // 显示Element Plus通知
  ElNotification({
    title: '新订单提醒',
    message: `您有新的订单：${orderData.orderNo}`,
    type: 'success',
    duration: 5000,
    onClick: () => {
      // 点击通知跳转到订单详情
      window.open(`/#/orders/${orderData.id}`, '_blank')
    }
  })
  
  // 显示浏览器通知
  showBrowserNotification(
    '新订单提醒',
    `订单号：${orderData.orderNo}，金额：¥${orderData.totalAmount}`,
    '/favicon.ico'
  )
  
  // 播放提示音
  playNotificationSound()
}

/**
 * 处理订单状态变更通知
 */
function handleOrderStatusChangeNotification(orderData) {
  const statusText = getOrderStatusText(orderData.status)
  const notification = {
    id: Date.now(),
    title: '订单状态更新',
    description: `订单${orderData.orderNo}状态已更新为：${statusText}`,
    time: new Date(),
    read: false,
    icon: 'Bell',
    iconColor: '#E6A23C',
    type: 'order_status',
    data: orderData
  }
  
  addNotification(notification)
  
  ElNotification({
    title: '订单状态更新',
    message: `订单${orderData.orderNo}状态已更新为：${statusText}`,
    type: 'info',
    duration: 3000
  })
}

/**
 * 处理库存不足通知
 */
function handleLowStockNotification(stockData) {
  const notification = {
    id: Date.now(),
    title: '库存预警',
    description: `${stockData.flowerName}库存不足，当前库存：${stockData.stock}`,
    time: new Date(),
    read: false,
    icon: 'Warning',
    iconColor: '#E6A23C',
    type: 'stock',
    data: stockData
  }
  
  addNotification(notification)
  
  ElNotification({
    title: '库存预警',
    message: `${stockData.flowerName}库存不足，请及时补货`,
    type: 'warning',
    duration: 0 // 不自动关闭
  })
}

/**
 * 添加通知到列表
 */
function addNotification(notification) {
  notificationState.notifications.unshift(notification)
  notificationState.unreadCount++
  
  // 限制通知数量，最多保留100条
  if (notificationState.notifications.length > 100) {
    notificationState.notifications = notificationState.notifications.slice(0, 100)
  }
}

/**
 * 显示浏览器通知
 */
function showBrowserNotification(title, body, icon) {
  if ('Notification' in window && Notification.permission === 'granted') {
    const notification = new Notification(title, {
      body,
      icon,
      badge: icon,
      tag: 'flower-shop-notification',
      requireInteraction: true
    })
    
    notification.onclick = () => {
      window.focus()
      notification.close()
    }
    
    // 5秒后自动关闭
    setTimeout(() => {
      notification.close()
    }, 5000)
  }
}

/**
 * 播放提示音
 */
function playNotificationSound() {
  try {
    const audio = new Audio('/notification-sound.mp3')
    audio.volume = 0.5
    audio.play().catch(error => {
      console.log('播放提示音失败:', error)
    })
  } catch (error) {
    console.log('创建音频对象失败:', error)
  }
}

/**
 * 定期检查新订单（作为WebSocket的备用方案）
 */
function startOrderPolling() {
  setInterval(async () => {
    try {
      // 这里应该调用API检查新订单
      // const response = await adminApi.getOrderStats()
      // 如果有新订单，触发通知
    } catch (error) {
      console.error('检查新订单失败:', error)
    }
  }, 30000) // 每30秒检查一次
}

/**
 * 标记通知为已读
 */
export function markNotificationAsRead(notificationId) {
  const notification = notificationState.notifications.find(n => n.id === notificationId)
  if (notification && !notification.read) {
    notification.read = true
    notificationState.unreadCount--
  }
}

/**
 * 标记所有通知为已读
 */
export function markAllNotificationsAsRead() {
  notificationState.notifications.forEach(n => {
    if (!n.read) {
      n.read = true
    }
  })
  notificationState.unreadCount = 0
}

/**
 * 清空所有通知
 */
export function clearAllNotifications() {
  notificationState.notifications = []
  notificationState.unreadCount = 0
}

/**
 * 获取订单状态文本
 */
function getOrderStatusText(status) {
  const statusMap = {
    1: '待支付',
    2: '已支付',
    3: '配送中',
    4: '已完成',
    5: '已取消'
  }
  return statusMap[status] || '未知状态'
}

/**
 * 发送测试通知
 */
export function sendTestNotification() {
  const testOrder = {
    id: 999,
    orderNo: 'TEST' + Date.now(),
    totalAmount: '99.99',
    userNickname: '测试用户'
  }

  handleNewOrderNotification(testOrder)
}

/**
 * 发送测试库存通知
 */
export function sendTestStockNotification() {
  const testStock = {
    id: 888,
    flowerName: '测试玫瑰',
    stock: 5,
    minStock: 10
  }

  handleLowStockNotification(testStock)
}
