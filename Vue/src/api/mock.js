// Mock数据服务 - 用于开发环境的数据模拟
import { ElMessage } from 'element-plus'

// 模拟延迟
const delay = (ms = 500) => new Promise(resolve => setTimeout(resolve, ms))

// Mock API服务
export const mockApi = {
  // 分类相关
  async getCategories(params = {}) {
    await delay()
    return {
      code: 200,
      message: '获取分类列表成功',
      data: {
        records: [],
        total: 0,
        current: 1,
        size: 10
      }
    }
  },

  async createCategory(data) {
    await delay()
    return {
      code: 200,
      message: '创建分类成功',
      data: { id: Date.now(), ...data }
    }
  },

  async updateCategory(id, data) {
    await delay()
    return {
      code: 200,
      message: '更新分类成功',
      data: { id, ...data }
    }
  },

  async deleteCategory(id) {
    await delay()
    return {
      code: 200,
      message: '删除分类成功',
      data: null
    }
  },

  // 商品相关
  async getFlowers(params = {}) {
    await delay()
    return {
      code: 200,
      message: '获取商品列表成功',
      data: {
        records: [],
        total: 0,
        current: params.current || 1,
        size: params.size || 10
      }
    }
  },

  // 评价相关
  async getReviews(params = {}) {
    await delay()
    return {
      code: 200,
      message: '获取评价列表成功',
      data: {
        records: [],
        total: 0,
        current: params.current || 1,
        size: params.size || 10
      }
    }
  },

  // 地址相关
  async getProvinces() {
    await delay(200)
    return {
      code: 200,
      message: '获取省份列表成功',
      data: []
    }
  },

  async getCities(provinceCode) {
    await delay(200)
    return {
      code: 200,
      message: '获取城市列表成功',
      data: []
    }
  },

  async getDistricts(cityCode) {
    await delay(200)
    return {
      code: 200,
      message: '获取区县列表成功',
      data: []
    }
  },

  // 订单相关
  async getOrders(params = {}) {
    await delay()
    return {
      code: 200,
      message: '获取订单列表成功',
      data: {
        records: [],
        total: 0,
        current: params.current || 1,
        size: params.size || 10
      }
    }
  }
}
