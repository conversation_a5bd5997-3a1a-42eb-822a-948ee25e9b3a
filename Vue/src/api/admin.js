import request from './request'
import { mockApi } from './mock'

// 检查是否使用Mock数据（开发环境且后端接口不可用时）
// 设置为false以优先使用真实后端接口
const useMockData = false

export const adminApi = {
  // 管理员认证
  login: (data) => request.post('/admin/login', data),
  logout: () => request.post('/admin/logout'),
  verifyToken: () => request.get('/admin/verify'),

  // 验证码
  getCaptcha: () => request.get('/admin/captcha'),
  
  // 仪表盘统计
  getStats: () => request.get('/admin/stats'),
  getChartData: (type) => request.get(`/admin/chart/${type}`),
  
  // 用户管理
  getUsers: (params) => request.get('/admin/users', { params }),
  getUserDetail: (id) => request.get(`/admin/users/${id}`),
  updateUser: (id, data) => request.put(`/admin/users/${id}`, data),
  updateUserStatus: (id, status) => request.put(`/admin/users/${id}/status`, { status }),
  deleteUser: (id) => request.delete(`/admin/users/${id}`),
  batchDeleteUsers: (ids) => request.delete('/admin/users/batch', { data: { ids } }),
  
  // 商品管理
  getFlowers: (params) => request.get('/admin/flowers', { params }),
  getFlowerDetail: (id) => request.get(`/admin/flowers/${id}`),
  createFlower: (data) => request.post('/admin/flowers', data),
  updateFlower: (id, data) => request.put(`/admin/flowers/${id}`, data),
  deleteFlower: (id) => request.delete(`/admin/flowers/${id}`),
  updateFlowerStatus: (id, status) => request.put(`/admin/flowers/${id}/status`, { status }),
  
  // 分类管理
  getCategories: (params) => request.get('/admin/categories', { params }),
  createCategory: (data) => request.post('/admin/categories', data),
  updateCategory: (id, data) => request.put(`/admin/categories/${id}`, data),
  updateCategoryStatus: (id, status) => request.put(`/admin/categories/${id}/status`, { status }),
  deleteCategory: (id) => request.delete(`/admin/categories/${id}`),

  // 价格分类管理
  getPriceCategories: (params) => request.get('/admin/price-categories', { params }),
  getPriceCategoryDetail: (id) => request.get(`/admin/price-categories/${id}`),
  createPriceCategory: (data) => request.post('/admin/price-categories', data),
  updatePriceCategory: (id, data) => request.put(`/admin/price-categories/${id}`, data),
  updatePriceCategoryStatus: (id, status) => request.put(`/admin/price-categories/${id}/status`, { status }),
  deletePriceCategory: (id) => request.delete(`/admin/price-categories/${id}`),
  getActivePriceCategories: () => request.get('/admin/price-categories/active'),
  
  // 订单管理
  getOrders: async (params) => {
    try {
      return await request.get('/admin/orders', { params })
    } catch (error) {
      console.warn('后端API调用失败，使用Mock数据:', error.message)
      return await mockApi.getOrders(params)
    }
  },
  getOrderDetail: (id) => request.get(`/admin/orders/${id}`),
  updateOrderStatus: (id, status) => request.put(`/admin/orders/${id}/status`, { status }),
  updateOrder: (id, data) => request.put(`/admin/orders/${id}`, data),
  confirmPayment: (id) => request.put(`/admin/orders/${id}/confirm-payment`),
  deleteOrder: (id) => request.delete(`/admin/orders/${id}`),
  
  // 评价管理
  getReviews: (params) => request.get('/admin/reviews', { params }),
  updateReviewStatus: (id, status) => request.put(`/admin/reviews/${id}/status`, { status }),
  deleteReview: (id) => request.delete(`/admin/reviews/${id}`),
  batchDeleteReviews: (ids) => request.delete('/admin/reviews/batch', { data: { ids } }),
  
  // 地址管理
  getProvinces: () => request.get('/admin/provinces'),
  getCities: (provinceCode) => request.get(`/admin/cities/${provinceCode}`),
  getDistricts: (cityCode) => request.get(`/admin/districts/${cityCode}`),
  getAddressStats: () => request.get('/admin/address-stats'),

  // 省份增删改查
  addProvince: (data) => request.post('/admin/provinces', data),
  updateProvince: (id, data) => request.put(`/admin/provinces/${id}`, data),
  deleteProvince: (id) => request.delete(`/admin/provinces/${id}`),

  // 城市增删改查
  addCity: (data) => request.post('/admin/cities', data),
  updateCity: (id, data) => request.put(`/admin/cities/${id}`, data),
  deleteCity: (id) => request.delete(`/admin/cities/${id}`),

  // 区县增删改查
  addDistrict: (data) => request.post('/admin/districts', data),
  updateDistrict: (id, data) => request.put(`/admin/districts/${id}`, data),
  deleteDistrict: (id) => request.delete(`/admin/districts/${id}`),
  
  // 文件上传
  uploadImage: async (file) => {
    try {
      const formData = new FormData()
      formData.append('file', file)
      return await request.post('/admin/upload/image', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })
    } catch (error) {
      console.warn('后端API调用失败，使用Mock数据:', error.message)
      return await mockApi.uploadImage(file)
    }
  },

  // 个人设置
  getProfile: () => request.get('/admin/profile'),
  updateProfile: (data) => request.put('/admin/profile', data),
  updatePassword: (data) => request.put('/admin/password', data),
  uploadAvatar: async (file) => {
    const formData = new FormData()
    formData.append('file', file)
    return await request.post('/admin/upload/avatar', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  }
}
