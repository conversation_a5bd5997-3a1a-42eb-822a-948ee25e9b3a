<template>
  <div class="image-upload">
    <!-- 主图上传 -->
    <div v-if="type === 'main'" class="main-image-upload">
      <el-upload
        class="main-upload"
        :action="uploadUrl"
        :headers="uploadHeaders"
        :show-file-list="false"
        :on-success="handleMainSuccess"
        :on-error="handleError"
        :before-upload="beforeUpload"
        :disabled="uploading"
        accept="image/*"
        drag
      >
        <div v-if="!mainImageUrl" class="upload-placeholder">
          <el-icon class="upload-icon"><Plus /></el-icon>
          <div class="upload-text">点击或拖拽上传主图</div>
          <div class="upload-hint">支持 jpg、png、gif 格式，大小不超过5MB</div>
        </div>
        <div v-else class="uploaded-image">
          <img :src="mainImageUrl" alt="主图" />
          <div class="image-overlay">
            <el-icon class="overlay-icon"><Edit /></el-icon>
            <span>点击更换</span>
          </div>
        </div>
      </el-upload>
      
      <!-- 删除主图按钮 -->
      <el-button
        v-if="mainImageUrl"
        type="danger"
        size="small"
        class="delete-main-btn"
        @click="deleteMainImage"
        :loading="deleting"
      >
        <el-icon><Delete /></el-icon>
        删除主图
      </el-button>
    </div>

    <!-- 详情图片上传 -->
    <div v-else-if="type === 'detail'" class="detail-images-upload">
      <el-upload
        class="detail-upload"
        :action="uploadUrl"
        :headers="uploadHeaders"
        :file-list="detailFileList"
        :on-success="handleDetailSuccess"
        :on-error="handleError"
        :on-remove="handleRemove"
        :before-upload="beforeUpload"
        :on-preview="handlePreview"
        :disabled="uploading"
        accept="image/*"
        list-type="picture-card"
        multiple
        :limit="maxCount"
        :auto-upload="true"
      >
        <div class="upload-btn">
          <el-icon><Plus /></el-icon>
          <div class="upload-text">添加图片</div>
        </div>

        <template #tip>
          <div class="upload-tip">
            最多上传{{ maxCount }}张图片，支持 jpg、png、gif 格式，单张大小不超过5MB
          </div>
        </template>
      </el-upload>
    </div>

    <!-- 上传进度 -->
    <div v-if="uploading" class="upload-progress">
      <el-progress :percentage="uploadProgress" :show-text="false" />
      <span class="progress-text">上传中... {{ uploadProgress }}%</span>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Edit, Delete } from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'

// Props
const props = defineProps({
  type: {
    type: String,
    default: 'main', // 'main' | 'detail'
    validator: (value) => ['main', 'detail'].includes(value)
  },
  modelValue: {
    type: [String, Array],
    default: () => []
  },
  maxCount: {
    type: Number,
    default: 10
  },
  disabled: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'change'])

// 获取认证store
const authStore = useAuthStore()

// 响应式数据
const uploading = ref(false)
const deleting = ref(false)
const uploadProgress = ref(0)

// 主图URL
const mainImageUrl = ref('')

// 详情图片列表
const detailFileList = ref([])

// 初始化数据
if (props.type === 'main') {
  mainImageUrl.value = props.modelValue || ''
} else {
  if (Array.isArray(props.modelValue)) {
    detailFileList.value = props.modelValue.map((url, index) => ({
      uid: Date.now() + index, // 使用时间戳确保唯一性
      name: `image_${index}`,
      status: 'success',
      url: url
    }))
  }
}

// 上传配置 - 使用相对路径，通过Vite代理
const uploadUrl = '/api/admin/upload/image'
const uploadHeaders = computed(() => ({
  Authorization: `Bearer ${authStore.token}`
}))

// 监听props变化
watch(() => props.modelValue, (newValue) => {
  if (props.type === 'main') {
    mainImageUrl.value = newValue || ''
  } else {
    // 处理详情图片数组
    if (Array.isArray(newValue)) {
      detailFileList.value = newValue.map((url, index) => ({
        uid: Date.now() + index, // 使用时间戳确保唯一性
        name: `image_${index}`,
        status: 'success',
        url: url
      }))
    } else {
      detailFileList.value = []
    }
  }
}, { immediate: true })

// 上传前验证
const beforeUpload = (file) => {
  // 检查文件类型
  const isImage = file.type.startsWith('image/')
  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }

  // 检查文件大小
  const isLt5M = file.size / 1024 / 1024 < 5
  if (!isLt5M) {
    ElMessage.error('图片大小不能超过 5MB!')
    return false
  }

  // 检查详情图片数量限制
  if (props.type === 'detail' && detailFileList.value.length >= props.maxCount) {
    ElMessage.error(`最多只能上传 ${props.maxCount} 张图片!`)
    return false
  }

  uploading.value = true
  uploadProgress.value = 0

  // 模拟上传进度
  const progressTimer = setInterval(() => {
    if (uploadProgress.value < 90) {
      uploadProgress.value += Math.random() * 30
    } else {
      clearInterval(progressTimer)
    }
  }, 200)

  return true
}

// 主图上传成功
const handleMainSuccess = (response) => {
  uploading.value = false
  uploadProgress.value = 100

  if (response.code === 200) {
    mainImageUrl.value = response.data.url
    emit('update:modelValue', response.data.url)
    emit('change', response.data.url)
    ElMessage.success('主图上传成功!')
  } else {
    ElMessage.error(response.message || '上传失败')
  }

  setTimeout(() => {
    uploadProgress.value = 0
  }, 1000)
}

// 详情图片上传成功
const handleDetailSuccess = (response, file) => {
  uploading.value = false
  uploadProgress.value = 100

  if (response.code === 200) {
    // 更新文件列表中对应项的URL
    const fileIndex = detailFileList.value.findIndex(item => item.uid === file.uid)
    if (fileIndex !== -1) {
      detailFileList.value[fileIndex].url = response.data.url
      detailFileList.value[fileIndex].status = 'success'
    } else {
      // 如果找不到对应的文件，直接添加到列表中
      detailFileList.value.push({
        uid: file.uid,
        name: file.name,
        status: 'success',
        url: response.data.url
      })
    }

    // 提取所有图片URL
    const imageUrls = detailFileList.value.map(item => item.url).filter(Boolean)

    emit('update:modelValue', imageUrls)
    emit('change', imageUrls)
    ElMessage.success('图片上传成功!')
  } else {
    // 上传失败，移除该文件
    const fileIndex = detailFileList.value.findIndex(item => item.uid === file.uid)
    if (fileIndex !== -1) {
      detailFileList.value.splice(fileIndex, 1)
    }
    ElMessage.error(response.message || '上传失败')
  }

  setTimeout(() => {
    uploadProgress.value = 0
  }, 1000)
}

// 上传失败
const handleError = (error) => {
  uploading.value = false
  uploadProgress.value = 0
  console.error('上传失败:', error)
  ElMessage.error('上传失败，请重试')
}

// 图片预览处理
const handlePreview = (file) => {
  if (file.url) {
    window.open(file.url, '_blank')
  }
}

// 移除详情图片
const handleRemove = (file) => {
  // 从文件列表中移除该文件
  const fileIndex = detailFileList.value.findIndex(item => item.uid === file.uid)
  if (fileIndex !== -1) {
    detailFileList.value.splice(fileIndex, 1)
  }

  // 更新图片URL数组
  const imageUrls = detailFileList.value.map(item => item.url).filter(Boolean)

  emit('update:modelValue', imageUrls)
  emit('change', imageUrls)
  ElMessage.success('图片已删除')
}

// 删除主图
const deleteMainImage = async () => {
  try {
    await ElMessageBox.confirm('确定要删除主图吗？', '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    deleting.value = true
    
    // 这里可以调用删除接口
    // await deleteImageApi(mainImageUrl.value)
    
    mainImageUrl.value = ''
    emit('update:modelValue', '')
    emit('change', '')
    ElMessage.success('主图删除成功!')
    
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  } finally {
    deleting.value = false
  }
}
</script>

<style scoped>
.image-upload {
  width: 100%;
}

/* 主图上传样式 */
.main-image-upload {
  position: relative;
}

.main-upload {
  width: 200px;
  height: 200px;
}

.main-upload :deep(.el-upload) {
  width: 200px;
  height: 200px;
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: border-color 0.3s;
}

.main-upload :deep(.el-upload:hover) {
  border-color: #409eff;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #8c939d;
}

.upload-icon {
  font-size: 28px;
  margin-bottom: 8px;
}

.upload-text {
  font-size: 14px;
  margin-bottom: 4px;
}

.upload-hint {
  font-size: 12px;
  color: #a8abb2;
  text-align: center;
  line-height: 1.4;
}

.uploaded-image {
  position: relative;
  width: 100%;
  height: 100%;
}

.uploaded-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  opacity: 0;
  transition: opacity 0.3s;
}

.uploaded-image:hover .image-overlay {
  opacity: 1;
}

.overlay-icon {
  font-size: 20px;
  margin-bottom: 4px;
}

.delete-main-btn {
  margin-top: 8px;
}

/* 详情图片上传样式 */
.detail-images-upload {
  width: 100%;
}

.detail-upload :deep(.el-upload--picture-card) {
  width: 120px;
  height: 120px;
}

.detail-upload :deep(.el-upload-list--picture-card .el-upload-list__item) {
  width: 120px;
  height: 120px;
}

.upload-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #8c939d;
}

.upload-btn .el-icon {
  font-size: 24px;
  margin-bottom: 4px;
}

.upload-btn .upload-text {
  font-size: 12px;
}

.upload-tip {
  font-size: 12px;
  color: #606266;
  margin-top: 8px;
  line-height: 1.4;
}

/* 上传进度样式 */
.upload-progress {
  margin-top: 12px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.upload-progress .el-progress {
  flex: 1;
}

.progress-text {
  font-size: 12px;
  color: #606266;
  white-space: nowrap;
}
</style>
