<template>
  <el-dialog
    v-model="visible"
    title="修改订单"
    width="800px"
    :before-close="handleClose"
    destroy-on-close
  >
    <el-form
      ref="formRef"
      :model="editForm"
      :rules="rules"
      label-width="120px"
      v-loading="loading"
    >
      <!-- 收货信息 -->
      <el-card class="mb-16">
        <template #header>
          <span>收货信息</span>
        </template>
        
        <el-form-item label="收货人姓名" prop="recipientName">
          <el-input v-model="editForm.recipientName" placeholder="请输入收货人姓名" />
        </el-form-item>
        
        <el-form-item label="联系电话" prop="recipientPhone">
          <el-input v-model="editForm.recipientPhone" placeholder="请输入联系电话" />
        </el-form-item>
        
        <el-form-item label="收货地址" prop="recipientAddress">
          <el-input
            v-model="editForm.recipientAddress"
            type="textarea"
            :rows="3"
            placeholder="请输入详细收货地址"
          />
        </el-form-item>
      </el-card>

      <!-- 配送信息 -->
      <el-card class="mb-16">
        <template #header>
          <span>配送信息</span>
        </template>
        
        <el-form-item label="配送方式" prop="deliveryType">
          <el-radio-group v-model="editForm.deliveryType">
            <el-radio :label="1">外卖配送</el-radio>
            <el-radio :label="2">到店自取</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="配送备注" prop="deliveryNotes">
          <el-input
            v-model="editForm.deliveryNotes"
            type="textarea"
            :rows="2"
            placeholder="配送时间要求、特殊说明等"
          />
        </el-form-item>
      </el-card>

      <!-- 订单状态和金额 -->
      <el-card class="mb-16">
        <template #header>
          <span>订单状态和金额</span>
        </template>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="订单状态" prop="status">
              <el-select v-model="editForm.status" placeholder="请选择订单状态" style="width: 100%">
                <el-option label="已下单" :value="2">
                  <span>已下单</span>
                  <span style="float: right; color: #8492a6; font-size: 13px">等待配送</span>
                </el-option>
                <el-option label="配送中" :value="3">
                  <span>配送中</span>
                  <span style="float: right; color: #8492a6; font-size: 13px">正在配送</span>
                </el-option>
                <el-option label="待付款" :value="1">
                  <span>待付款</span>
                  <span style="float: right; color: #8492a6; font-size: 13px">已送达等待付款</span>
                </el-option>
                <el-option label="已完成" :value="4">
                  <span>已完成</span>
                  <span style="float: right; color: #8492a6; font-size: 13px">订单完成</span>
                </el-option>
                <el-option label="已取消" :value="5">
                  <span>已取消</span>
                  <span style="float: right; color: #8492a6; font-size: 13px">订单取消</span>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="支付状态" prop="paymentStatus">
              <el-select v-model="editForm.paymentStatus" placeholder="请选择支付状态" style="width: 100%">
                <el-option label="未支付" :value="0">
                  <span>未支付</span>
                  <span style="float: right; color: #f56c6c; font-size: 13px">等待付款</span>
                </el-option>
                <el-option label="已支付" :value="1">
                  <span>已支付</span>
                  <span style="float: right; color: #67c23a; font-size: 13px">付款完成</span>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 金额输入区域 - 重新设计 -->
        <div class="amount-section">
          <div class="amount-grid">
            <!-- 商品总额 -->
            <div class="amount-item">
              <label class="amount-label">商品总额</label>
              <div class="amount-input-container">
                <span class="currency-symbol">¥</span>
                <el-input
                  v-model="editForm.totalAmount"
                  type="number"
                  :step="0.01"
                  :min="0"
                  class="amount-input"
                  placeholder="0.00"
                />
              </div>
              <div class="amount-tip">商品原价总额</div>
            </div>

            <!-- 优惠金额 -->
            <div class="amount-item">
              <label class="amount-label">优惠金额</label>
              <div class="amount-input-container">
                <span class="currency-symbol">¥</span>
                <el-input
                  v-model="editForm.discountAmount"
                  type="number"
                  :step="0.01"
                  :min="0"
                  class="amount-input"
                  placeholder="0.00"
                />
              </div>
              <div class="amount-tip">折扣、优惠券等</div>
            </div>

            <!-- 实付金额 -->
            <div class="amount-item final-amount-item">
              <label class="amount-label">实付金额</label>
              <div class="amount-input-container final-container">
                <span class="currency-symbol final-symbol">¥</span>
                <el-input
                  v-model="editForm.finalAmount"
                  type="number"
                  :step="0.01"
                  :min="0"
                  class="amount-input final-input"
                  placeholder="0.00"
                />
              </div>
              <div class="amount-tip final-tip">客户实际支付金额</div>
            </div>
          </div>
        </div>

        <div class="amount-summary">
          <div class="calculation-formula">
            <span class="formula-text">实付金额 = 商品总额 - 优惠金额</span>
            <div class="calculation-result">
              <span class="result-label">当前实付：</span>
              <span class="result-amount">¥{{ formatMoney(parseFloat(editForm.finalAmount) || 0) }}</span>
            </div>
          </div>
          <el-alert
            title="💡 智能计算"
            type="info"
            :closable="false"
            show-icon
          >
            修改商品总额或优惠金额时，实付金额会自动计算。您也可以手动调整实付金额。
          </el-alert>
        </div>

        <el-form-item label="支付方式" prop="paymentMethod">
          <el-select v-model="editForm.paymentMethod" placeholder="请选择支付方式" style="width: 100%">
            <el-option label="微信支付" value="微信支付" />
            <el-option label="支付宝" value="支付宝" />
            <el-option label="现金支付" value="现金支付" />
            <el-option label="银行卡" value="银行卡" />
            <el-option label="货到付款" value="货到付款" />
          </el-select>
        </el-form-item>
      </el-card>

      <!-- 订单备注 -->
      <el-card class="mb-16">
        <template #header>
          <span>订单备注</span>
        </template>

        <el-form-item label="订单备注" prop="remark">
          <el-input
            v-model="editForm.remark"
            type="textarea"
            :rows="3"
            placeholder="其他特殊要求或备注信息"
          />
        </el-form-item>
      </el-card>

      <!-- 商品信息（只读显示） -->
      <el-card class="mb-16">
        <template #header>
          <span>商品信息</span>
          <el-tag type="info" size="small">商品信息不可修改</el-tag>
        </template>
        
        <el-table :data="editForm.items" stripe>
          <el-table-column label="商品图片" width="80">
            <template #default="{ row }">
              <el-image
                :src="row.flowerImage"
                style="width: 50px; height: 50px"
                fit="cover"
              />
            </template>
          </el-table-column>
          
          <el-table-column prop="flowerName" label="商品名称" />
          
          <el-table-column prop="price" label="单价">
            <template #default="{ row }">
              ¥{{ formatMoney(row.price) }}
            </template>
          </el-table-column>
          
          <el-table-column prop="quantity" label="数量" />
          
          <el-table-column label="小计">
            <template #default="{ row }">
              ¥{{ formatMoney(row.price * row.quantity) }}
            </template>
          </el-table-column>
        </el-table>
        
        <div class="order-summary">
          <div class="summary-row">
            <span class="summary-label">商品总额：</span>
            <span class="summary-amount">¥{{ formatMoney(parseFloat(editForm.totalAmount) || 0) }}</span>
          </div>
          <div class="summary-row">
            <span class="summary-label">优惠金额：</span>
            <span class="summary-discount">-¥{{ formatMoney(parseFloat(editForm.discountAmount) || 0) }}</span>
          </div>
          <div class="summary-row total-row">
            <span class="summary-label">实付金额：</span>
            <span class="summary-final">¥{{ formatMoney(parseFloat(editForm.finalAmount) || 0) }}</span>
          </div>
        </div>
      </el-card>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSave" :loading="saving">
          保存修改
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { formatMoney } from '@/utils'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  order: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:modelValue', 'save'])

const visible = ref(false)
const loading = ref(false)
const saving = ref(false)
const formRef = ref()

// 编辑表单数据
const editForm = reactive({
  id: null,
  recipientName: '',
  recipientPhone: '',
  recipientAddress: '',
  deliveryType: 1,
  deliveryNotes: '',
  remark: '',
  status: 2,
  paymentStatus: 0,
  paymentMethod: '',
  totalAmount: 0,
  discountAmount: 0,
  finalAmount: 0,
  items: []
})

// 表单验证规则
const rules = {
  recipientName: [
    { required: true, message: '请输入收货人姓名', trigger: 'blur' },
    { min: 2, max: 20, message: '姓名长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  recipientPhone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  recipientAddress: [
    { required: true, message: '请输入收货地址', trigger: 'blur' },
    { min: 10, max: 200, message: '地址长度在 10 到 200 个字符', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择订单状态', trigger: 'change' }
  ],
  paymentStatus: [
    { required: true, message: '请选择支付状态', trigger: 'change' }
  ],
  totalAmount: [
    { required: true, message: '请输入商品总额', trigger: 'blur' },
    { type: 'number', min: 0, message: '金额不能小于0', trigger: 'blur' }
  ],
  finalAmount: [
    { required: true, message: '请输入实付金额', trigger: 'blur' },
    { type: 'number', min: 0, message: '金额不能小于0', trigger: 'blur' }
  ]
}

// 监听显示状态
watch(() => props.modelValue, (val) => {
  visible.value = val
  if (val && props.order) {
    initForm()
  }
})

watch(visible, (val) => {
  emit('update:modelValue', val)
})

// 监听总额和优惠金额变化，自动计算实付金额
watch([() => editForm.totalAmount, () => editForm.discountAmount], ([total, discount]) => {
  const totalNum = parseFloat(total) || 0
  const discountNum = parseFloat(discount) || 0
  if (totalNum >= 0 && discountNum >= 0) {
    editForm.finalAmount = Math.max(0, totalNum - discountNum).toFixed(2)
  }
})

// 初始化表单数据
const initForm = () => {
  const order = props.order
  editForm.id = order.id
  editForm.recipientName = order.recipientName || ''
  editForm.recipientPhone = order.recipientPhone || ''
  editForm.recipientAddress = order.recipientAddress || ''
  editForm.deliveryType = order.deliveryType || 1
  editForm.deliveryNotes = order.deliveryNotes || ''
  editForm.remark = order.remark || ''
  editForm.status = order.status || 2
  editForm.paymentStatus = order.paymentStatus || 0
  editForm.paymentMethod = order.paymentMethod || ''
  editForm.totalAmount = (order.totalAmount || 0).toString()
  editForm.discountAmount = (order.discountAmount || 0).toString()
  editForm.finalAmount = (order.finalAmount || order.totalAmount || 0).toString()
  editForm.items = order.items || []
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
}

// 保存修改
const handleSave = async () => {
  try {
    // 表单验证
    await formRef.value.validate()
    
    // 确认保存
    await ElMessageBox.confirm(
      '确定要保存订单修改吗？修改后将重新计算配送时间。',
      '确认保存',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    saving.value = true
    
    // 准备保存数据
    const saveData = {
      id: editForm.id,
      recipientName: editForm.recipientName,
      recipientPhone: editForm.recipientPhone,
      recipientAddress: editForm.recipientAddress,
      deliveryType: editForm.deliveryType,
      deliveryNotes: editForm.deliveryNotes,
      remark: editForm.remark,
      status: editForm.status,
      paymentStatus: editForm.paymentStatus,
      paymentMethod: editForm.paymentMethod,
      totalAmount: parseFloat(editForm.totalAmount) || 0,
      discountAmount: parseFloat(editForm.discountAmount) || 0,
      finalAmount: parseFloat(editForm.finalAmount) || 0
    }
    
    // 触发保存事件
    emit('save', saveData)
    
  } catch (error) {
    if (error !== 'cancel') {
      console.error('保存失败:', error)
    }
  } finally {
    saving.value = false
  }
}
</script>

<style scoped>
.mb-16 {
  margin-bottom: 16px;
}

/* 商品信息汇总样式 */
.order-summary {
  margin-top: 20px;
  padding: 16px;
  background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
  border-radius: 12px;
  border: 2px solid #fdcb6e;
}

.summary-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 6px;
  font-size: 14px;
}

.summary-row:last-child {
  margin-bottom: 0;
}

.summary-label {
  font-weight: 500;
  color: #2d3436;
}

.summary-amount {
  font-weight: 600;
  color: #0984e3;
  font-size: 15px;
}

.summary-discount {
  font-weight: 600;
  color: #e17055;
  font-size: 15px;
}

.summary-final {
  font-weight: 700;
  color: #d63031;
  font-size: 18px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

.total-row {
  background: rgba(255, 255, 255, 0.95) !important;
  border: 2px solid #d63031;
  font-size: 16px;
  margin-top: 8px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-card__header) {
  display: flex;
  justify-content: space-between;
  align-items: center;
}



:deep(.el-select-dropdown__item) {
  height: auto;
  padding: 8px 20px;
  line-height: 1.4;
  color: #606266 !important;
  background-color: #ffffff !important;
}

:deep(.el-select-dropdown__item:hover) {
  background-color: #f5f7fa !important;
  color: #409eff !important;
}

:deep(.el-select-dropdown__item.selected) {
  background-color: #409eff !important;
  color: #ffffff !important;
}

:deep(.el-select-dropdown__item span) {
  color: inherit !important;
}

/* 全新的金额输入区域样式 */
.amount-section {
  padding: 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12px;
  border: 1px solid #dee2e6;
  margin: 16px 0;
}

.amount-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 24px;
  align-items: start;
}

.amount-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.amount-label {
  font-size: 14px;
  font-weight: 600;
  color: #495057;
  margin-bottom: 4px;
  display: block;
}

.amount-input-container {
  position: relative;
  display: flex;
  align-items: center;
  background: white;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  padding: 0;
  transition: all 0.3s ease;
  overflow: hidden;
}

.amount-input-container:hover {
  border-color: #adb5bd;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.amount-input-container:focus-within {
  border-color: #0d6efd;
  box-shadow: 0 0 0 3px rgba(13, 110, 253, 0.25);
}

.currency-symbol {
  padding: 0 12px;
  font-size: 16px;
  font-weight: 600;
  color: #6c757d;
  background: #f8f9fa;
  border-right: 1px solid #e9ecef;
  height: 48px;
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.amount-input {
  flex: 1;
  min-width: 0;
}

:deep(.amount-input .el-input__wrapper) {
  border: none !important;
  box-shadow: none !important;
  padding: 0 16px !important;
  background: transparent !important;
}

:deep(.amount-input .el-input__inner) {
  height: 48px !important;
  font-size: 16px !important;
  font-weight: 600 !important;
  color: #212529 !important;
  border: none !important;
  background: transparent !important;
  text-align: right !important;
  padding: 0 !important;
}

:deep(.amount-input .el-input__inner:focus) {
  box-shadow: none !important;
}

.amount-tip {
  font-size: 12px;
  color: #6c757d;
  text-align: center;
  margin-top: 4px;
}

/* 实付金额特殊样式 */
.final-amount-item {
  position: relative;
}

.final-amount-item::before {
  content: '';
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4);
  border-radius: 12px;
  z-index: -1;
  animation: gradient-border 3s ease infinite;
  background-size: 400% 400%;
}

@keyframes gradient-border {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.final-container {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  border-color: transparent !important;
}

.final-container:hover {
  border-color: transparent !important;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3) !important;
}

.final-container:focus-within {
  border-color: transparent !important;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.4) !important;
}

.final-symbol {
  background: rgba(255, 255, 255, 0.2) !important;
  color: white !important;
  border-right-color: rgba(255, 255, 255, 0.3) !important;
}

:deep(.final-input .el-input__inner) {
  color: white !important;
  font-weight: 700 !important;
  font-size: 18px !important;
}

:deep(.final-input .el-input__inner::placeholder) {
  color: rgba(255, 255, 255, 0.7) !important;
}

.final-tip {
  color: #dc3545 !important;
  font-weight: 600 !important;
}

/* 商品信息汇总样式 */
.order-summary {
  margin-top: 20px;
  padding: 16px;
  background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
  border-radius: 12px;
  border: 2px solid #fdcb6e;
}

.summary-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 6px;
  font-size: 14px;
}

.summary-row:last-child {
  margin-bottom: 0;
}

.summary-label {
  font-weight: 500;
  color: #2d3436;
}

.summary-amount {
  font-weight: 600;
  color: #0984e3;
  font-size: 15px;
}

.summary-discount {
  font-weight: 600;
  color: #e17055;
  font-size: 15px;
}

.summary-final {
  font-weight: 700;
  color: #d63031;
  font-size: 18px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

.total-row {
  background: rgba(255, 255, 255, 0.95) !important;
  border: 2px solid #d63031;
  font-size: 16px;
  margin-top: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .amount-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .amount-section {
    padding: 16px;
  }
}

/* 表单提示样式优化 */
.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 6px;
  line-height: 1.2;
  text-align: center;
}

.form-tip.important {
  color: #f56c6c;
  font-weight: 600;
}

/* 金额汇总区域样式 */
.amount-summary {
  margin-top: 16px;
  padding: 16px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 12px;
  border: 1px solid #e4e7ed;
}

.calculation-formula {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding: 12px 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.formula-text {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

.calculation-result {
  display: flex;
  align-items: center;
  gap: 8px;
}

.result-label {
  font-size: 14px;
  color: #909399;
}

.result-amount {
  font-size: 20px;
  font-weight: 700;
  color: #f56c6c;
  background: linear-gradient(45deg, #ff6b6b, #ee5a24);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}
</style>
