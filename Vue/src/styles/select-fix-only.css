/* 仅修复选择组件下拉选项文字不可见的问题 */

/* 只针对下拉选项的文字颜色修复，不影响其他元素 */
.el-select-dropdown .el-select-dropdown__item {
  color: #606266 !important;
}

.el-select-dropdown .el-select-dropdown__item:hover {
  color: #409eff !important;
}

.el-select-dropdown .el-select-dropdown__item.selected {
  color: #ffffff !important;
}

.el-select-dropdown .el-select-dropdown__item.is-disabled {
  color: #c0c4cc !important;
}

/* 修复 el-option 文字颜色 */
.el-option {
  color: #606266 !important;
}

.el-option:hover {
  color: #409eff !important;
}

.el-option.selected {
  color: #ffffff !important;
}

.el-option.is-disabled {
  color: #c0c4cc !important;
}

/* 修复选择框内的自定义内容 */
.el-select-dropdown .el-select-dropdown__item .option-item {
  color: inherit !important;
}

.el-option .option-item {
  color: inherit !important;
}

.option-item span {
  color: inherit !important;
}

.option-item .el-icon {
  color: #909399 !important;
}

.el-option:hover .option-item .el-icon,
.el-select-dropdown__item:hover .option-item .el-icon {
  color: #409eff !important;
}

.el-option.selected .option-item .el-icon,
.el-select-dropdown__item.selected .option-item .el-icon {
  color: #ffffff !important;
}
