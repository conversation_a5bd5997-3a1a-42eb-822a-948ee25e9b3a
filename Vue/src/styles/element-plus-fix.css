/* Element Plus 选择组件样式修复 */

/* 修复 el-select 下拉选项文字颜色问题 */
.el-select-dropdown {
  background-color: #ffffff !important;
  border: 1px solid #e4e7ed !important;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1) !important;
}

.el-select-dropdown .el-popper__arrow {
  border-bottom-color: #ffffff !important;
}

.el-select-dropdown .el-select-dropdown__item {
  color: #606266 !important;
  background-color: #ffffff !important;
  font-size: 14px !important;
  padding: 0 20px !important;
  height: 34px !important;
  line-height: 34px !important;
  cursor: pointer !important;
  transition: background-color 0.3s !important;
}

.el-select-dropdown .el-select-dropdown__item:hover {
  background-color: #f5f7fa !important;
  color: #409eff !important;
}

.el-select-dropdown .el-select-dropdown__item.selected {
  background-color: #409eff !important;
  color: #ffffff !important;
  font-weight: bold !important;
}

.el-select-dropdown .el-select-dropdown__item.is-disabled {
  color: #c0c4cc !important;
  cursor: not-allowed !important;
  background-color: #ffffff !important;
}

/* 修复 el-option 组件样式 */
.el-option {
  color: #606266 !important;
  background-color: #ffffff !important;
  font-size: 14px !important;
  padding: 0 20px !important;
  height: 34px !important;
  line-height: 34px !important;
  cursor: pointer !important;
  transition: background-color 0.3s !important;
}

.el-option:hover {
  background-color: #f5f7fa !important;
  color: #409eff !important;
}

.el-option.selected {
  background-color: #409eff !important;
  color: #ffffff !important;
  font-weight: bold !important;
}

.el-option.is-disabled {
  color: #c0c4cc !important;
  cursor: not-allowed !important;
  background-color: #ffffff !important;
}

/* 修复 el-cascader 级联选择器样式 */
.el-cascader-panel {
  background-color: #ffffff !important;
  border: 1px solid #e4e7ed !important;
}

.el-cascader-node {
  color: #606266 !important;
  background-color: #ffffff !important;
  padding: 0 20px !important;
  height: 34px !important;
  line-height: 34px !important;
  cursor: pointer !important;
}

.el-cascader-node:hover {
  background-color: #f5f7fa !important;
  color: #409eff !important;
}

.el-cascader-node.is-active {
  background-color: #409eff !important;
  color: #ffffff !important;
}

.el-cascader-node.is-disabled {
  color: #c0c4cc !important;
  cursor: not-allowed !important;
}

/* 修复 el-dropdown 下拉菜单样式 */
.el-dropdown-menu {
  background-color: #ffffff !important;
  border: 1px solid #e4e7ed !important;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1) !important;
}

.el-dropdown-menu__item {
  color: #606266 !important;
  background-color: #ffffff !important;
  font-size: 14px !important;
  padding: 0 20px !important;
  height: 36px !important;
  line-height: 36px !important;
  cursor: pointer !important;
}

.el-dropdown-menu__item:hover {
  background-color: #ecf5ff !important;
  color: #409eff !important;
}

.el-dropdown-menu__item:focus {
  background-color: #ecf5ff !important;
  color: #409eff !important;
}

.el-dropdown-menu__item.is-disabled {
  color: #c0c4cc !important;
  cursor: not-allowed !important;
}

/* 修复 el-autocomplete 自动完成样式 */
.el-autocomplete-suggestion {
  background-color: #ffffff !important;
  border: 1px solid #e4e7ed !important;
}

.el-autocomplete-suggestion__item {
  color: #606266 !important;
  background-color: #ffffff !important;
  padding: 0 20px !important;
  height: 34px !important;
  line-height: 34px !important;
  cursor: pointer !important;
}

.el-autocomplete-suggestion__item:hover {
  background-color: #f5f7fa !important;
  color: #409eff !important;
}

.el-autocomplete-suggestion__item.highlighted {
  background-color: #f5f7fa !important;
  color: #409eff !important;
}

/* 修复 el-tree 树形控件样式 */
.el-tree {
  background-color: #ffffff !important;
  color: #606266 !important;
}

.el-tree-node__content {
  color: #606266 !important;
  background-color: #ffffff !important;
  height: 26px !important;
  line-height: 26px !important;
  cursor: pointer !important;
}

.el-tree-node__content:hover {
  background-color: #f5f7fa !important;
  color: #409eff !important;
}

.el-tree-node.is-current > .el-tree-node__content {
  background-color: #409eff !important;
  color: #ffffff !important;
}

.el-tree-node__label {
  color: inherit !important;
  font-size: 14px !important;
}

/* 修复 el-menu 菜单样式 */
.el-menu {
  background-color: #ffffff !important;
  border-right: solid 1px #e6e6e6 !important;
}

.el-menu-item {
  color: #303133 !important;
  background-color: #ffffff !important;
  height: 56px !important;
  line-height: 56px !important;
  font-size: 14px !important;
  cursor: pointer !important;
}

.el-menu-item:hover {
  background-color: #ecf5ff !important;
  color: #409eff !important;
}

.el-menu-item.is-active {
  background-color: #409eff !important;
  color: #ffffff !important;
}

.el-submenu__title {
  color: #303133 !important;
  background-color: #ffffff !important;
  height: 56px !important;
  line-height: 56px !important;
  font-size: 14px !important;
  cursor: pointer !important;
}

.el-submenu__title:hover {
  background-color: #ecf5ff !important;
  color: #409eff !important;
}

/* 修复 el-checkbox 复选框样式 */
.el-checkbox {
  color: #606266 !important;
  font-weight: 500 !important;
  font-size: 14px !important;
  cursor: pointer !important;
}

.el-checkbox__label {
  color: #606266 !important;
  font-size: 14px !important;
  padding-left: 10px !important;
}

.el-checkbox:hover .el-checkbox__label {
  color: #409eff !important;
}

.el-checkbox.is-checked .el-checkbox__label {
  color: #409eff !important;
}

/* 修复 el-radio 单选框样式 */
.el-radio {
  color: #606266 !important;
  font-weight: 500 !important;
  font-size: 14px !important;
  cursor: pointer !important;
}

.el-radio__label {
  color: #606266 !important;
  font-size: 14px !important;
  padding-left: 10px !important;
}

.el-radio:hover .el-radio__label {
  color: #409eff !important;
}

.el-radio.is-checked .el-radio__label {
  color: #409eff !important;
}

/* 修复 el-table 表格样式 */
.el-table {
  color: #606266 !important;
  background-color: #ffffff !important;
}

.el-table th {
  background-color: #fafafa !important;
  color: #909399 !important;
  font-weight: 500 !important;
}

.el-table td {
  color: #606266 !important;
  background-color: #ffffff !important;
}

.el-table--striped .el-table__body tr.el-table__row--striped td {
  background-color: #fafafa !important;
}

.el-table__body tr:hover > td {
  background-color: #f5f7fa !important;
}

/* 修复 el-pagination 分页样式 */
.el-pagination {
  color: #606266 !important;
}

.el-pagination .el-pager li {
  color: #606266 !important;
  background-color: #ffffff !important;
  border: 1px solid #dcdfe6 !important;
}

.el-pagination .el-pager li:hover {
  color: #409eff !important;
}

.el-pagination .el-pager li.active {
  color: #409eff !important;
  background-color: #409eff !important;
  color: #ffffff !important;
}

/* 修复 el-input 输入框样式 */
.el-input__inner {
  color: #606266 !important;
  background-color: #ffffff !important;
  border: 1px solid #dcdfe6 !important;
}

.el-input__inner:focus {
  border-color: #409eff !important;
}

.el-input__inner::placeholder {
  color: #c0c4cc !important;
}

/* 修复 el-textarea 文本域样式 */
.el-textarea__inner {
  color: #606266 !important;
  background-color: #ffffff !important;
  border: 1px solid #dcdfe6 !important;
}

.el-textarea__inner:focus {
  border-color: #409eff !important;
}

.el-textarea__inner::placeholder {
  color: #c0c4cc !important;
}

/* 修复自定义选项内容样式 */
.option-item {
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
  color: #606266 !important;
  font-size: 14px !important;
}

.el-option .option-item {
  color: inherit !important;
}

.el-option:hover .option-item {
  color: inherit !important;
}

.el-option.selected .option-item {
  color: inherit !important;
}

/* 修复选择框内的图标颜色 */
.el-option .el-icon {
  color: #909399 !important;
}

.el-option:hover .el-icon {
  color: #409eff !important;
}

.el-option.selected .el-icon {
  color: #ffffff !important;
}

/* 修复下拉框中的文本内容 */
.el-select-dropdown__item span {
  color: inherit !important;
}

.el-select-dropdown__item .el-icon {
  color: #909399 !important;
}

.el-select-dropdown__item:hover .el-icon {
  color: #409eff !important;
}

.el-select-dropdown__item.selected .el-icon {
  color: #ffffff !important;
}

/* 修复筛选相关的选择组件 */
.filter-dropdown .el-checkbox {
  color: #606266 !important;
}

.filter-dropdown .el-checkbox__label {
  color: #606266 !important;
}

.filter-dropdown .color-option {
  color: #606266 !important;
}

.filter-dropdown .size-option {
  color: #606266 !important;
}

/* 修复表格筛选下拉框 */
.el-table-filter .el-checkbox {
  color: #606266 !important;
}

.el-table-filter .el-checkbox__label {
  color: #606266 !important;
}

/* 修复弹出层中的选择组件 */
.el-popper .el-select-dropdown__item {
  color: #606266 !important;
}

.el-popper .el-option {
  color: #606266 !important;
}

/* 修复级联选择器面板 */
.el-cascader-panel .el-cascader-node__label {
  color: #606266 !important;
}

.el-cascader-panel .el-cascader-node:hover .el-cascader-node__label {
  color: #409eff !important;
}

.el-cascader-panel .el-cascader-node.is-active .el-cascader-node__label {
  color: #ffffff !important;
}
