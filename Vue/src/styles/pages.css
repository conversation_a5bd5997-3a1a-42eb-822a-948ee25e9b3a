/* 页面通用样式 - 企业级后台管理系统设计 */

/* 页面容器基础样式 */
.page-container {
  min-height: 100vh;
  background: transparent;
}

/* 页面头部样式 */
.page-header {
  background: transparent;
  padding: 0 0 24px 0;
  border: none;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title-section {
  flex: 1;
}

.page-title {
  font-size: 28px;
  font-weight: 700;
  color: #2c3e50;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.title-icon {
  color: #667eea;
  font-size: 32px;
}

.page-subtitle {
  font-size: 14px;
  color: #8892b0;
  margin: 0;
  opacity: 0.8;
}

/* 主要内容区域 */
.main-content {
  padding: 0;
}

/* 统计卡片区域 */
.stats-section {
  margin-bottom: 24px;
}

.stats-card {
  border: none;
  border-radius: 16px;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.stats-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.stats-card.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.stats-card.success {
  background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
  color: white;
}

.stats-card.warning {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
}

.stats-card.danger {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
}

.stats-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
}

.stats-info {
  flex: 1;
}

.stats-value {
  font-size: 36px;
  font-weight: 800;
  margin-bottom: 8px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stats-label {
  font-size: 14px;
  opacity: 0.9;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stats-icon {
  opacity: 0.8;
}

/* 内容卡片 */
.content-card {
  border-radius: 16px;
  border: none;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e9ecef;
  background: #f8f9fa;
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

/* 搜索和筛选区域 */
.search-section {
  margin-bottom: 24px;
}

.search-card {
  border-radius: 16px;
  border: none;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
}

.filter-section {
  margin-bottom: 24px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 12px;
  border: 1px solid #e9ecef;
}

.filter-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.hint-text {
  color: #8892b0;
  font-size: 13px;
}

.option-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 表格容器 */
.table-container {
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid #e9ecef;
  background: white;
}

/* 表格样式增强 */
.el-table {
  border-radius: 12px;
  overflow: hidden;
}

.el-table th {
  background: #f8f9fa !important;
  color: #606266 !important;
  font-weight: 600;
  border-bottom: 1px solid #e9ecef;
}

.el-table td {
  border-bottom: 1px solid #f0f0f0;
}

.el-table .el-table__row:hover {
  background-color: #f8f9fa;
}

/* 表格单元格样式 */
.name-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.location-icon {
  color: #667eea;
  font-size: 16px;
}

.name-text {
  font-weight: 500;
  color: #2c3e50;
}

.time-text {
  color: #8892b0;
  font-size: 13px;
}

/* 分页样式 */
.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 24px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

/* 按钮样式增强 */
.el-button {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.el-button:hover {
  transform: translateY(-1px);
}

.el-button--primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
}

.el-button--primary:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

/* 标签样式增强 */
.el-tag {
  border-radius: 6px;
  font-weight: 500;
  border: none;
}

/* 对话框样式 */
.el-dialog {
  border-radius: 16px;
  overflow: hidden;
}

.el-dialog__header {
  background: #f8f9fa;
  padding: 20px 24px;
  border-bottom: 1px solid #e9ecef;
}

.el-dialog__body {
  padding: 24px;
}

/* 表单样式 */
.el-form-item__label {
  font-weight: 500;
  color: #2c3e50;
}

.el-input__wrapper {
  border-radius: 8px;
  transition: all 0.3s ease;
}

.el-input__wrapper:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.el-select .el-input__wrapper {
  border-radius: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-title {
    font-size: 24px;
  }
  
  .stats-value {
    font-size: 28px;
  }
  
  .stats-content {
    padding: 20px;
  }
  
  .filter-section {
    padding: 16px;
  }
  
  .card-header {
    padding: 16px 20px;
  }
}

@media (max-width: 480px) {
  .page-title {
    font-size: 20px;
  }
  
  .stats-value {
    font-size: 24px;
  }
  
  .stats-content {
    padding: 16px;
  }
}
