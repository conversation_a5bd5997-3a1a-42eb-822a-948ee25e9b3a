<template>
  <el-container class="modern-layout">
    <!-- 顶部导航栏 -->
    <el-header class="modern-header" height="72px">
      <div class="header-wrapper">
        <!-- 左侧区域 -->
        <div class="header-left">
          <div class="brand-section">
            <div class="brand-logo" :class="{ collapsed: sidebarCollapsed }">
              <div class="logo-icon">🌸</div>
              <!-- 折叠状态下的按钮放在花朵右侧 -->
              <el-button
                v-if="sidebarCollapsed"
                class="sidebar-toggle-btn collapsed-btn"
                @click="toggleSidebar"
                :icon="Expand"
                circle
                size="small"
              />
              <div class="brand-info" v-show="!sidebarCollapsed">
                <h1 class="brand-title">花语小铺</h1>
                <span class="brand-subtitle">管理后台</span>
              </div>
            </div>
            <!-- 展开状态下的按钮 -->
            <el-button
              v-if="!sidebarCollapsed"
              class="sidebar-toggle-btn"
              @click="toggleSidebar"
              :icon="Fold"
              circle
              size="large"
            />
          </div>
        </div>

        <!-- 中间区域 - 搜索和导航 -->
        <div class="header-center">
          <div class="global-search">
            <el-autocomplete
              v-model="searchKeyword"
              :fetch-suggestions="querySearchAsync"
              placeholder="全局搜索..."
              :prefix-icon="Search"
              class="search-input"
              clearable
              @select="handleSearchSelect"
              @keyup.enter="handleGlobalSearch"
              :popper-class="'global-search-popper'"
            >
              <template #default="{ item }">
                <div class="search-item">
                  <el-icon class="search-item-icon">
                    <component :is="item.icon" />
                  </el-icon>
                  <div class="search-item-content">
                    <div class="search-item-title">{{ item.title }}</div>
                    <div class="search-item-desc">{{ item.description }}</div>
                  </div>
                  <el-tag size="small" :type="item.type">{{ item.category }}</el-tag>
                </div>
              </template>
            </el-autocomplete>
          </div>
          <div class="breadcrumb-nav">
            <el-breadcrumb separator="/">
              <el-breadcrumb-item :to="{ path: '/' }">
                <el-icon><HomeFilled /></el-icon>
                首页
              </el-breadcrumb-item>
              <el-breadcrumb-item>{{ getCurrentPageName() }}</el-breadcrumb-item>
            </el-breadcrumb>
          </div>
        </div>

        <!-- 右侧区域 -->
        <div class="header-right">
          <!-- 快捷操作 -->
          <div class="quick-actions">
            <el-popover
              placement="bottom"
              :width="380"
              trigger="click"
              popper-class="notification-popover"
            >
              <template #reference>
                <el-tooltip content="消息通知" placement="bottom">
                  <el-badge :value="unreadNotifications" :max="99" class="notification-badge">
                    <el-button class="action-button" circle>
                      <el-icon><Bell /></el-icon>
                    </el-button>
                  </el-badge>
                </el-tooltip>
              </template>

              <div class="notification-panel">
                <div class="notification-header">
                  <div class="notification-title">
                    <span>消息通知</span>
                    <el-badge :value="unreadNotifications" :max="99" />
                    <el-tag
                      :type="notificationState.isConnected ? 'success' : 'danger'"
                      size="small"
                      style="margin-left: 8px;"
                    >
                      {{ notificationState.isConnected ? '已连接' : '未连接' }}
                    </el-tag>
                  </div>
                  <div class="notification-actions">
                    <el-button type="primary" text size="small" @click="addTestNotification">
                      <el-icon><Bell /></el-icon>
                      测试订单
                    </el-button>
                    <el-button type="warning" text size="small" @click="addTestStockNotification">
                      <el-icon><Warning /></el-icon>
                      测试库存
                    </el-button>
                    <el-button text size="small" @click="markAllAsRead">全部已读</el-button>
                    <el-button text size="small" @click="clearAllNotifications">清空</el-button>
                  </div>
                </div>

                <el-scrollbar height="300px" class="notification-list">
                  <div v-if="notifications.length === 0" class="empty-notifications">
                    <el-empty description="暂无通知" :image-size="80" />
                  </div>
                  <div v-else>
                    <div
                      v-for="notification in notifications"
                      :key="notification.id"
                      class="notification-item"
                      :class="{ unread: !notification.read }"
                      @click="handleNotificationClick(notification)"
                    >
                      <div class="notification-icon">
                        <el-icon :color="notification.iconColor">
                          <component :is="notification.icon" />
                        </el-icon>
                      </div>
                      <div class="notification-content">
                        <div class="notification-text">{{ notification.title }}</div>
                        <div class="notification-desc">{{ notification.description }}</div>
                        <div class="notification-time">{{ formatTime(notification.time) }}</div>
                      </div>
                      <div class="notification-status">
                        <el-badge v-if="!notification.read" is-dot />
                      </div>
                    </div>
                  </div>
                </el-scrollbar>

                <div class="notification-footer">
                  <el-button text @click="viewAllNotifications">查看全部通知</el-button>
                </div>
              </div>
            </el-popover>

            <el-tooltip content="全屏显示" placement="bottom">
              <el-button class="action-button" circle @click="toggleFullscreen">
                <el-icon><FullScreen /></el-icon>
              </el-button>
            </el-tooltip>

            <!-- 使用对话框代替抽屉 -->
            <el-dialog
              v-model="settingsDrawerVisible"
              title="系统设置"
              width="600px"
              :modal="true"
              :close-on-click-modal="false"
              :destroy-on-close="false"
              class="settings-dialog"
              :z-index="3000"
              :append-to-body="true"
            >
              <div style="padding: 20px; color: black;">
                <h3 style="color: #333; margin-bottom: 20px;">系统设置</h3>

                <div style="margin-top: 30px;">
                  <h4 style="color: #333; margin-bottom: 15px;">主题设置</h4>
                  <div style="margin-bottom: 15px; display: flex; align-items: center; gap: 10px;">
                    <span style="color: #666;">主题颜色：</span>
                    <el-color-picker
                      v-model="currentThemeColor"
                      @change="onThemeColorChange"
                      show-alpha
                      :predefine="presetColors.map(c => c.value)"
                      size="large"
                    />
                    <span style="color: #999; font-size: 12px;">选择任意颜色</span>
                  </div>

                  <div style="margin-bottom: 15px; display: flex; align-items: center; gap: 10px;">
                    <span style="color: #666;">侧边栏折叠：</span>
                    <el-switch
                      v-model="sidebarCollapsed"
                      active-text="折叠"
                      inactive-text="展开"
                      size="large"
                    />
                  </div>

                  <div style="margin-bottom: 20px;">
                    <h4 style="color: #333; margin-bottom: 15px;">主题颜色</h4>

                    <!-- 预设颜色 -->
                    <div style="margin-bottom: 15px;">
                      <span style="color: #666; display: block; margin-bottom: 8px;">快速选择：</span>
                      <div style="display: flex; gap: 8px; flex-wrap: wrap;">
                        <div
                          v-for="color in presetColors"
                          :key="color.name"
                          @click="selectPresetColor(color.value)"
                          :style="{
                            width: '32px',
                            height: '32px',
                            backgroundColor: color.value,
                            borderRadius: '4px',
                            cursor: 'pointer',
                            border: currentThemeColor === color.value ? '3px solid #409EFF' : (color.value.toUpperCase() === '#FFFFFF' ? '2px solid #ccc' : '2px solid #ddd'),
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            boxShadow: color.value.toUpperCase() === '#FFFFFF' ? '0 1px 3px rgba(0,0,0,0.1)' : 'none'
                          }"
                          :title="color.name"
                        >
                          <el-icon v-if="currentThemeColor === color.value" :style="{
                            color: color.value.toUpperCase() === '#FFFFFF' ? '#333333' : 'white',
                            fontSize: '16px'
                          }">
                            <Check />
                          </el-icon>
                        </div>
                      </div>
                    </div>

                    <!-- 重置按钮 -->
                    <div style="margin-bottom: 15px;">
                      <el-button size="small" @click="resetThemeColor">重置为默认颜色</el-button>
                    </div>
                  </div>
                </div>
              </div>

              <template #footer>
                <span class="dialog-footer">
                  <el-button @click="settingsDrawerVisible = false">取消</el-button>
                  <el-button type="primary" @click="saveSettings">保存设置</el-button>
                </span>
              </template>
            </el-dialog>

            <el-tooltip content="系统设置" placement="bottom">
              <el-button
                class="action-button"
                circle
                @click="openSettings"
                :class="{ 'settings-active': settingsDrawerVisible }"
              >
                <el-icon><Setting /></el-icon>
              </el-button>
            </el-tooltip>
          </div>

          <!-- 用户信息 -->
          <el-dropdown @command="handleCommand" class="user-dropdown" trigger="click">
            <div class="user-profile">
              <el-avatar :size="40" :src="computedUserAvatar" class="user-avatar" @error="handleAvatarError">
                <el-icon><User /></el-icon>
              </el-avatar>
              <div class="user-info">
                <div class="user-name">{{ authStore.user?.username || '管理员' }}</div>
                <div class="user-role">超级管理员</div>
              </div>
              <el-icon class="dropdown-arrow"><ArrowDown /></el-icon>
            </div>
            <template #dropdown>
              <el-dropdown-menu class="modern-dropdown-menu">
                <el-dropdown-item command="profile" class="dropdown-item">
                  <el-icon><User /></el-icon>
                  <span>个人设置</span>
                </el-dropdown-item>
                <el-dropdown-item command="settings" class="dropdown-item">
                  <el-icon><Setting /></el-icon>
                  <span>系统设置</span>
                </el-dropdown-item>
                <el-dropdown-item command="logout" divided class="dropdown-item logout-item">
                  <el-icon><SwitchButton /></el-icon>
                  <span>退出登录</span>
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </el-header>

    <!-- 主体容器 -->
    <el-container class="main-container">
      <!-- 侧边栏 -->
      <el-aside class="modern-sidebar" :class="{ collapsed: sidebarCollapsed }" :width="sidebarWidth">
        <div class="sidebar-wrapper">
          <!-- 菜单导航 -->
          <el-scrollbar class="sidebar-scrollbar">
            <el-menu
              :default-active="activeMenu"
              :collapse="sidebarCollapsed"
              :unique-opened="true"
              router
              class="navigation-menu"
              background-color="transparent"
              text-color="#64748b"
              active-text-color="#3b82f6"
            >
              <!-- 仪表盘 -->
              <el-tooltip
                :content="sidebarCollapsed ? '仪表盘' : ''"
                placement="right"
                :disabled="!sidebarCollapsed"
              >
                <el-menu-item index="/" class="nav-item">
                  <el-icon class="menu-icon"><Odometer /></el-icon>
                  <span class="menu-text">仪表盘</span>
                </el-menu-item>
              </el-tooltip>

              <!-- 用户管理 -->
              <el-tooltip
                :content="sidebarCollapsed ? '用户管理' : ''"
                placement="right"
                :disabled="!sidebarCollapsed"
              >
                <el-menu-item index="/users" class="nav-item">
                  <el-icon class="menu-icon"><User /></el-icon>
                  <span class="menu-text">用户管理</span>
                </el-menu-item>
              </el-tooltip>

              <!-- 商品管理 -->
              <template v-if="!sidebarCollapsed">
                <el-sub-menu index="products" class="nav-submenu">
                  <template #title>
                    <el-icon class="menu-icon"><Goods /></el-icon>
                    <span class="menu-text">商品管理</span>
                  </template>
                  <el-menu-item index="/flowers" class="sub-nav-item">
                    <span class="submenu-text">商品列表</span>
                  </el-menu-item>
                  <el-menu-item index="/categories" class="sub-nav-item">
                    <span class="submenu-text">分类管理</span>
                  </el-menu-item>
                  <el-menu-item index="/price-categories" class="sub-nav-item">
                    <span class="submenu-text">价格分类</span>
                  </el-menu-item>
                </el-sub-menu>
              </template>

              <!-- 折叠状态下的商品管理菜单项 -->
              <template v-else>
                <el-tooltip content="商品列表" placement="right">
                  <el-menu-item index="/flowers" class="nav-item">
                    <el-icon class="menu-icon"><Goods /></el-icon>
                    <span class="menu-text">商品列表</span>
                  </el-menu-item>
                </el-tooltip>

                <el-tooltip content="分类管理" placement="right">
                  <el-menu-item index="/categories" class="nav-item">
                    <el-icon class="menu-icon"><FolderOpened /></el-icon>
                    <span class="menu-text">分类管理</span>
                  </el-menu-item>
                </el-tooltip>

                <el-tooltip content="价格分类" placement="right">
                  <el-menu-item index="/price-categories" class="nav-item">
                    <el-icon class="menu-icon"><Money /></el-icon>
                    <span class="menu-text">价格分类</span>
                  </el-menu-item>
                </el-tooltip>
              </template>

              <!-- 订单管理 -->
              <el-tooltip
                :content="sidebarCollapsed ? '订单管理' : ''"
                placement="right"
                :disabled="!sidebarCollapsed"
              >
                <el-menu-item index="/orders" class="nav-item">
                  <el-icon class="menu-icon"><ShoppingCart /></el-icon>
                  <span class="menu-text">订单管理</span>
                </el-menu-item>
              </el-tooltip>

              <!-- 评价管理 -->
              <el-tooltip
                :content="sidebarCollapsed ? '评价管理' : ''"
                placement="right"
                :disabled="!sidebarCollapsed"
              >
                <el-menu-item index="/reviews" class="nav-item">
                  <el-icon class="menu-icon"><ChatDotRound /></el-icon>
                  <span class="menu-text">评价管理</span>
                </el-menu-item>
              </el-tooltip>

              <!-- 地址管理 -->
              <el-tooltip
                :content="sidebarCollapsed ? '地址管理' : ''"
                placement="right"
                :disabled="!sidebarCollapsed"
              >
                <el-menu-item index="/addresses" class="nav-item">
                  <el-icon class="menu-icon"><Location /></el-icon>
                  <span class="menu-text">地址管理</span>
                </el-menu-item>
              </el-tooltip>

              <!-- 后端用户管理 -->
              <el-tooltip
                :content="sidebarCollapsed ? '后端用户' : ''"
                placement="right"
                :disabled="!sidebarCollapsed"
              >
                <el-menu-item index="/admin-users" class="nav-item">
                  <el-icon class="menu-icon"><UserFilled /></el-icon>
                  <span class="menu-text">后端用户</span>
                </el-menu-item>
              </el-tooltip>

              <!-- 轮播图管理 -->
              <el-tooltip
                :content="sidebarCollapsed ? '轮播图管理' : ''"
                placement="right"
                :disabled="!sidebarCollapsed"
              >
                <el-menu-item index="/swiper" class="nav-item">
                  <el-icon class="menu-icon"><Picture /></el-icon>
                  <span class="menu-text">轮播图管理</span>
                </el-menu-item>
              </el-tooltip>
            </el-menu>
          </el-scrollbar>
        </div>
      </el-aside>

      <!-- 主内容区域 -->
      <el-main class="modern-main">
        <div class="content-wrapper">
          <router-view />
        </div>
      </el-main>
    </el-container>
  </el-container>
</template>

<script setup>
import { ref, computed, watch, onMounted, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { ElMessageBox, ElMessage } from 'element-plus'
import {
  Search,
  Bell,
  FullScreen,
  Setting,
  User,
  ArrowDown,
  SwitchButton,
  HomeFilled,
  Expand,
  Fold,
  Menu,
  FolderOpened,
  Odometer,
  Goods,
  ShoppingCart,
  ChatDotRound,
  Location,
  Money,
  Warning,
  CircleCheck,
  Brush,
  Grid,
  Tools,
  Cpu,
  RefreshLeft,
  Download,
  Check,
  Picture
} from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

const sidebarCollapsed = ref(false)
const userAvatar = ref('')
const avatarTimestamp = ref(Date.now())

// 计算属性 - 用户头像URL（带时间戳防止缓存）
const computedUserAvatar = computed(() => {
  const avatar = authStore.user?.avatar || userAvatar.value
  if (!avatar) return ''

  // 如果URL已经包含时间戳，直接返回
  if (avatar.includes('?t=')) {
    return avatar
  }

  // 添加时间戳防止缓存
  return avatar + '?t=' + avatarTimestamp.value
})

// 全局搜索相关
const searchKeyword = ref('')
const searchResults = ref([])

// 通知相关 - 使用通知服务
import {
  initNotificationService,
  notificationState,
  markNotificationAsRead,
  markAllNotificationsAsRead,
  clearAllNotifications as clearAllNotificationsService,
  sendTestNotification,
  sendTestStockNotification
} from '@/services/notificationService'

// 通知数据从服务中获取
const notifications = computed(() => notificationState.notifications)

// 设置相关
const settingsDrawerVisible = ref(false)
const isDarkMode = ref(false)
const currentThemeColor = ref('#FFFFFF')
const customThemeColor = ref('#FFFFFF')
const fixedHeader = ref(true)
const pageAnimation = ref(true)
const notificationSound = ref(true)

// 新增设置项
const pageWidth = ref('fluid')
const navStyle = ref('side')
const autoSave = ref(true)
const refreshInterval = ref(30)
const lazyLoading = ref(true)
const virtualScroll = ref(false)
const cacheStrategy = ref('memory')

const presetColors = [
  { name: '默认白', value: '#FFFFFF' },
  { name: '默认蓝', value: '#409EFF' },
  { name: '成功绿', value: '#67C23A' },
  { name: '警告橙', value: '#E6A23C' },
  { name: '危险红', value: '#F56C6C' },
  { name: '信息灰', value: '#909399' },
  { name: '紫色', value: '#722ED1' },
  { name: '青色', value: '#13C2C2' },
  { name: '粉色', value: '#EB2F96' },
  { name: '深蓝', value: '#1976D2' },
  { name: '橙色', value: '#FF9800' },
  { name: '深绿', value: '#388E3C' },
  { name: '棕色', value: '#795548' }
]

// 预定义颜色
const predefineColors = [
  '#ff4500',
  '#ff8c00',
  '#ffd700',
  '#90ee90',
  '#00ced1',
  '#1e90ff',
  '#c71585',
  '#409EFF',
  '#67C23A',
  '#E6A23C',
  '#F56C6C',
  '#909399'
]

const sidebarWidth = computed(() => {
  return sidebarCollapsed.value ? '80px' : '260px'
})

const activeMenu = computed(() => {
  return route.path
})

// 未读通知数量
const unreadNotifications = computed(() => {
  return notificationState.unreadCount
})

// 切换侧边栏
const toggleSidebar = () => {
  sidebarCollapsed.value = !sidebarCollapsed.value
}

// 获取当前页面名称
const getCurrentPageName = () => {
  const routeMap = {
    '/': '仪表盘',
    '/users': '用户管理',
    '/flowers': '商品列表',
    '/categories': '分类管理',
    '/price-categories': '价格分类管理',
    '/orders': '订单管理',
    '/reviews': '评价管理',
    '/addresses': '地址管理',
    '/admin-users': '后端用户管理',
    '/profile': '个人设置'
  }
  return routeMap[route.path] || '未知页面'
}

// 全屏切换
const toggleFullscreen = () => {
  if (!document.fullscreenElement) {
    document.documentElement.requestFullscreen()
    ElMessage.success('已进入全屏模式')
  } else {
    document.exitFullscreen()
    ElMessage.success('已退出全屏模式')
  }
}

// 刷新页面
const refreshPage = () => {
  window.location.reload()
}

// 全局搜索相关方法
const querySearchAsync = (queryString, callback) => {
  if (!queryString) {
    callback([])
    return
  }

  // 模拟搜索结果
  const searchItems = [
    {
      title: '商品管理',
      description: '管理商品信息、库存等',
      category: '菜单',
      type: 'primary',
      icon: 'Goods',
      path: '/flowers'
    },
    {
      title: '订单管理',
      description: '查看和处理订单',
      category: '菜单',
      type: 'success',
      icon: 'ShoppingCart',
      path: '/orders'
    },
    {
      title: '用户管理',
      description: '管理用户信息',
      category: '菜单',
      type: 'warning',
      icon: 'User',
      path: '/users'
    },
    {
      title: '分类管理',
      description: '管理商品分类',
      category: '菜单',
      type: 'info',
      icon: 'FolderOpened',
      path: '/categories'
    }
  ]

  const results = searchItems.filter(item =>
    item.title.toLowerCase().includes(queryString.toLowerCase()) ||
    item.description.toLowerCase().includes(queryString.toLowerCase())
  )

  callback(results)
}

const handleSearchSelect = (item) => {
  if (item.path) {
    router.push(item.path)
    searchKeyword.value = ''
  }
}

const handleGlobalSearch = () => {
  if (searchKeyword.value.trim()) {
    ElMessage.info(`搜索: ${searchKeyword.value}`)
    // 这里可以实现全局搜索逻辑
  }
}

// 通知相关方法
const handleNotificationClick = (notification) => {
  markNotificationAsRead(notification.id)

  // 根据通知类型跳转到相应页面
  switch (notification.type) {
    case 'order':
      if (notification.data && notification.data.id) {
        router.push(`/orders/${notification.data.id}`)
      } else {
        router.push('/orders')
      }
      break
    case 'order_status':
      if (notification.data && notification.data.id) {
        router.push(`/orders/${notification.data.id}`)
      } else {
        router.push('/orders')
      }
      break
    case 'stock':
      router.push('/flowers')
      break
    case 'system':
      // 系统通知不跳转
      break
  }
}

const markAllAsRead = () => {
  markAllNotificationsAsRead()
  ElMessage.success('所有通知已标记为已读')
}

const clearNotifications = () => {
  clearAllNotificationsService()
  ElMessage.success('所有通知已清空')
}

const viewAllNotifications = () => {
  ElMessage.info('通知中心功能开发中')
}

const formatTime = (time) => {
  const now = new Date()
  const diff = now - time
  const minutes = Math.floor(diff / 60000)
  const hours = Math.floor(diff / 3600000)
  const days = Math.floor(diff / 86400000)

  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`
  if (hours < 24) return `${hours}小时前`
  return `${days}天前`
}

// 设置相关方法
const openSettings = () => {
  console.log('打开设置对话框')
  settingsDrawerVisible.value = true
  console.log('settingsDrawerVisible:', settingsDrawerVisible.value)
}

const testClick = () => {
  ElMessage.success('测试按钮点击成功！')
}

const toggleTheme = () => {
  // 切换主题模式
  document.documentElement.classList.toggle('dark', isDarkMode.value)
  ElMessage.success(`已切换到${isDarkMode.value ? '深色' : '浅色'}模式`)
}

const changeThemeColor = (color) => {
  currentThemeColor.value = color
  customThemeColor.value = color
  // 更新CSS变量
  document.documentElement.style.setProperty('--el-color-primary', color)
  ElMessage.success('主题色已更新')
}

// 处理自定义颜色变化
const handleCustomColorChange = (color) => {
  if (color) {
    changeThemeColor(color)
  }
}

// 格式化刷新间隔提示
const formatRefreshTooltip = (value) => {
  return `${value}秒`
}

const resetSettings = () => {
  isDarkMode.value = false
  currentThemeColor.value = '#409EFF'
  customThemeColor.value = '#409EFF'
  fixedHeader.value = true
  pageAnimation.value = true
  notificationSound.value = true
  pageWidth.value = 'fluid'
  navStyle.value = 'side'
  autoSave.value = true
  refreshInterval.value = 30
  lazyLoading.value = true
  virtualScroll.value = false
  cacheStrategy.value = 'memory'

  // 重置CSS
  document.documentElement.classList.remove('dark')
  document.documentElement.style.setProperty('--el-color-primary', '#409EFF')

  ElMessage.success('设置已重置')
}

// 颜色选择相关方法
const onThemeColorChange = (color) => {
  if (color) {
    currentThemeColor.value = color
    customThemeColor.value = color
    applyThemeColor(color)
    ElMessage.success('主题颜色已更新')
  }
}

const selectPresetColor = (color) => {
  currentThemeColor.value = color
  customThemeColor.value = color
  applyThemeColor(color)
  ElMessage.success(`已应用${presetColors.find(c => c.value === color)?.name || ''}主题色`)
}

const selectCustomColor = (color) => {
  if (color) {
    customThemeColor.value = color
  }
}

const applyCustomColor = () => {
  if (customThemeColor.value) {
    currentThemeColor.value = customThemeColor.value
    applyThemeColor(customThemeColor.value)
    ElMessage.success('已应用自定义主题色')
  }
}

const applyThemeColor = (color) => {
  // 应用主题色到CSS变量
  document.documentElement.style.setProperty('--el-color-primary', color)
  document.documentElement.style.setProperty('--el-color-primary-light-3', color + '4D')
  document.documentElement.style.setProperty('--el-color-primary-light-5', color + '80')
  document.documentElement.style.setProperty('--el-color-primary-light-7', color + 'B3')
  document.documentElement.style.setProperty('--el-color-primary-light-8', color + 'CC')
  document.documentElement.style.setProperty('--el-color-primary-light-9', color + 'E6')
  document.documentElement.style.setProperty('--el-color-primary-dark-2', color)

  // 应用到导航栏和头部
  const headerElement = document.querySelector('.modern-header')
  const sidebarElement = document.querySelector('.modern-sidebar')

  // 判断是否为白色主题，需要特殊处理
  const isWhiteTheme = color.toUpperCase() === '#FFFFFF' || color.toUpperCase() === '#FFF'

  if (headerElement) {
    if (isWhiteTheme) {
      headerElement.style.background = 'linear-gradient(135deg, #FFFFFF 0%, #F5F5F5 100%)'
      headerElement.style.borderBottom = '1px solid #E0E0E0'
      headerElement.style.color = '#333333'
    } else {
      headerElement.style.background = `linear-gradient(135deg, ${color} 0%, ${color}CC 100%)`
      headerElement.style.borderBottom = 'none'
      headerElement.style.color = '#FFFFFF'
    }
  }

  if (sidebarElement) {
    if (isWhiteTheme) {
      sidebarElement.style.background = 'linear-gradient(180deg, #FFFFFF 0%, #FAFAFA 100%)'
      sidebarElement.style.borderRight = '1px solid #E0E0E0'
      sidebarElement.style.color = '#333333'
    } else {
      sidebarElement.style.background = `linear-gradient(180deg, ${color} 0%, ${color}E6 100%)`
      sidebarElement.style.borderRight = 'none'
      sidebarElement.style.color = '#FFFFFF'
    }
  }
}

const resetThemeColor = () => {
  const defaultColor = '#FFFFFF'
  currentThemeColor.value = defaultColor
  customThemeColor.value = defaultColor
  applyThemeColor(defaultColor)
  ElMessage.success('已重置为默认主题色')
}

const saveSettings = () => {
  // 保存设置到localStorage
  const settings = {
    isDarkMode: isDarkMode.value,
    currentThemeColor: currentThemeColor.value,
    customThemeColor: customThemeColor.value,
    sidebarCollapsed: sidebarCollapsed.value,
    fixedHeader: fixedHeader.value,
    pageAnimation: pageAnimation.value,
    notificationSound: notificationSound.value,
    pageWidth: pageWidth.value,
    navStyle: navStyle.value,
    autoSave: autoSave.value,
    refreshInterval: refreshInterval.value,
    lazyLoading: lazyLoading.value,
    virtualScroll: virtualScroll.value,
    cacheStrategy: cacheStrategy.value
  }

  localStorage.setItem('adminSettings', JSON.stringify(settings))
  ElMessage.success('设置已保存')
  settingsDrawerVisible.value = false
}

// 导出设置
const exportSettings = () => {
  const settings = {
    isDarkMode: isDarkMode.value,
    currentThemeColor: currentThemeColor.value,
    customThemeColor: customThemeColor.value,
    sidebarCollapsed: sidebarCollapsed.value,
    fixedHeader: fixedHeader.value,
    pageAnimation: pageAnimation.value,
    notificationSound: notificationSound.value,
    pageWidth: pageWidth.value,
    navStyle: navStyle.value,
    autoSave: autoSave.value,
    refreshInterval: refreshInterval.value,
    lazyLoading: lazyLoading.value,
    virtualScroll: virtualScroll.value,
    cacheStrategy: cacheStrategy.value,
    exportTime: new Date().toISOString()
  }

  const dataStr = JSON.stringify(settings, null, 2)
  const dataBlob = new Blob([dataStr], { type: 'application/json' })
  const url = URL.createObjectURL(dataBlob)

  const link = document.createElement('a')
  link.href = url
  link.download = `花语小铺-系统设置-${new Date().toISOString().split('T')[0]}.json`
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(url)

  ElMessage.success('设置配置已导出')
}

// 头像错误处理
const handleAvatarError = (e) => {
  console.error('顶部导航栏头像加载失败:', e)
  console.log('头像URL:', computedUserAvatar.value)

  // 尝试刷新时间戳
  avatarTimestamp.value = Date.now()
}

// 初始化用户头像
const initUserAvatar = async () => {
  try {
    // 如果authStore中没有用户头像信息，尝试从API获取
    if (!authStore.user?.avatar) {
      // 这里可以调用API获取最新的用户信息
      // const response = await adminApi.getProfile()
      // if (response.code === 200) {
      //   authStore.updateUser({ avatar: response.data.avatar })
      // }
    }
    userAvatar.value = authStore.user?.avatar || ''
  } catch (error) {
    console.error('初始化用户头像失败:', error)
  }
}

// 处理用户下拉菜单命令
const handleCommand = async (command) => {
  switch (command) {
    case 'profile':
      // 跳转到个人设置页面
      router.push('/profile')
      break
    case 'settings':
      openSettings()
      break
    case 'logout':
      try {
        await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        await authStore.logout()
        router.push('/login')
        ElMessage.success('退出登录成功')
      } catch (error) {
        // 用户取消操作
      }
      break
  }
}

// 初始化设置
const initSettings = () => {
  const savedSettings = localStorage.getItem('adminSettings')
  if (savedSettings) {
    try {
      const settings = JSON.parse(savedSettings)
      isDarkMode.value = settings.isDarkMode || false
      currentThemeColor.value = settings.currentThemeColor || '#FFFFFF'
      customThemeColor.value = settings.customThemeColor || '#FFFFFF'
      sidebarCollapsed.value = settings.sidebarCollapsed || false
      fixedHeader.value = settings.fixedHeader !== undefined ? settings.fixedHeader : true
      pageAnimation.value = settings.pageAnimation !== undefined ? settings.pageAnimation : true
      notificationSound.value = settings.notificationSound !== undefined ? settings.notificationSound : true

      // 新增设置项
      pageWidth.value = settings.pageWidth || 'fluid'
      navStyle.value = settings.navStyle || 'side'
      autoSave.value = settings.autoSave !== undefined ? settings.autoSave : true
      refreshInterval.value = settings.refreshInterval || 30
      lazyLoading.value = settings.lazyLoading !== undefined ? settings.lazyLoading : true
      virtualScroll.value = settings.virtualScroll || false
      cacheStrategy.value = settings.cacheStrategy || 'memory'

      // 应用主题设置
      document.documentElement.classList.toggle('dark', isDarkMode.value)
      applyThemeColor(currentThemeColor.value)

      // 应用布局设置
      applyLayoutSettings()
    } catch (error) {
      console.error('加载设置失败:', error)
    }
  }
}

// 应用布局设置
const applyLayoutSettings = () => {
  // 应用页面宽度设置
  const mainContainer = document.querySelector('.main-container')
  if (mainContainer) {
    if (pageWidth.value === 'fixed') {
      mainContainer.style.maxWidth = '1200px'
      mainContainer.style.margin = '0 auto'
    } else {
      mainContainer.style.maxWidth = 'none'
      mainContainer.style.margin = '0'
    }
  }

  // 应用其他布局设置...
}

// 监听路由变化，更新页面标题
watch(
  () => route.meta.title,
  (title) => {
    if (title) {
      document.title = `${title} - 花语小铺管理后台`
    }
  },
  { immediate: true }
)

// 组件挂载时初始化
onMounted(() => {
  initSettings()
  initUserAvatar()
  // 初始化通知服务
  initNotificationService()
})

// 添加测试通知功能（开发环境使用）
const addTestNotification = () => {
  sendTestNotification()
}

const addTestStockNotification = () => {
  sendTestStockNotification()
}

// 监听authStore中用户信息的变化
watch(
  () => authStore.user?.avatar,
  (newAvatar) => {
    if (newAvatar) {
      userAvatar.value = newAvatar
      avatarTimestamp.value = Date.now() // 刷新时间戳
    }
  },
  { immediate: true }
)
</script>

<style scoped>
/* 现代化布局容器 */
.modern-layout {
  height: 100vh;
  background: #f8fafc;
  overflow: hidden;
}

/* 现代化头部 */
.modern-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid #e2e8f0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1000;
  padding: 0;
}

.header-wrapper {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  max-width: 100%;
}

/* 头部左侧 - 品牌区域 */
.header-left {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.brand-section {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 1;
}

.brand-logo {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.logo-icon {
  font-size: 32px;
  animation: float 3s ease-in-out infinite;
  flex-shrink: 0;
}

.brand-info {
  display: flex;
  flex-direction: column;
}

.brand-title {
  font-size: 20px;
  font-weight: 700;
  color: #1e293b;
  margin: 0;
  line-height: 1.2;
}

.brand-subtitle {
  font-size: 12px;
  color: #64748b;
  font-weight: 500;
}

.sidebar-toggle-btn {
  background: #f1f5f9;
  border: 1px solid #e2e8f0;
  color: #475569;
  transition: all 0.3s ease;
}

.sidebar-toggle-btn:hover {
  background: #e2e8f0;
  color: #334155;
  transform: scale(1.05);
}

/* 头部中间 - 搜索和导航 */
.header-center {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 24px;
  margin: 0 32px;
}

.global-search {
  max-width: 400px;
  width: 100%;
}

.search-input {
  border-radius: 12px;
}

.search-input .el-input__wrapper {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.search-input .el-input__wrapper:hover {
  border-color: #cbd5e1;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.search-input .el-input__wrapper.is-focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 全局搜索下拉样式 */
.search-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  gap: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 8px;
  margin: 4px 8px;
}

.search-item:hover {
  background: #f5f7fa;
}

.search-item-icon {
  color: #409EFF;
  font-size: 16px;
  flex-shrink: 0;
}

.search-item-content {
  flex: 1;
  min-width: 0;
}

.search-item-title {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 2px;
}

.search-item-desc {
  font-size: 12px;
  color: #909399;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.breadcrumb-nav {
  display: flex;
  align-items: center;
}

.breadcrumb-nav .el-breadcrumb {
  font-weight: 500;
}

.breadcrumb-nav .el-breadcrumb__item {
  color: #64748b;
}

.breadcrumb-nav .el-breadcrumb__item.is-link {
  color: #3b82f6;
}

/* 头部右侧 */
.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-shrink: 0;
}

.quick-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.action-button {
  width: 40px;
  height: 40px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  color: #64748b;
  transition: all 0.3s ease;
}

.action-button:hover {
  background: #e2e8f0;
  color: #475569;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.action-button.settings-active {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.notification-badge {
  position: relative;
}

.notification-badge .el-badge__content {
  background: #ef4444;
  border: 2px solid white;
}

/* 通知面板样式 */
.notification-panel {
  padding: 0;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
}

.notification-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.notification-actions {
  display: flex;
  gap: 8px;
}

.notification-list {
  max-height: 300px;
}

.empty-notifications {
  padding: 40px 20px;
  text-align: center;
}

.notification-item {
  display: flex;
  align-items: flex-start;
  padding: 16px 20px;
  gap: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 1px solid #f5f5f5;
}

.notification-item:hover {
  background: #f8f9fa;
}

.notification-item.unread {
  background: #f0f9ff;
  border-left: 3px solid #409EFF;
}

.notification-icon {
  flex-shrink: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: #f5f7fa;
}

.notification-content {
  flex: 1;
  min-width: 0;
}

.notification-text {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.notification-desc {
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
  line-height: 1.4;
}

.notification-time {
  font-size: 11px;
  color: #c0c4cc;
}

.notification-status {
  flex-shrink: 0;
  margin-top: 2px;
}

.notification-footer {
  padding: 12px 20px;
  border-top: 1px solid #f0f0f0;
  text-align: center;
}

/* 用户信息区域 */
.user-dropdown {
  cursor: pointer;
}

.user-profile {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 16px;
  border-radius: 12px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

.user-profile:hover {
  background: #e2e8f0;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.user-avatar {
  border: 2px solid #3b82f6;
  transition: all 0.3s ease;
}

.user-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.user-name {
  font-size: 14px;
  font-weight: 600;
  color: #1e293b;
  line-height: 1.2;
}

.user-role {
  font-size: 12px;
  color: #64748b;
  line-height: 1.2;
}

.dropdown-arrow {
  color: #64748b;
  transition: transform 0.3s ease;
}

.user-profile:hover .dropdown-arrow {
  transform: rotate(180deg);
}

/* 下拉菜单样式 */
.modern-dropdown-menu {
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
  background: white;
  padding: 8px;
  min-width: 200px;
}

.dropdown-item {
  border-radius: 8px;
  margin: 2px 0;
  padding: 12px 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  transition: all 0.3s ease;
  color: #475569;
}

.dropdown-item:hover {
  background: #f1f5f9;
  color: #1e293b;
}

.dropdown-item.logout-item:hover {
  background: #fef2f2;
  color: #dc2626;
}

/* 设置抽屉样式 */
.settings-drawer {
  z-index: 3000 !important;
}

.settings-drawer .el-drawer__header {
  padding: 20px 20px 10px 20px;
  margin-bottom: 0;
  border-bottom: 1px solid #f0f0f0;
  background: #fff !important;
}

.settings-drawer .el-drawer__body {
  padding: 0 !important;
  background: #fff !important;
  height: 100% !important;
}

.settings-content {
  padding: 0 !important;
  height: 100% !important;
  overflow-y: auto;
  background: #fff !important;
  min-height: 500px !important;
}

.settings-section {
  margin-bottom: 32px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.section-title .el-icon {
  color: #409EFF;
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f5f5f5;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-label {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
  min-width: 100px;
}

.color-picker-group {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.color-option {
  width: 28px;
  height: 28px;
  border-radius: 6px;
  cursor: pointer;
  border: 2px solid transparent;
  transition: all 0.3s ease;
  position: relative;
}

.color-option:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.color-option.active {
  border-color: #303133;
  box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.3);
}

.color-option.active::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: bold;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.settings-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  padding-top: 20px;
  border-top: 1px solid #f0f0f0;
  margin-top: 20px;
}

.settings-actions .el-button {
  border-radius: 8px;
  font-weight: 500;
}

/* 滑块样式优化 */
.setting-item .el-slider {
  flex: 1;
  margin-left: 20px;
}

/* 单选按钮组样式优化 */
.setting-item .el-radio-group {
  flex: 1;
  display: flex;
  justify-content: flex-end;
}

.setting-item .el-radio-button {
  border-radius: 6px !important;
}

/* 颜色选择器样式 */
.setting-item .el-color-picker {
  margin-left: auto;
}

/* 开关样式优化 */
.setting-item .el-switch {
  margin-left: auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .settings-drawer {
    width: 100% !important;
  }

  .setting-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .setting-label {
    min-width: auto;
  }

  .setting-item .el-slider,
  .setting-item .el-radio-group,
  .setting-item .el-color-picker,
  .setting-item .el-switch {
    margin-left: 0;
    width: 100%;
  }
}

/* 主容器 */
.main-container {
  flex: 1;
  overflow: hidden;
}

/* 现代化侧边栏 */
.modern-sidebar {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-right: 1px solid #e2e8f0;
  transition: width 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  overflow: hidden;
  will-change: width;
}

.modern-sidebar.collapsed {
  width: 80px !important;
}



/* 折叠状态下的品牌logo */
.brand-logo.collapsed {
  justify-content: center;
  gap: 8px;
  padding-left: 0;
}

.brand-logo.collapsed .logo-icon {
  margin-right: 0;
}

/* 折叠状态下的按钮样式 */
.collapsed-btn {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  color: #64748b;
  transition: all 0.2s;
  width: 28px !important;
  height: 28px !important;
  min-height: 28px !important;
}

.collapsed-btn:hover {
  background: #3b82f6;
  border-color: #3b82f6;
  color: white;
}

.sidebar-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.sidebar-scrollbar {
  flex: 1;
  padding: 16px 0;
  transition: padding 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  will-change: padding;
}

/* 折叠状态下的滚动区域 */
.modern-sidebar.collapsed .sidebar-scrollbar {
  padding: 16px 0;
  display: flex;
  justify-content: center;
}

/* 导航菜单样式 */
.navigation-menu {
  border: none;
  background: transparent;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  will-change: transform;
}

/* 折叠状态下的菜单容器 */
.modern-sidebar.collapsed .navigation-menu {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* 折叠状态下的菜单项 */
.modern-sidebar.collapsed .nav-item {
  margin: 4px 8px;
  width: 48px;
  height: 48px;
  padding: 12px;
  justify-content: center;
}

.modern-sidebar.collapsed .nav-item .menu-icon {
  margin-right: 0;
  width: 24px;
  height: 24px;
}

.modern-sidebar.collapsed .nav-item .menu-text {
  display: none;
}

.nav-item {
  margin: 4px 16px;
  border-radius: 12px;
  background: transparent;
  border: none;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  overflow: hidden;
  display: flex;
  align-items: center;
  padding: 12px 16px;
  height: 48px;
  min-height: 48px;
  will-change: transform, background, margin, width, padding;
}

.nav-item:hover {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  transform: translateX(4px);
  box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);
}

.nav-item.is-active {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);
}

.menu-icon {
  font-size: 18px;
  flex-shrink: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  will-change: margin, width, height, font-size;
}

.menu-text {
  font-weight: 500;
  font-size: 14px;
  line-height: 1.4;
  flex: 1;
  transition: opacity 0.2s cubic-bezier(0.25, 0.8, 0.25, 1);
  will-change: opacity;
}

/* 子菜单样式 */
.nav-submenu {
  margin: 4px 16px;
  transition: margin 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  will-change: margin;
}

.nav-submenu .el-sub-menu__title {
  border-radius: 12px;
  background: transparent;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  padding: 12px 16px;
  height: 48px;
  min-height: 48px;
  display: flex;
  align-items: center;
  will-change: padding, justify-content;
}

.nav-submenu .el-sub-menu__title .menu-icon {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  will-change: margin;
}

.nav-submenu .el-sub-menu__title .menu-text {
  flex: 1;
  line-height: 1.4;
  transition: opacity 0.2s cubic-bezier(0.25, 0.8, 0.25, 1);
  will-change: opacity;
}

.nav-submenu .el-sub-menu__title:hover {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
}

.sub-nav-item {
  margin: 2px 0;
  border-radius: 8px;
  background: transparent;
}

.sub-nav-item:hover {
  background: #f1f5f9;
  color: #3b82f6;
}

.sub-nav-item.is-active {
  background: #dbeafe;
  color: #1d4ed8;
}

.submenu-item-content {
  padding: 8px 16px;
}

.submenu-text {
  font-size: 13px;
  font-weight: 500;
}

/* 主内容区域 */
.modern-main {
  background: transparent;
  padding: 24px;
  overflow-y: auto;
}

.content-wrapper {
  max-width: 100%;
  margin: 0 auto;
}

/* 浮动动画 */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-6px);
  }
}

/* 优化的折叠状态样式 */
.modern-sidebar.collapsed .nav-item {
  margin: 4px 8px;
  width: 48px;
  height: 48px;
  padding: 12px;
  justify-content: center;
}

.modern-sidebar.collapsed .nav-item .menu-icon {
  margin-right: 0;
  width: 24px;
  height: 24px;
  font-size: 20px;
}

.modern-sidebar.collapsed .nav-item .menu-text {
  opacity: 0;
  width: 0;
  overflow: hidden;
}

.modern-sidebar.collapsed .nav-submenu .el-sub-menu__title {
  justify-content: center;
  padding: 12px;
}

.modern-sidebar.collapsed .nav-submenu .el-sub-menu__title .menu-text {
  opacity: 0;
  width: 0;
  overflow: hidden;
}



/* 响应式设计 */
@media (max-width: 1024px) {
  .header-center {
    margin: 0 16px;
  }

  .global-search {
    max-width: 300px;
  }
}

@media (max-width: 768px) {
  .header-wrapper {
    padding: 0 16px;
  }

  .header-center {
    display: none;
  }

  .brand-title {
    font-size: 18px;
  }

  .modern-main {
    padding: 16px;
  }
}

@media (max-width: 480px) {
  .header-wrapper {
    padding: 0 12px;
  }

  .brand-info {
    display: none;
  }

  .quick-actions {
    gap: 4px;
  }

  .action-button {
    width: 36px;
    height: 36px;
  }

  .modern-main {
    padding: 12px;
  }
}

/* 主体布局 */
.layout-main {
  flex: 1;
  display: flex;
  overflow: hidden;
  padding: 20px;
  gap: 20px;
}

/* 侧边栏样式 */
.layout-sidebar {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
}

.layout-sidebar.collapsed {
  width: 64px !important;
}

.layout-sidebar.collapsed .sidebar-content {
  padding: 16px 8px;
}

.sidebar-content {
  height: 100%;
  padding: 16px 0;
  transition: padding 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar-menu {
  border: none;
  background: transparent;
  height: 100%;
  overflow: hidden;
}

/* 内容区域 */
.layout-content {
  flex: 1;
  background: transparent;
  overflow-y: auto;
  border-radius: 16px;
  padding-left: 0;
  margin-left: -8px;
}

/* 动画效果 */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-6px);
  }
}

/* 深度选择器样式 */
:deep(.el-menu) {
  border: none;
  background: transparent;
}

:deep(.el-menu-item) {
  margin: 3px 12px;
  border-radius: 10px;
  background: transparent;
  border: none;
  color: #5a6c7d;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  height: 44px;
  line-height: 44px;
  position: relative;
  overflow: hidden;
}

:deep(.el-menu--collapse .el-menu-item) {
  margin: 6px auto !important;
  padding: 0 !important;
  text-align: center;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  height: 48px !important;
  line-height: 48px !important;
  border-radius: 12px !important;
  width: 48px !important;
}

/* 强制显示折叠状态下的图标 */
:deep(.el-menu--collapse .el-menu-item .el-icon) {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  margin: 0 !important;
  font-size: 24px !important;
  color: #64748b !important;
  width: 24px !important;
  height: 24px !important;
}

/* 只在折叠状态下调整SVG图标位置和大小 */
:deep(.el-menu--collapse .el-icon svg) {
  margin-left: 60% !important;
  width: 24px !important;
  height: 24px !important;
}

:deep(.el-menu--collapse .el-menu-item:hover .el-icon) {
  color: white !important;
}

:deep(.el-menu--collapse .el-menu-item.is-active .el-icon) {
  color: white !important;
}

/* 隐藏折叠状态下的文本 */
:deep(.el-menu--collapse .el-menu-item .menu-text) {
  display: none !important;
}

:deep(.el-menu-item:hover) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  transform: translateX(2px);
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.25);
}

:deep(.el-menu--collapse .el-menu-item:hover) {
  transform: translateX(0) scale(1.02);
}

/* 额外的图标显示保障 */
:deep(.el-menu.el-menu--collapse) {
  width: 80px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

:deep(.el-menu.el-menu--collapse .el-menu-item) {
  height: 48px !important;
  line-height: 48px !important;
  padding: 0 !important;
  margin: 6px auto !important;
  width: 48px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  border-radius: 12px !important;
}

:deep(.el-menu.el-menu--collapse .el-menu-item > *) {
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  width: 100% !important;
}

/* 确保所有图标元素都显示 */
:deep(.el-menu.el-menu--collapse .el-icon),
:deep(.el-menu.el-menu--collapse i),
:deep(.el-menu.el-menu--collapse svg) {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  font-size: 24px !important;
  width: 24px !important;
  height: 24px !important;
}

/* 强制覆盖Element Plus的默认样式 */
:deep(.el-menu.el-menu--collapse) {
  width: 80px !important;
  padding: 0 !important;
}

:deep(.el-menu.el-menu--collapse .el-menu-item) {
  margin-left: auto !important;
  margin-right: auto !important;
  left: 0 !important;
  right: 0 !important;
  position: relative !important;
}

/* 优化的tooltip包装器 */
:deep(.modern-sidebar.collapsed .el-tooltip__trigger) {
  width: 48px !important;
  display: flex !important;
  justify-content: center !important;
  margin: 4px 8px !important;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1) !important;
}

:deep(.modern-sidebar.collapsed .el-tooltip__trigger .el-menu-item) {
  width: 48px !important;
  margin: 0 !important;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1) !important;
}

:deep(.el-menu-item.is-active) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
  transform: translateX(2px);
}

/* 性能优化 - 启用硬件加速 */
.modern-sidebar,
.navigation-menu,
.nav-item,
.menu-icon,
.menu-text {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* 减少重绘和回流 */
.modern-sidebar * {
  box-sizing: border-box;
}

/* 优化动画性能 */
@media (prefers-reduced-motion: no-preference) {
  .modern-sidebar {
    transition: width 0.25s cubic-bezier(0.25, 0.8, 0.25, 1);
  }

  .nav-item {
    transition: all 0.25s cubic-bezier(0.25, 0.8, 0.25, 1);
  }

  .menu-icon {
    transition: all 0.25s cubic-bezier(0.25, 0.8, 0.25, 1);
  }

  .menu-text {
    transition: opacity 0.2s cubic-bezier(0.25, 0.8, 0.25, 1);
  }
}

/* 为用户偏好减少动画的情况提供快速过渡 */
@media (prefers-reduced-motion: reduce) {
  .modern-sidebar,
  .nav-item,
  .menu-icon,
  .menu-text {
    transition: none;
  }
}

:deep(.el-menu--collapse .el-menu-item.is-active) {
  transform: translateX(0) scale(1.02);
}

:deep(.el-sub-menu) {
  margin: 3px 12px;
}

:deep(.el-menu--collapse .el-sub-menu) {
  margin: 3px 8px;
}

:deep(.el-sub-menu__title) {
  border-radius: 10px;
  background: transparent;
  color: #5a6c7d;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  height: 44px;
  line-height: 44px;
  position: relative;
  overflow: hidden;
}

:deep(.el-menu--collapse .el-sub-menu__title) {
  padding: 0 !important;
  text-align: center;
}

:deep(.el-sub-menu__title:hover) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  transform: translateX(2px);
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.25);
}

:deep(.el-menu--collapse .el-sub-menu__title:hover) {
  transform: translateX(0) scale(1.02);
}

:deep(.el-sub-menu .el-menu-item) {
  height: 40px;
  line-height: 40px;
  padding-left: 50px !important;
  margin: 2px 0;
  border-radius: 8px;
}

/* 下拉菜单样式 */
:deep(.user-dropdown-menu) {
  border-radius: 12px;
  border: none;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  padding: 8px;
}

:deep(.user-dropdown-menu .el-dropdown-menu__item) {
  border-radius: 8px;
  margin: 2px 0;
  padding: 12px 16px;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
}

:deep(.user-dropdown-menu .el-dropdown-menu__item:hover) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .header-center {
    margin: 0 16px;
  }

  .logo {
    font-size: 20px;
  }

  .logo-icon {
    font-size: 24px;
  }
}

@media (max-width: 768px) {
  .layout-main {
    padding: 12px;
    gap: 12px;
  }

  .header-content {
    padding: 0 12px;
  }

  .header-left {
    gap: 12px;
  }

  .header-center {
    display: none;
  }

  .user-details {
    display: none;
  }

  .logo-subtitle {
    display: none;
  }

  .logo {
    font-size: 18px;
  }

  .logo-icon {
    font-size: 22px;
  }

  .sidebar-toggle {
    width: 38px;
    height: 38px;
  }

  .action-btn {
    width: 36px;
    height: 36px;
  }
}

@media (max-width: 480px) {
  .header-content {
    padding: 0 8px;
  }

  .header-left {
    gap: 8px;
  }

  .header-right {
    gap: 8px;
  }

  .header-actions {
    gap: 4px;
  }
}

/* 滚动条样式 */
.layout-content::-webkit-scrollbar {
  width: 6px;
}

.layout-content::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.layout-content::-webkit-scrollbar-thumb {
  background: rgba(102, 126, 234, 0.3);
  border-radius: 3px;
}

.layout-content::-webkit-scrollbar-thumb:hover {
  background: rgba(102, 126, 234, 0.5);
}

/* 设置对话框样式 */
.settings-dialog {
  z-index: 3000 !important;
}

.settings-dialog .el-dialog__body {
  padding: 0 !important;
  background: white !important;
  color: black !important;
}

.settings-dialog .el-dialog__header {
  background: white !important;
  color: black !important;
}

.settings-dialog .el-dialog__footer {
  background: white !important;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
