<template>
  <div class="page-container">
    <div class="page-header">
      <h2 class="page-title">评价管理</h2>
    </div>

    <!-- 搜索栏 -->
    <div class="search-bar mb-16">
      <el-row :gutter="16">
        <el-col :span="6">
          <el-input
            v-model="searchForm.keyword"
            placeholder="搜索商品名称或用户"
            clearable
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-col>
        <el-col :span="4">
          <el-select
            v-model="searchForm.status"
            placeholder="选择状态"
            clearable
          >
            <el-option label="正常" :value="1" />
            <el-option label="隐藏" :value="0" />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-select
            v-model="searchForm.rating"
            placeholder="选择评分"
            clearable
          >
            <el-option label="5星" :value="5" />
            <el-option label="4星" :value="4" />
            <el-option label="3星" :value="3" />
            <el-option label="2星" :value="2" />
            <el-option label="1星" :value="1" />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetSearch">
            重置
          </el-button>
          <el-button @click="handleRefresh" :loading="loading">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 批量操作栏 -->
    <div v-if="selectedReviews.length > 0" class="batch-actions">
      <div class="batch-info">
        <div class="batch-summary">
          <div class="summary-item">
            <el-icon><InfoFilled /></el-icon>
            <span class="summary-label">已选择:</span>
            <span class="summary-value">{{ selectedReviews.length }} 条评价</span>
          </div>
          <div class="summary-item">
            <el-icon><Star /></el-icon>
            <span class="summary-label">平均评分:</span>
            <span class="summary-value">{{ selectedSummary.averageRating }}</span>
          </div>
          <div class="summary-item">
            <el-icon><User /></el-icon>
            <span class="summary-label">用户数:</span>
            <span class="summary-value">{{ selectedSummary.userCount }} 个</span>
          </div>
          <div class="summary-item">
            <el-icon><Goods /></el-icon>
            <span class="summary-label">商品数:</span>
            <span class="summary-value">{{ selectedSummary.flowerCount }} 个</span>
          </div>
          <div class="summary-item">
            <el-icon><View /></el-icon>
            <span class="summary-label">显示:</span>
            <span class="summary-value">{{ selectedSummary.visibleCount }}</span>
          </div>
          <div class="summary-item">
            <el-icon><Hide /></el-icon>
            <span class="summary-label">隐藏:</span>
            <span class="summary-value">{{ selectedSummary.hiddenCount }}</span>
          </div>
        </div>
      </div>
      <div class="batch-buttons">
        <el-button
          type="success"
          size="small"
          @click="batchToggleStatus(1)"
          :disabled="selectedSummary.hiddenCount === 0"
        >
          <el-icon><View /></el-icon>
          批量显示 ({{ selectedSummary.hiddenCount }})
        </el-button>
        <el-button
          type="warning"
          size="small"
          @click="batchToggleStatus(0)"
          :disabled="selectedSummary.visibleCount === 0"
        >
          <el-icon><Hide /></el-icon>
          批量隐藏 ({{ selectedSummary.visibleCount }})
        </el-button>
        <el-button
          type="danger"
          size="small"
          @click="batchDelete"
        >
          <el-icon><Delete /></el-icon>
          批量删除
        </el-button>
      </div>
    </div>

    <!-- 评价表格 -->
    <el-table
      :data="reviews"
      :loading="loading"
      stripe
      style="width: 100%"
      @selection-change="handleSelectionChange"
    >
      <!-- 多选列 -->
      <el-table-column type="selection" width="55" align="center" />

      <!-- 序号列 -->
      <el-table-column label="序号" width="80" align="center">
        <template #default="{ $index }">
          <span class="row-number">{{ (pagination.current - 1) * pagination.size + $index + 1 }}</span>
        </template>
      </el-table-column>
      
      <el-table-column prop="userName" label="用户" width="120" />
      
      <el-table-column prop="flowerName" label="商品" width="150" />
      
      <el-table-column prop="rating" label="评分" width="100">
        <template #default="{ row }">
          <el-rate
            v-model="row.rating"
            disabled
            show-score
            text-color="#ff9900"
            score-template="{value}"
          />
        </template>
      </el-table-column>
      
      <el-table-column prop="content" label="评价内容" min-width="200">
        <template #default="{ row }">
          <div class="review-content">
            {{ row.content || '用户未填写评价内容' }}
          </div>
        </template>
      </el-table-column>
      
      <el-table-column prop="isAnonymous" label="匿名" width="80">
        <template #default="{ row }">
          <el-tag :type="row.isAnonymous ? 'info' : 'success'" size="small">
            {{ row.isAnonymous ? '匿名' : '实名' }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column prop="status" label="状态" width="100">
        <template #default="{ row }">
          <el-tag :type="row.status === 1 ? 'success' : 'danger'">
            {{ row.status === 1 ? '正常' : '隐藏' }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column prop="createdAt" label="评价时间" width="180">
        <template #default="{ row }">
          {{ formatDate(row.createdAt) }}
        </template>
      </el-table-column>
      
      <el-table-column label="操作" width="240" fixed="right" align="center">
        <template #default="{ row }">
          <div class="action-buttons">
            <el-button
              type="primary"
              size="small"
              @click="viewReviewDetail(row)"
            >
              <el-icon><View /></el-icon>
              详情
            </el-button>

            <el-button
              :type="row.status === 1 ? 'warning' : 'success'"
              size="small"
              @click="toggleReviewStatus(row)"
            >
              <el-icon v-if="row.status === 1"><Hide /></el-icon>
              <el-icon v-else><View /></el-icon>
              {{ row.status === 1 ? '隐藏' : '显示' }}
            </el-button>

            <el-button
              type="danger"
              size="small"
              @click="deleteReview(row)"
            >
              <el-icon><Delete /></el-icon>
              删除
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="pagination.current"
        v-model:page-size="pagination.size"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 评价详情弹窗 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="评价详情"
      width="600px"
    >
      <div v-if="selectedReview" class="review-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="评价ID">
            {{ selectedReview.id }}
          </el-descriptions-item>
          <el-descriptions-item label="用户">
            {{ selectedReview.userName }}
          </el-descriptions-item>
          <el-descriptions-item label="商品">
            {{ selectedReview.flowerName }}
          </el-descriptions-item>
          <el-descriptions-item label="订单号">
            {{ selectedReview.orderId }}
          </el-descriptions-item>
          <el-descriptions-item label="评分">
            <el-rate
              v-model="selectedReview.rating"
              disabled
              show-score
              text-color="#ff9900"
              score-template="{value} 分"
            />
          </el-descriptions-item>
          <el-descriptions-item label="匿名">
            <el-tag :type="selectedReview.isAnonymous ? 'info' : 'success'" size="small">
              {{ selectedReview.isAnonymous ? '匿名评价' : '实名评价' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="selectedReview.status === 1 ? 'success' : 'danger'">
              {{ selectedReview.status === 1 ? '正常显示' : '已隐藏' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="评价时间">
            {{ formatDate(selectedReview.createdAt) }}
          </el-descriptions-item>
          <el-descriptions-item label="评价内容" :span="2">
            <div class="review-content-detail">
              {{ selectedReview.content || '用户未填写评价内容' }}
            </div>
          </el-descriptions-item>
        </el-descriptions>
        
        <!-- 评价图片 -->
        <div v-if="selectedReview.images && selectedReview.images.length > 0" class="review-images">
          <h4>评价图片</h4>
          <el-image
            v-for="(image, index) in selectedReview.images"
            :key="index"
            :src="image"
            :preview-src-list="selectedReview.images"
            style="width: 100px; height: 100px; margin-right: 8px"
            fit="cover"
          />
        </div>
      </div>
      
      <template #footer>
        <el-button @click="detailDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { adminApi } from '@/api/admin'
import { formatDate } from '@/utils'

const loading = ref(false)
const reviews = ref([])
const selectedReviews = ref([])
const selectedReview = ref(null)
const detailDialogVisible = ref(false)

const searchForm = reactive({
  keyword: '',
  status: null,
  rating: null
})

const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

// 计算选中评价的统计信息
const selectedSummary = computed(() => {
  if (selectedReviews.value.length === 0) {
    return {
      averageRating: '0.0',
      userCount: 0,
      flowerCount: 0,
      visibleCount: 0,
      hiddenCount: 0
    }
  }

  // 计算平均评分
  const totalRating = selectedReviews.value.reduce((sum, review) => {
    return sum + (review.rating || 0)
  }, 0)
  const averageRating = (totalRating / selectedReviews.value.length).toFixed(1)

  // 计算用户数量（去重）
  const userIds = new Set(selectedReviews.value.map(review => review.userId))
  const userCount = userIds.size

  // 计算商品数量（去重）
  const flowerIds = new Set(selectedReviews.value.map(review => review.flowerId))
  const flowerCount = flowerIds.size

  // 计算显示和隐藏数量
  const visibleCount = selectedReviews.value.filter(review => review.status === 1).length
  const hiddenCount = selectedReviews.value.filter(review => review.status === 0).length

  return {
    averageRating,
    userCount,
    flowerCount,
    visibleCount,
    hiddenCount
  }
})

// 加载评价列表
const loadReviews = async () => {
  loading.value = true
  try {
    const params = {
      current: pagination.current,
      size: pagination.size,
      keyword: searchForm.keyword,
      status: searchForm.status,
      rating: searchForm.rating
    }
    
    const response = await adminApi.getReviews(params)
    reviews.value = response.data.records
    pagination.total = response.data.total
  } catch (error) {
    console.error('加载评价列表失败:', error)
    ElMessage.error('加载评价列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.current = 1
  loadReviews()
}

// 重置搜索
const resetSearch = () => {
  searchForm.keyword = ''
  searchForm.status = null
  searchForm.rating = null
  pagination.current = 1
  loadReviews()
  ElMessage.success('搜索条件已重置')
}

// 刷新数据
const handleRefresh = () => {
  // 保持当前搜索条件，重新加载数据
  loadReviews()
  ElMessage({
    message: '评价数据已刷新',
    type: 'success',
    duration: 1500,
    showClose: false
  })
}

// 分页大小改变
const handleSizeChange = (size) => {
  pagination.size = size
  pagination.current = 1
  loadReviews()
}

// 当前页改变
const handleCurrentChange = (current) => {
  pagination.current = current
  loadReviews()
}

// 查看评价详情
const viewReviewDetail = (review) => {
  selectedReview.value = review
  detailDialogVisible.value = true
}

// 切换评价状态
const toggleReviewStatus = async (review) => {
  const action = review.status === 1 ? '隐藏' : '显示'
  const newStatus = review.status === 1 ? 0 : 1
  
  try {
    await ElMessageBox.confirm(
      `确定要${action}这条评价吗？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await adminApi.updateReviewStatus(review.id, newStatus)
    ElMessage.success(`${action}成功`)
    loadReviews()
  } catch (error) {
    if (error !== 'cancel') {
      console.error(`${action}评价失败:`, error)
      ElMessage.error(`${action}评价失败`)
    }
  }
}

// 删除评价
const deleteReview = async (review) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除这条评价吗？此操作不可恢复！`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await adminApi.deleteReview(review.id)
    ElMessage.success('删除成功')
    loadReviews()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除评价失败:', error)
      ElMessage.error('删除评价失败')
    }
  }
}

// 多选变化处理
const handleSelectionChange = (selection) => {
  selectedReviews.value = selection
}

// 批量切换状态
const batchToggleStatus = async (status) => {
  if (selectedReviews.value.length === 0) {
    ElMessage.warning('请先选择要操作的评价')
    return
  }

  // 筛选出需要操作的评价
  const targetReviews = selectedReviews.value.filter(review => review.status !== status)

  if (targetReviews.length === 0) {
    const action = status === 1 ? '显示' : '隐藏'
    ElMessage.warning(`选中的评价都已经是${action}状态`)
    return
  }

  try {
    const action = status === 1 ? '显示' : '隐藏'
    await ElMessageBox.confirm(
      `确定要${action}选中的 ${targetReviews.length} 条评价吗？`,
      '批量操作确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 批量更新状态
    const promises = targetReviews.map(review =>
      adminApi.updateReviewStatus(review.id, status)
    )

    await Promise.all(promises)
    ElMessage.success(`批量${action}成功，共操作 ${targetReviews.length} 条评价`)
    selectedReviews.value = []
    loadReviews()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量操作失败:', error)
      ElMessage.error('批量操作失败')
    }
  }
}

// 批量删除
const batchDelete = async () => {
  if (selectedReviews.value.length === 0) {
    ElMessage.warning('请先选择要删除的评价')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedReviews.value.length} 条评价吗？此操作不可恢复！`,
      '批量删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 批量删除
    const promises = selectedReviews.value.map(review =>
      adminApi.deleteReview(review.id)
    )

    await Promise.all(promises)
    ElMessage.success('批量删除成功')
    selectedReviews.value = []
    loadReviews()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败:', error)
      ElMessage.error('批量删除失败')
    }
  }
}

onMounted(async () => {
  console.log('评价管理页面已挂载，开始加载数据...')
  try {
    await loadReviews()
    console.log('评价数据加载完成')
  } catch (error) {
    console.error('评价数据加载失败:', error)
    ElMessage.error('评价数据加载失败: ' + error.message)
  }
})
</script>

<style scoped>
.search-bar {
  background: #fff;
  padding: 16px;
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0,0,0,0.03);
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 24px;
}

.text-danger {
  color: #f56c6c;
}

.text-success {
  color: #67c23a;
}

.review-content {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.review-content-detail {
  padding: 8px;
  background: #f5f7fa;
  border-radius: 4px;
  line-height: 1.6;
  word-break: break-all;
}

.review-images {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e4e7ed;
}

.review-images h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #303133;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
  flex-wrap: wrap;
}

.action-buttons .el-button {
  margin: 0;
  min-width: 60px;
}

/* 序号样式 */
.row-number {
  color: #606266;
  font-size: 14px;
  font-weight: 500;
}

/* 批量操作栏样式 */
.batch-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border: 1px solid #bae6fd;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.batch-info {
  flex: 1;
}

.batch-summary {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  align-items: center;
}

.summary-item {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #0369a1;
  font-size: 14px;
}

.summary-label {
  font-weight: 500;
  color: #0f172a;
}

.summary-value {
  font-weight: 600;
  color: #0369a1;
}

.batch-buttons {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
}

.batch-buttons .el-button {
  margin: 0;
  min-width: 100px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .batch-actions {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .batch-summary {
    justify-content: center;
  }

  .batch-buttons {
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .batch-summary {
    gap: 12px;
  }

  .summary-item {
    font-size: 13px;
  }

  .batch-buttons {
    flex-direction: column;
  }

  .batch-buttons .el-button {
    min-width: auto;
  }
}
</style>
