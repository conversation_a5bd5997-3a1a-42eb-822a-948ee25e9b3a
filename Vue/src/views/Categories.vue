<template>
  <div class="page-container">
    <div class="page-header">
      <h2 class="page-title">分类管理</h2>
      <el-button type="primary" @click="showCreateDialog">
        <el-icon><Plus /></el-icon>
        添加分类
      </el-button>
    </div>

    <!-- 搜索栏 -->
    <div class="search-bar mb-16">
      <el-row :gutter="16">
        <el-col :span="6">
          <el-input
            v-model="searchForm.keyword"
            placeholder="搜索分类名称或描述"
            clearable
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-col>
        <el-col :span="4">
          <el-select
            v-model="searchForm.status"
            placeholder="选择状态"
            clearable
          >
            <el-option label="启用" :value="1" />
            <el-option label="禁用" :value="0" />
          </el-select>
        </el-col>
        <el-col :span="8">
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetSearch">
            重置
          </el-button>
          <el-button @click="handleRefresh" :loading="loading">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 批量操作栏 -->
    <div v-if="selectedCategories.length > 0" class="batch-actions">
      <div class="batch-info">
        <div class="batch-summary">
          <div class="summary-item">
            <el-icon><InfoFilled /></el-icon>
            <span class="summary-label">已选择:</span>
            <span class="summary-value">{{ selectedCategories.length }} 个分类</span>
          </div>
          <div class="summary-item">
            <el-icon><Sort /></el-icon>
            <span class="summary-label">排序范围:</span>
            <span class="summary-value">{{ selectedSummary.sortRange }}</span>
          </div>
          <div class="summary-item">
            <el-icon><CircleCheck /></el-icon>
            <span class="summary-label">启用:</span>
            <span class="summary-value">{{ selectedSummary.enabledCount }}</span>
          </div>
          <div class="summary-item">
            <el-icon><CircleClose /></el-icon>
            <span class="summary-label">禁用:</span>
            <span class="summary-value">{{ selectedSummary.disabledCount }}</span>
          </div>
        </div>
      </div>
      <div class="batch-buttons">
        <el-button
          type="success"
          size="small"
          @click="batchToggleStatus(1)"
          :disabled="selectedSummary.disabledCount === 0"
        >
          <el-icon><CircleCheck /></el-icon>
          批量启用 ({{ selectedSummary.disabledCount }})
        </el-button>
        <el-button
          type="warning"
          size="small"
          @click="batchToggleStatus(0)"
          :disabled="selectedSummary.enabledCount === 0"
        >
          <el-icon><CircleClose /></el-icon>
          批量禁用 ({{ selectedSummary.enabledCount }})
        </el-button>
        <el-button
          type="danger"
          size="small"
          @click="batchDelete"
        >
          <el-icon><Delete /></el-icon>
          批量删除
        </el-button>
      </div>
    </div>

    <!-- 分类表格 -->
    <el-table
      :data="categories"
      :loading="loading"
      stripe
      style="width: 100%"
      @selection-change="handleSelectionChange"
    >
      <!-- 多选列 -->
      <el-table-column type="selection" width="55" align="center" />

      <!-- 序号列 -->
      <el-table-column label="序号" width="80" align="center">
        <template #default="{ $index }">
          <span class="row-number">{{ (pagination.current - 1) * pagination.size + $index + 1 }}</span>
        </template>
      </el-table-column>
      
      <el-table-column label="分类图片" width="120">
        <template #default="{ row }">
          <el-image
            v-if="row.imageUrl"
            :src="row.imageUrl"
            :preview-src-list="[row.imageUrl]"
            style="width: 60px; height: 60px; border-radius: 8px;"
            fit="cover"
            :preview-teleported="true"
          >
            <template #error>
              <div class="image-error">
                <el-icon><Picture /></el-icon>
              </div>
            </template>
          </el-image>
          <div v-else class="no-image">
            <el-icon><Picture /></el-icon>
            <span>无图片</span>
          </div>
        </template>
      </el-table-column>
      
      <el-table-column prop="name" label="分类名称" />
      
      <el-table-column prop="description" label="描述" min-width="200" />
      
      <el-table-column prop="sortOrder" label="排序" width="80" />
      
      <el-table-column prop="status" label="状态" width="100">
        <template #default="{ row }">
          <el-tag :type="row.status === 1 ? 'success' : 'danger'">
            {{ row.status === 1 ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column prop="createdAt" label="创建时间" width="180">
        <template #default="{ row }">
          {{ formatDate(row.createdAt) }}
        </template>
      </el-table-column>
      
      <el-table-column label="操作" width="240" fixed="right" align="center">
        <template #default="{ row }">
          <div class="action-buttons">
            <el-button
              type="primary"
              size="small"
              @click="showEditDialog(row)"
            >
              <el-icon><Edit /></el-icon>
              编辑
            </el-button>

            <el-button
              :type="row.status === 1 ? 'warning' : 'success'"
              size="small"
              @click="toggleStatus(row)"
            >
              <el-icon v-if="row.status === 1"><CircleClose /></el-icon>
              <el-icon v-else><CircleCheck /></el-icon>
              {{ row.status === 1 ? '禁用' : '启用' }}
            </el-button>

            <el-button
              type="danger"
              size="small"
              @click="deleteCategory(row)"
            >
              <el-icon><Delete /></el-icon>
              删除
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="pagination.current"
        v-model:page-size="pagination.size"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 添加/编辑分类弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑分类' : '添加分类'"
      width="500px"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="formRules"
        label-width="80px"
      >
        <el-form-item label="分类名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入分类名称" />
        </el-form-item>
        
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            :rows="3"
            placeholder="请输入分类描述"
          />
        </el-form-item>
        
        <el-form-item label="分类图片">
          <div class="image-upload-container">
            <el-upload
              class="image-uploader"
              :action="uploadAction"
              :headers="uploadHeaders"
              :show-file-list="false"
              :on-success="handleImageSuccess"
              :on-error="handleImageError"
              :before-upload="beforeImageUpload"
            >
              <img v-if="form.imageUrl" :src="form.imageUrl" class="image" />
              <el-icon v-else class="image-uploader-icon"><Plus /></el-icon>
            </el-upload>
            <div v-if="form.imageUrl" class="image-actions">
              <el-button size="small" type="danger" plain @click="removeImage">
                <el-icon><Delete /></el-icon>
                删除图片
              </el-button>
            </div>
          </div>
        </el-form-item>
        
        <el-form-item label="排序" prop="sortOrder">
          <el-input-number
            v-model="form.sortOrder"
            :min="0"
            :max="999"
            placeholder="排序值"
          />
        </el-form-item>
        
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="submitting" @click="handleSubmit">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  Search,
  Refresh,
  Edit,
  Delete,
  Picture,
  CircleCheck,
  CircleClose,
  InfoFilled,
  Sort
} from '@element-plus/icons-vue'
import { adminApi } from '@/api/admin'
import { formatDate } from '@/utils'
import { useAuthStore } from '@/stores/auth'

const authStore = useAuthStore()
const loading = ref(false)
const submitting = ref(false)
const categories = ref([])
const selectedCategories = ref([])
const dialogVisible = ref(false)
const isEdit = ref(false)
const formRef = ref()

// 搜索表单
const searchForm = reactive({
  keyword: '',
  status: null
})

// 分页数据
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

const form = reactive({
  id: null,
  name: '',
  description: '',
  imageUrl: '',
  sortOrder: 0,
  status: 1
})

const formRules = {
  name: [
    { required: true, message: '请输入分类名称', trigger: 'blur' },
    { min: 2, max: 50, message: '分类名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  description: [
    { max: 200, message: '描述不能超过 200 个字符', trigger: 'blur' }
  ],
  sortOrder: [
    { required: true, message: '请输入排序值', trigger: 'blur' }
  ]
}

const uploadAction = computed(() => 'http://localhost:8080/api/admin/upload/image')
const uploadHeaders = computed(() => ({
  Authorization: `Bearer ${authStore.token}`
}))

// 计算选中分类的统计信息
const selectedSummary = computed(() => {
  if (selectedCategories.value.length === 0) {
    return {
      sortRange: '-',
      enabledCount: 0,
      disabledCount: 0
    }
  }

  // 计算排序范围
  const sortOrders = selectedCategories.value.map(cat => cat.sortOrder || 0)
  const minSort = Math.min(...sortOrders)
  const maxSort = Math.max(...sortOrders)
  const sortRange = minSort === maxSort ? minSort.toString() : `${minSort}-${maxSort}`

  // 计算启用和禁用数量
  const enabledCount = selectedCategories.value.filter(cat => cat.status === 1).length
  const disabledCount = selectedCategories.value.filter(cat => cat.status === 0).length

  return {
    sortRange,
    enabledCount,
    disabledCount
  }
})

// 加载分类列表
const loadCategories = async () => {
  loading.value = true
  try {
    // 构建查询参数
    const params = {
      current: pagination.current,
      size: pagination.size,
      keyword: searchForm.keyword,
      status: searchForm.status
    }

    // 清理空值参数
    Object.keys(params).forEach(key => {
      if (params[key] === null || params[key] === undefined || params[key] === '') {
        delete params[key]
      }
    })

    console.log('请求参数:', params)
    const response = await adminApi.getCategories(params)

    // 处理API响应数据
    if (response && response.data) {
      categories.value = response.data.records || []
      pagination.total = response.data.total || 0

      console.log('分类数据:', {
        records: categories.value.length,
        total: pagination.total,
        current: pagination.current,
        size: pagination.size
      })
    } else {
      categories.value = []
      pagination.total = 0
    }
  } catch (error) {
    console.error('加载分类列表失败:', error)
    console.error('错误详情:', error.response || error)
    ElMessage.error('加载分类列表失败: ' + (error.message || '未知错误'))
    categories.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleSearch = () => {
  pagination.current = 1
  loadCategories()
}

// 重置搜索
const resetSearch = () => {
  searchForm.keyword = ''
  searchForm.status = null
  pagination.current = 1
  loadCategories()
  ElMessage.success('搜索条件已重置')
}

// 刷新数据
const handleRefresh = () => {
  // 保持当前搜索条件，重新加载数据
  loadCategories()
  ElMessage({
    message: '分类数据已刷新',
    type: 'success',
    duration: 1500,
    showClose: false
  })
}

// 分页大小改变
const handleSizeChange = (size) => {
  pagination.size = size
  pagination.current = 1
  loadCategories()
}

// 当前页改变
const handleCurrentChange = (current) => {
  pagination.current = current
  loadCategories()
}

// 显示创建弹窗
const showCreateDialog = () => {
  isEdit.value = false
  resetForm()
  dialogVisible.value = true
}

// 显示编辑弹窗
const showEditDialog = (category) => {
  console.log('=== 打开编辑对话框 ===')
  console.log('原始分类数据:', category)

  isEdit.value = true

  // 先重置表单
  resetForm()

  // 然后设置编辑数据
  form.id = category.id
  form.name = category.name
  form.description = category.description || ''
  form.imageUrl = category.imageUrl || ''
  form.sortOrder = category.sortOrder || 0
  form.status = category.status !== undefined ? category.status : 1

  console.log('设置后的表单数据:', { ...form })
  dialogVisible.value = true
}

// 重置表单
const resetForm = () => {
  form.id = null
  form.name = ''
  form.description = ''
  form.imageUrl = ''
  form.sortOrder = 0
  form.status = 1

  // 只在表单引用存在时重置验证
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

// 处理图片上传成功
const handleImageSuccess = (response) => {
  console.log('图片上传响应:', response)
  if (response.code === 200) {
    const oldImageUrl = form.imageUrl
    form.imageUrl = response.data.url
    console.log('图片上传成功!')
    console.log('旧图片URL:', oldImageUrl)
    console.log('新图片URL:', form.imageUrl)
    console.log('当前表单数据:', { ...form })
    ElMessage.success('图片上传成功')
  } else {
    console.error('图片上传失败:', response)
    ElMessage.error(response.message || '图片上传失败')
  }
}

// 处理图片上传失败
const handleImageError = (error) => {
  console.error('图片上传失败:', error)
  ElMessage.error('图片上传失败，请重试')
}

// 删除图片
const removeImage = () => {
  form.imageUrl = ''
  ElMessage.success('图片已删除')
}

// 上传前验证
const beforeImageUpload = (file) => {
  const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isJPG) {
    ElMessage.error('上传图片只能是 JPG/PNG 格式!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('上传图片大小不能超过 2MB!')
    return false
  }
  return true
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    submitting.value = true

    // 准备提交的数据
    const submitData = {
      name: form.name,
      description: form.description,
      imageUrl: form.imageUrl,
      sortOrder: form.sortOrder,
      status: form.status
    }

    console.log('=== 提交表单数据 ===')
    console.log('当前表单状态:', { ...form })
    console.log('准备提交的数据:', submitData)
    console.log('是否为编辑模式:', isEdit.value)
    console.log('分类ID:', form.id)

    if (isEdit.value) {
      const result = await adminApi.updateCategory(form.id, submitData)
      console.log('=== 更新API响应 ===')
      console.log('更新结果:', result)
      if (result.data) {
        console.log('返回的imageUrl:', result.data.imageUrl)
      }
      ElMessage.success('更新成功')
    } else {
      const result = await adminApi.createCategory(submitData)
      console.log('=== 创建API响应 ===')
      console.log('创建结果:', result)
      ElMessage.success('创建成功')
    }

    dialogVisible.value = false
    // 强制刷新分类列表
    await loadCategories()
    console.log('=== 刷新后的分类列表 ===')
    console.log('更新后的分类数据:', categories.value)
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('提交失败')
  } finally {
    submitting.value = false
  }
}

// 删除分类
const deleteCategory = async (category) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除分类 "${category.name}" 吗？此操作不可恢复！`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await adminApi.deleteCategory(category.id)
    ElMessage.success('删除成功')
    loadCategories()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除分类失败:', error)
      ElMessage.error('删除分类失败')
    }
  }
}

// 切换单个分类状态
const toggleStatus = async (category) => {
  try {
    const action = category.status === 1 ? '禁用' : '启用'
    await ElMessageBox.confirm(`确定要${action}分类"${category.name}"吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await adminApi.updateCategoryStatus(category.id, category.status === 1 ? 0 : 1)
    ElMessage.success(`${action}成功`)
    loadCategories()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('切换分类状态失败:', error)
      ElMessage.error('操作失败')
    }
  }
}

// 多选变化处理
const handleSelectionChange = (selection) => {
  selectedCategories.value = selection
}

// 批量切换状态
const batchToggleStatus = async (status) => {
  if (selectedCategories.value.length === 0) {
    ElMessage.warning('请先选择要操作的分类')
    return
  }

  // 筛选出需要操作的分类
  const targetCategories = selectedCategories.value.filter(cat => cat.status !== status)

  if (targetCategories.length === 0) {
    const action = status === 1 ? '启用' : '禁用'
    ElMessage.warning(`选中的分类都已经是${action}状态`)
    return
  }

  try {
    const action = status === 1 ? '启用' : '禁用'
    await ElMessageBox.confirm(
      `确定要${action}选中的 ${targetCategories.length} 个分类吗？`,
      '批量操作确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 批量更新状态
    const promises = targetCategories.map(cat =>
      adminApi.updateCategoryStatus(cat.id, status)
    )

    await Promise.all(promises)
    ElMessage.success(`批量${action}成功，共操作 ${targetCategories.length} 个分类`)
    selectedCategories.value = []
    loadCategories()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量操作失败:', error)
      ElMessage.error('批量操作失败')
    }
  }
}

// 批量删除
const batchDelete = async () => {
  if (selectedCategories.value.length === 0) {
    ElMessage.warning('请先选择要删除的分类')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedCategories.value.length} 个分类吗？此操作不可恢复！`,
      '批量删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 批量删除
    const promises = selectedCategories.value.map(cat =>
      adminApi.deleteCategory(cat.id)
    )

    await Promise.all(promises)
    ElMessage.success('批量删除成功')
    selectedCategories.value = []
    loadCategories()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败:', error)
      ElMessage.error('批量删除失败')
    }
  }
}

onMounted(async () => {
  console.log('分类管理页面已挂载，开始加载数据...')
  try {
    await loadCategories()
    console.log('分类数据加载完成')
  } catch (error) {
    console.error('分类数据加载失败:', error)
    ElMessage.error('分类数据加载失败: ' + error.message)
  }
})
</script>

<style scoped>
/* 搜索栏样式 */
.search-bar {
  background: #fff;
  padding: 16px;
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0,0,0,0.03);
}

/* 分页容器样式 */
.pagination-container {
  display: flex;
  justify-content: center;
  padding: 20px 0;
  background: #fff;
  border-radius: 4px;
  margin-top: 16px;
  box-shadow: 0 1px 2px rgba(0,0,0,0.03);
}

.no-image {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  background: #f5f7fa;
  border-radius: 8px;
  color: #c0c4cc;
  font-size: 12px;
  gap: 4px;
}

/* 序号样式 */
.row-number {
  color: #606266;
  font-size: 14px;
  font-weight: 500;
}

.image-error {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  background: #f5f7fa;
  border-radius: 8px;
  color: #c0c4cc;
}

.text-danger {
  color: #f56c6c;
}

.image-upload-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.image-uploader {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: border-color 0.2s;
}

.image-actions {
  display: flex;
  justify-content: center;
}

.image-uploader:hover {
  border-color: #409eff;
}

.image-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  line-height: 100px;
  text-align: center;
}

.image {
  width: 100px;
  height: 100px;
  display: block;
  object-fit: cover;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
  flex-wrap: wrap;
}

.action-buttons .el-button {
  margin: 0;
  min-width: 60px;
}

/* 批量操作栏样式 */
.batch-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border: 1px solid #bae6fd;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.batch-info {
  flex: 1;
}

.batch-summary {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  align-items: center;
}

.summary-item {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #0369a1;
  font-size: 14px;
}

.summary-label {
  font-weight: 500;
  color: #0f172a;
}

.summary-value {
  font-weight: 600;
  color: #0369a1;
}

.batch-buttons {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
}

.batch-buttons .el-button {
  margin: 0;
  min-width: 100px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .batch-actions {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .batch-summary {
    justify-content: center;
  }

  .batch-buttons {
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .batch-summary {
    gap: 12px;
  }

  .summary-item {
    font-size: 13px;
  }

  .batch-buttons {
    flex-direction: column;
  }

  .batch-buttons .el-button {
    min-width: auto;
  }
}
</style>
