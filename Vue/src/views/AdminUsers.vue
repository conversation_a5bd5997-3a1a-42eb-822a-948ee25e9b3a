<template>
  <div class="admin-users-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">后端用户管理</h1>
        <p class="page-description">管理系统后端管理员账户</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="showAddDialog" :icon="Plus">
          新增用户
        </el-button>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="search-section">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-input
            v-model="searchForm.keyword"
            placeholder="搜索用户名、邮箱"
            :prefix-icon="Search"
            clearable
            @input="handleSearch"
          />
        </el-col>
        <el-col :span="4">
          <el-select v-model="searchForm.status" placeholder="状态筛选" clearable @change="handleSearch">
            <el-option label="全部" value="" />
            <el-option label="启用" value="1" />
            <el-option label="禁用" value="0" />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-select v-model="searchForm.role" placeholder="角色筛选" clearable @change="handleSearch">
            <el-option label="全部" value="" />
            <el-option label="超级管理员" value="super_admin" />
            <el-option label="管理员" value="admin" />
            <el-option label="操作员" value="operator" />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-button type="primary" @click="handleSearch" :icon="Search">搜索</el-button>
          <el-button @click="resetSearch" :icon="RefreshLeft">重置</el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 用户列表 -->
    <div class="table-section">
      <el-table
        :data="userList"
        v-loading="loading"
        stripe
        border
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column type="index" label="序号" width="80" :index="getTableIndex" />
        <el-table-column prop="username" label="用户名" width="120" />
        <el-table-column prop="email" label="邮箱" width="200" />
        <el-table-column prop="realName" label="真实姓名" width="120" />
        <el-table-column prop="phone" label="手机号" width="130" />
        <el-table-column prop="role" label="角色" width="120">
          <template #default="{ row }">
            <el-tag :type="getRoleTagType(row.role)">
              {{ getRoleText(row.role) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-switch
              v-model="row.status"
              :active-value="1"
              :inactive-value="0"
              @change="handleStatusChange(row)"
            />
          </template>
        </el-table-column>
        <el-table-column prop="lastLoginTime" label="最后登录" width="160" />
        <el-table-column prop="createdAt" label="创建时间" width="160" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="showEditDialog(row)" :icon="Edit">
              编辑
            </el-button>
            <el-button type="danger" size="small" @click="handleDelete(row)" :icon="Delete">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </div>

    <!-- 批量操作 -->
    <div class="batch-actions" v-if="selectedUsers.length > 0">
      <el-button type="success" @click="batchEnable" :icon="Check">
        批量启用 ({{ selectedUsers.length }})
      </el-button>
      <el-button type="warning" @click="batchDisable" :icon="Close">
        批量禁用 ({{ selectedUsers.length }})
      </el-button>
      <el-button type="danger" @click="batchDelete" :icon="Delete">
        批量删除 ({{ selectedUsers.length }})
      </el-button>
    </div>

    <!-- 新增/编辑用户对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="userFormRef"
        :model="userForm"
        :rules="userFormRules"
        label-width="100px"
      >
        <el-form-item label="用户名" prop="username">
          <el-input v-model="userForm.username" placeholder="请输入用户名" />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="userForm.email" placeholder="请输入邮箱" />
        </el-form-item>
        <el-form-item label="真实姓名" prop="realName">
          <el-input v-model="userForm.realName" placeholder="请输入真实姓名" />
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="userForm.phone" placeholder="请输入手机号" />
        </el-form-item>
        <el-form-item label="密码" prop="password" v-if="!isEdit">
          <el-input v-model="userForm.password" type="password" placeholder="请输入密码" show-password />
        </el-form-item>
        <el-form-item label="确认密码" prop="confirm_password" v-if="!isEdit">
          <el-input v-model="userForm.confirm_password" type="password" placeholder="请确认密码" show-password />
        </el-form-item>
        <el-form-item label="重置密码" prop="password" v-if="isEdit">
          <el-input v-model="userForm.password" type="password" placeholder="留空则不修改密码" show-password />
        </el-form-item>
        <el-form-item label="角色" prop="role">
          <el-select v-model="userForm.role" placeholder="请选择角色">
            <el-option label="超级管理员" value="super_admin" />
            <el-option label="管理员" value="admin" />
            <el-option label="操作员" value="operator" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="userForm.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="userForm.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>

          <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
            {{ isEdit ? '更新' : '创建' }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  Search,
  Edit,
  Delete,
  RefreshLeft,
  Check,
  Close
} from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const submitLoading = ref(false)
const dialogVisible = ref(false)
const isEdit = ref(false)
const userFormRef = ref()

// 搜索表单
const searchForm = reactive({
  keyword: '',
  status: '',
  role: ''
})

// 分页
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 用户列表
const userList = ref([])
const selectedUsers = ref([])

// 用户表单
const userForm = reactive({
  id: null,
  username: '',
  email: '',
  realName: '',
  password: '',
  confirm_password: '',
  role: '',
  status: 1,
  phone: '',
  remark: ''
})

// 表单验证规则 - 动态规则
const userFormRules = computed(() => {
  const baseRules = {
    username: [
      { required: true, message: '请输入用户名', trigger: 'blur' },
      { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }
    ],
    email: [
      { required: true, message: '请输入邮箱', trigger: 'blur' },
      { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
    ],
    realName: [
      { required: true, message: '请输入真实姓名', trigger: 'blur' }
    ],
    role: [
      { required: true, message: '请选择角色', trigger: 'change' }
    ]
  }

  // 新增模式下密码是必填的
  if (!isEdit.value) {
    baseRules.password = [
      { required: true, message: '请输入密码', trigger: 'blur' },
      { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
    ]
    baseRules.confirm_password = [
      { required: true, message: '请确认密码', trigger: 'blur' },
      {
        validator: (rule, value, callback) => {
          if (value !== userForm.password) {
            callback(new Error('两次输入密码不一致'))
          } else {
            callback()
          }
        },
        trigger: 'blur'
      }
    ]
  } else {
    // 编辑模式下密码不是必填的
    baseRules.password = [
      { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
    ]
  }

  return baseRules
})

// 计算属性
const dialogTitle = computed(() => {
  return isEdit.value ? '编辑用户' : '新增用户'
})

// 方法
const getRoleTagType = (role) => {
  const typeMap = {
    'super_admin': 'danger',
    'admin': 'warning',
    'operator': 'info'
  }
  return typeMap[role] || 'info'
}

const getRoleText = (role) => {
  const textMap = {
    'super_admin': '超级管理员',
    'admin': '管理员',
    'operator': '操作员'
  }
  return textMap[role] || '未知'
}

// API基础URL - 使用相对路径，通过Vite代理
const API_BASE_URL = '/api/admin'

// API请求函数
const apiRequest = async (url, options = {}) => {
  const token = localStorage.getItem('adminToken')
  const defaultOptions = {
    mode: 'cors',
    credentials: 'include',
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      ...(token && { 'Authorization': `Bearer ${token}` })
    }
  }

  console.log('API请求:', `${API_BASE_URL}${url}`, options)

  try {
    const response = await fetch(`${API_BASE_URL}${url}`, {
      ...defaultOptions,
      ...options,
      headers: {
        ...defaultOptions.headers,
        ...options.headers
      }
    })

    console.log('API响应状态:', response.status, response.statusText)

    if (!response.ok) {
      const errorText = await response.text()
      console.error('API错误响应:', errorText)
      throw new Error(`HTTP ${response.status}: ${errorText}`)
    }

    const data = await response.json()
    console.log('API响应数据:', data)

    return data
  } catch (error) {
    console.error('API请求失败:', error)
    throw error
  }
}

// 获取用户列表
const fetchUsers = async () => {
  loading.value = true
  try {
    const params = new URLSearchParams({
      current: pagination.page.toString(),
      size: pagination.size.toString()
    })

    if (searchForm.keyword) {
      params.append('keyword', searchForm.keyword)
    }
    if (searchForm.status !== '') {
      params.append('status', searchForm.status)
    }
    if (searchForm.role) {
      params.append('role', searchForm.role)
    }

    const response = await apiRequest(`/admin-users?${params}`)

    if (response.code === 200) {
      userList.value = response.data.records
      pagination.total = response.data.total
    } else {
      throw new Error(response.message)
    }

  } catch (error) {
    console.error('获取用户列表失败:', error)
    ElMessage.error('获取用户列表失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  fetchUsers()
}

// 重置搜索
const resetSearch = () => {
  Object.assign(searchForm, {
    keyword: '',
    status: '',
    role: ''
  })
  handleSearch()
}

// 分页
const handleSizeChange = (size) => {
  pagination.size = size
  fetchUsers()
}

const handlePageChange = (page) => {
  pagination.page = page
  fetchUsers()
}

// 选择变化
const handleSelectionChange = (selection) => {
  selectedUsers.value = selection
}

// 状态切换
const handleStatusChange = async (row) => {
  try {
    const response = await apiRequest(`/admin-users/${row.id}/status`, {
      method: 'PUT',
      body: JSON.stringify({ status: row.status })
    })

    if (response.code === 200) {
      ElMessage.success(`用户${row.status ? '启用' : '禁用'}成功`)
    } else {
      throw new Error(response.message)
    }
  } catch (error) {
    // 恢复原状态
    row.status = row.status ? 0 : 1
    console.error('状态更新失败:', error)
    ElMessage.error('状态更新失败: ' + error.message)
  }
}

// 显示新增对话框
const showAddDialog = () => {
  isEdit.value = false
  resetForm()
  dialogVisible.value = true
}

// 显示编辑对话框
const showEditDialog = (row) => {
  isEdit.value = true
  Object.assign(userForm, { ...row })
  dialogVisible.value = true
}

// 重置表单
const resetForm = () => {
  Object.assign(userForm, {
    id: null,
    username: '',
    email: '',
    realName: '',
    password: '',
    confirm_password: '',
    role: '',
    status: 1,
    phone: '',
    remark: ''
  })
  userFormRef.value?.clearValidate()
}

// 计算表格序号（考虑分页）
const getTableIndex = (index) => {
  return (pagination.page - 1) * pagination.size + index + 1
}

// 提交表单
const handleSubmit = async () => {
  try {
    await userFormRef.value.validate()
    submitLoading.value = true

    const submitData = {
      username: userForm.username,
      email: userForm.email,
      realName: userForm.realName,
      role: userForm.role,
      status: userForm.status,
      phone: userForm.phone || null,
      remark: userForm.remark || null
    }

    // 新增用户时，密码是必填的
    if (!isEdit.value) {
      submitData.password = userForm.password
    }

    // 编辑用户时，只有输入了新密码才更新
    if (isEdit.value && userForm.password && userForm.password.trim() !== '') {
      submitData.password = userForm.password
    }

    let response
    if (isEdit.value) {
      // 更新用户
      response = await apiRequest(`/admin-users/${userForm.id}`, {
        method: 'PUT',
        body: JSON.stringify(submitData)
      })
    } else {
      // 新增用户
      response = await apiRequest('/admin-users', {
        method: 'POST',
        body: JSON.stringify(submitData)
      })
    }

    if (response.code === 200) {
      ElMessage.success(isEdit.value ? '用户更新成功' : '用户创建成功')
      dialogVisible.value = false
      fetchUsers() // 重新获取用户列表
    } else {
      throw new Error(response.message)
    }

  } catch (error) {
    console.error('操作失败:', error)
    ElMessage.error('操作失败: ' + error.message)
  } finally {
    submitLoading.value = false
  }
}

// 删除用户
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除用户 "${row.username}" 吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await apiRequest(`/admin-users/${row.id}`, {
      method: 'DELETE'
    })

    if (response.code === 200) {
      ElMessage.success('用户删除成功')
      fetchUsers() // 重新获取用户列表
    } else {
      throw new Error(response.message)
    }

  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败: ' + error.message)
    }
  }
}

// 批量启用
const batchEnable = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要启用选中的 ${selectedUsers.value.length} 个用户吗？`,
      '批量启用确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }
    )

    const ids = selectedUsers.value.map(user => user.id)
    const response = await apiRequest('/admin-users/batch/status', {
      method: 'PUT',
      body: JSON.stringify({ ids, status: 1 })
    })

    if (response.code === 200) {
      ElMessage.success('批量启用成功')
      fetchUsers() // 重新获取用户列表
    } else {
      throw new Error(response.message)
    }

  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量启用失败:', error)
      ElMessage.error('批量启用失败: ' + error.message)
    }
  }
}

// 批量禁用
const batchDisable = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要禁用选中的 ${selectedUsers.value.length} 个用户吗？`,
      '批量禁用确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const ids = selectedUsers.value.map(user => user.id)
    const response = await apiRequest('/admin-users/batch/status', {
      method: 'PUT',
      body: JSON.stringify({ ids, status: 0 })
    })

    if (response.code === 200) {
      ElMessage.success('批量禁用成功')
      fetchUsers() // 重新获取用户列表
    } else {
      throw new Error(response.message)
    }

  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量禁用失败:', error)
      ElMessage.error('批量禁用失败: ' + error.message)
    }
  }
}

// 批量删除
const batchDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedUsers.value.length} 个用户吗？此操作不可恢复！`,
      '批量删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'error'
      }
    )

    const ids = selectedUsers.value.map(user => user.id)
    const response = await apiRequest('/admin-users/batch', {
      method: 'DELETE',
      body: JSON.stringify({ ids })
    })

    if (response.code === 200) {
      ElMessage.success('批量删除成功')
      fetchUsers() // 重新获取用户列表
    } else {
      throw new Error(response.message)
    }

  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败:', error)
      ElMessage.error('批量删除失败: ' + error.message)
    }
  }
}

// 组件挂载时获取数据
onMounted(() => {
  fetchUsers()
})
</script>

<style scoped>
.admin-users-container {
  padding: 20px;
  background: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.page-title {
  margin: 0;
  font-size: 24px;
  color: #333;
}

.page-description {
  margin: 5px 0 0 0;
  color: #666;
  font-size: 14px;
}

.search-section {
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.table-section {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.pagination-section {
  padding: 20px;
  display: flex;
  justify-content: center;
  border-top: 1px solid #ebeef5;
}

.batch-actions {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: white;
  padding: 15px 20px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 1px solid #ebeef5;
  z-index: 1000;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* 表格样式优化 */
:deep(.el-table) {
  border: none;
}

:deep(.el-table th) {
  background-color: #fafafa;
  color: #333;
  font-weight: 600;
}

:deep(.el-table td) {
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-table tr:hover > td) {
  background-color: #f8f9fa;
}

/* 按钮样式 */
.el-button + .el-button {
  margin-left: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .admin-users-container {
    padding: 10px;
  }

  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .header-right {
    width: 100%;
  }

  .search-section .el-row {
    flex-direction: column;
  }

  .search-section .el-col {
    width: 100%;
    margin-bottom: 10px;
  }

  .batch-actions {
    left: 10px;
    right: 10px;
    transform: none;
    text-align: center;
  }
}

/* 状态开关样式 */
:deep(.el-switch) {
  --el-switch-on-color: #67c23a;
  --el-switch-off-color: #dcdfe6;
}

/* 标签样式 */
.el-tag {
  font-weight: 500;
}

/* 表单样式 */
:deep(.el-form-item__label) {
  font-weight: 600;
  color: #333;
}

:deep(.el-input__wrapper) {
  border-radius: 6px;
}

:deep(.el-select .el-input__wrapper) {
  border-radius: 6px;
}

/* 对话框样式 */
:deep(.el-dialog) {
  border-radius: 8px;
}

:deep(.el-dialog__header) {
  border-bottom: 1px solid #ebeef5;
  padding: 20px 20px 15px;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  border-top: 1px solid #ebeef5;
  padding: 15px 20px 20px;
}
</style>
