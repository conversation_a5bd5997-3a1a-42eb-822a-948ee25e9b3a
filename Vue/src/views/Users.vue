<template>
  <div class="users-page">
    <!-- 用户管理主控制面板 -->
    <div class="user-control-panel">
      <el-card class="control-card" shadow="never">
        <template #header>
          <div class="control-header">
            <!-- 页面标题区域 -->
            <div class="page-info">
              <div class="page-title-section">
                <h1 class="page-title">
                  <el-icon class="title-icon"><User /></el-icon>
                  用户管理
                </h1>
                <p class="page-subtitle">管理系统中的所有用户信息</p>
              </div>
            </div>

            <!-- 统计数据区域 -->
            <div class="stats-section">
              <div class="stats-cards">
                <div class="stat-card primary">
                  <div class="stat-content">
                    <div class="stat-number">{{ totalUsers }}</div>
                    <div class="stat-label">总用户数</div>
                  </div>
                  <div class="stat-icon">
                    <el-icon><UserFilled /></el-icon>
                  </div>
                </div>
                <div class="stat-card success">
                  <div class="stat-content">
                    <div class="stat-number">{{ activeUsers }}</div>
                    <div class="stat-label">活跃用户</div>
                  </div>
                  <div class="stat-icon">
                    <el-icon><CircleCheckFilled /></el-icon>
                  </div>
                </div>
              </div>
            </div>

            <!-- 快捷操作区域 -->
            <div class="quick-actions">
              <el-tooltip content="刷新数据" placement="bottom">
                <el-button type="primary" circle @click="handleRefresh" :loading="loading">
                  <el-icon><Refresh /></el-icon>
                </el-button>
              </el-tooltip>
            </div>
          </div>
        </template>

        <div class="search-content">
          <el-row :gutter="20" align="middle">
            <el-col :span="10">
              <div class="search-group">
                <label class="search-label">
                  <el-icon><User /></el-icon>
                  关键词搜索
                </label>
                <div class="search-input-wrapper">
                  <el-input
                    v-model="searchForm.keyword"
                    placeholder="输入用户昵称、手机号或邮箱进行搜索..."
                    clearable
                    size="large"
                    @keyup.enter="handleSearch"
                    class="search-input"
                  >
                    <template #prefix>
                      <el-icon class="search-icon"><Search /></el-icon>
                    </template>
                    <template #suffix>
                      <el-tooltip content="支持模糊搜索" placement="top">
                        <el-icon class="help-icon"><QuestionFilled /></el-icon>
                      </el-tooltip>
                    </template>
                  </el-input>
                </div>
              </div>
            </el-col>

            <el-col :span="6">
              <div class="search-group">
                <label class="search-label">
                  <el-icon><Flag /></el-icon>
                  用户状态
                </label>
                <el-select
                  v-model="searchForm.status"
                  placeholder="选择状态"
                  clearable
                  size="large"
                  class="status-select"
                >
                  <el-option label="全部状态" value="">
                    <div class="option-item">
                      <el-icon><List /></el-icon>
                      <span>全部状态</span>
                    </div>
                  </el-option>
                  <el-option label="正常用户" :value="1">
                    <div class="option-item">
                      <el-icon class="status-icon success"><CircleCheckFilled /></el-icon>
                      <span>正常用户</span>
                    </div>
                  </el-option>
                  <el-option label="禁用用户" :value="0">
                    <div class="option-item">
                      <el-icon class="status-icon danger"><CircleCloseFilled /></el-icon>
                      <span>禁用用户</span>
                    </div>
                  </el-option>
                </el-select>
              </div>
            </el-col>

            <el-col :span="8">
              <div class="search-actions">
                <el-button
                  type="primary"
                  size="large"
                  @click="handleSearch"
                  class="search-btn"
                  :loading="loading"
                >
                  <el-icon><Search /></el-icon>
                  <span>搜索用户</span>
                </el-button>

                <el-button
                  size="large"
                  @click="handleReset"
                  class="reset-btn"
                >
                  <el-icon><RefreshLeft /></el-icon>
                  <span>重置条件</span>
                </el-button>

                <el-dropdown @command="handleExport" class="export-dropdown">
                  <el-button size="large" class="export-btn">
                    <el-icon><Download /></el-icon>
                    <span>导出</span>
                    <el-icon class="dropdown-icon"><ArrowDown /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="current">
                        <el-icon><DocumentCopy /></el-icon>
                        导出当前页
                      </el-dropdown-item>
                      <el-dropdown-item command="all">
                        <el-icon><FolderOpened /></el-icon>
                        导出全部数据
                      </el-dropdown-item>
                      <el-dropdown-item command="selected" divided>
                        <el-icon><Select /></el-icon>
                        导出已选择
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </el-col>
          </el-row>

          <!-- 快速筛选标签 -->
          <div class="quick-filters" v-if="quickFilters.length > 0">
            <div class="quick-filters-label">
              <el-icon><Filter /></el-icon>
              <span>快速筛选:</span>
            </div>
            <div class="quick-filters-tags">
              <el-tag
                v-for="filter in quickFilters"
                :key="filter.key"
                :type="filter.type"
                effect="light"
                closable
                @close="removeQuickFilter(filter.key)"
                class="quick-filter-tag"
              >
                {{ filter.label }}
              </el-tag>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 用户表格 -->
    <div class="table-section">
      <el-card class="table-card" shadow="never">
        <template #header>
          <div class="table-header">
            <div class="table-title">
              <el-icon><List /></el-icon>
              用户列表
              <el-tag v-if="selectedUsers.length > 0" type="primary" size="small" class="selected-count">
                已选择 {{ selectedUsers.length }} 项
              </el-tag>
            </div>
            <div class="table-actions">
              <el-button
                v-if="selectedUsers.length > 0"
                type="danger"
                plain
                size="small"
                @click="handleBatchDelete"
              >
                <el-icon><Delete /></el-icon>
                批量删除
              </el-button>
            </div>
          </div>
        </template>

        <el-table
          :data="users"
          :loading="loading"
          class="modern-table"
          :header-cell-style="{
            background: '#fafafa',
            color: '#606266',
            fontWeight: '600',
            borderBottom: '1px solid #ebeef5'
          }"
          :row-style="{ height: '60px' }"
          empty-text="暂无用户数据"
          @selection-change="handleSelectionChange"
        >
          <!-- 勾选列 -->
          <el-table-column type="selection" width="55" align="center" />

          <!-- 序号列 -->
          <el-table-column label="序号" width="70" align="center">
            <template #default="{ $index }">
              <span class="row-number">{{ (pagination.current - 1) * pagination.size + $index + 1 }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="id" label="ID" width="80" align="center">
            <template #default="{ row }">
              <span class="user-id">#{{ row.id }}</span>
            </template>
          </el-table-column>

          <el-table-column label="用户信息" width="250">
            <template #default="{ row }">
              <div class="user-info">
                <el-avatar :size="45" :src="row.avatarUrl" class="user-avatar">
                  <el-icon><User /></el-icon>
                </el-avatar>
                <div class="user-details">
                  <div class="user-name">{{ row.nickname || '未设置昵称' }}</div>
                  <div class="user-id-text">ID: {{ row.id }}</div>
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="phone" label="手机号" width="140" align="center">
            <template #default="{ row }">
              <div class="phone-info">
                <el-icon class="phone-icon"><Phone /></el-icon>
                <span class="phone-number">{{ row.phone || '未绑定' }}</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="gender" label="性别" width="80" align="center">
            <template #default="{ row }">
              <el-tag
                :type="row.gender === 1 ? 'primary' : row.gender === 2 ? 'danger' : 'info'"
                effect="light"
                size="small"
                round
              >
                <span v-if="row.gender === 1">男</span>
                <span v-else-if="row.gender === 2">女</span>
                <span v-else>未知</span>
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="地区信息" min-width="150">
            <template #default="{ row }">
              <div class="location-info">
                <div class="location-main">{{ row.city || '未设置' }}</div>
                <div class="location-sub">{{ row.province || '' }}</div>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="状态" width="100" align="center">
            <template #default="{ row }">
              <el-tag
                :type="row.status === 1 ? 'success' : 'danger'"
                effect="light"
                round
                size="small"
              >
                {{ getUserStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="createdAt" label="注册时间" width="180" align="center">
            <template #default="{ row }">
              <div class="date-info">
                <div class="date-main">{{ formatDate(row.createdAt, 'YYYY-MM-DD') }}</div>
                <div class="date-time">{{ formatDate(row.createdAt, 'HH:mm:ss') }}</div>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="200" fixed="right" align="center">
            <template #default="{ row }">
              <div class="action-buttons">
                <el-tooltip content="查看详情" placement="top">
                  <el-button
                    type="primary"
                    plain
                    size="small"
                    circle
                    @click="viewUserDetail(row)"
                  >
                    <el-icon><View /></el-icon>
                  </el-button>
                </el-tooltip>

                <el-tooltip :content="row.status === 1 ? '禁用用户' : '启用用户'" placement="top">
                  <el-button
                    :type="row.status === 1 ? 'warning' : 'success'"
                    plain
                    size="small"
                    circle
                    @click="toggleUserStatus(row)"
                  >
                    <el-icon v-if="row.status === 1"><Lock /></el-icon>
                    <el-icon v-else><Unlock /></el-icon>
                  </el-button>
                </el-tooltip>

                <el-tooltip content="删除用户" placement="top">
                  <el-button
                    type="danger"
                    plain
                    size="small"
                    circle
                    @click="deleteUser(row)"
                  >
                    <el-icon><Delete /></el-icon>
                  </el-button>
                </el-tooltip>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="pagination.current"
        v-model:page-size="pagination.size"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 用户详情弹窗 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="用户详情"
      width="700px"
      :close-on-click-modal="false"
    >
      <div v-if="selectedUser" class="user-detail">
        <!-- 用户头像和基本信息 -->
        <div class="user-detail-header">
          <el-avatar :size="80" :src="selectedUser.avatarUrl" class="detail-avatar">
            <el-icon size="40"><User /></el-icon>
          </el-avatar>
          <div class="user-basic-info">
            <h3 class="user-nickname">{{ selectedUser.nickname || '未设置昵称' }}</h3>
            <p class="user-id">用户ID: #{{ selectedUser.id }}</p>
            <el-tag
              :type="selectedUser.status === 1 ? 'success' : 'danger'"
              size="large"
              effect="light"
            >
              {{ getUserStatusText(selectedUser.status) }}
            </el-tag>
          </div>
        </div>

        <!-- 详细信息 -->
        <el-descriptions :column="2" border class="detail-descriptions">
          <el-descriptions-item label="微信OpenID" :span="2">
            <span class="openid-text">{{ selectedUser.openid || '未绑定' }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="微信UnionID" :span="2">
            <span class="unionid-text">{{ selectedUser.unionid || '未绑定' }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="手机号码">
            {{ selectedUser.phone || '未绑定' }}
          </el-descriptions-item>
          <el-descriptions-item label="性别">
            <el-tag
              :type="selectedUser.gender === 1 ? 'primary' : selectedUser.gender === 2 ? 'danger' : 'info'"
              effect="light"
              size="small"
            >
              <span v-if="selectedUser.gender === 1">男</span>
              <span v-else-if="selectedUser.gender === 2">女</span>
              <span v-else>未知</span>
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="所在城市">
            {{ selectedUser.city || '未设置' }}
          </el-descriptions-item>
          <el-descriptions-item label="所在省份">
            {{ selectedUser.province || '未设置' }}
          </el-descriptions-item>
          <el-descriptions-item label="所在国家">
            {{ selectedUser.country || '未设置' }}
          </el-descriptions-item>
          <el-descriptions-item label="账户状态">
            <el-tag :type="selectedUser.status === 1 ? 'success' : 'danger'" effect="light">
              {{ getUserStatusText(selectedUser.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="注册时间">
            {{ formatDate(selectedUser.createdAt) }}
          </el-descriptions-item>
          <el-descriptions-item label="最后更新">
            {{ formatDate(selectedUser.updatedAt) }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailDialogVisible = false">关闭</el-button>
          <el-button
            type="primary"
            @click="editUser(selectedUser)"
            v-if="selectedUser?.status === 1"
          >
            编辑用户
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 编辑用户弹窗 -->
    <el-dialog
      v-model="editDialogVisible"
      title="编辑用户信息"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="editFormRef"
        :model="editForm"
        :rules="editRules"
        label-width="100px"
        class="edit-form"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="用户昵称" prop="nickname">
              <el-input
                v-model="editForm.nickname"
                placeholder="请输入用户昵称"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="手机号码" prop="phone">
              <el-input
                v-model="editForm.phone"
                placeholder="请输入手机号码"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="性别" prop="gender">
              <el-select v-model="editForm.gender" placeholder="请选择性别" style="width: 100%">
                <el-option label="未知" :value="0" />
                <el-option label="男" :value="1" />
                <el-option label="女" :value="2" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="账户状态" prop="status">
              <el-select v-model="editForm.status" placeholder="请选择状态" style="width: 100%">
                <el-option label="正常" :value="1" />
                <el-option label="禁用" :value="0" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="所在城市" prop="city">
              <el-input
                v-model="editForm.city"
                placeholder="请输入城市"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="所在省份" prop="province">
              <el-input
                v-model="editForm.province"
                placeholder="请输入省份"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="所在国家" prop="country">
              <el-input
                v-model="editForm.country"
                placeholder="请输入国家"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="头像URL" prop="avatarUrl">
          <el-input
            v-model="editForm.avatarUrl"
            placeholder="请输入头像URL"
            clearable
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="editDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleUpdateUser" :loading="updateLoading">
            保存
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { adminApi } from '@/api/admin'
import { formatDate, getUserStatusText } from '@/utils'

const loading = ref(false)
const users = ref([])
const selectedUser = ref(null)
const detailDialogVisible = ref(false)
const editDialogVisible = ref(false)
const updateLoading = ref(false)
const totalUsers = ref(0)
const activeUsers = ref(0)
const editFormRef = ref(null)

const searchForm = reactive({
  keyword: '',
  status: ''
})

const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

// 编辑表单数据
const editForm = reactive({
  id: null,
  nickname: '',
  phone: '',
  gender: 0,
  status: 1,
  city: '',
  province: '',
  country: '',
  avatarUrl: ''
})

// 编辑表单验证规则
const editRules = reactive({
  nickname: [
    { required: true, message: '请输入用户昵称', trigger: 'blur' },
    { min: 1, max: 20, message: '昵称长度在 1 到 20 个字符', trigger: 'blur' }
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ]
})

// 快速筛选标签
const quickFilters = ref([])

// 选中的用户
const selectedUsers = ref([])

// 添加快速筛选
const addQuickFilter = (key, label, type = 'info') => {
  const exists = quickFilters.value.find(f => f.key === key)
  if (!exists) {
    quickFilters.value.push({ key, label, type })
  }
}

// 移除快速筛选
const removeQuickFilter = (key) => {
  const index = quickFilters.value.findIndex(f => f.key === key)
  if (index > -1) {
    quickFilters.value.splice(index, 1)
    // 根据key重置对应的搜索条件
    if (key === 'keyword') {
      searchForm.keyword = ''
    } else if (key === 'status') {
      searchForm.status = ''
    }
    handleSearch()
  }
}

// 加载用户列表
const loadUsers = async () => {
  loading.value = true
  try {
    const params = {
      current: pagination.current,
      size: pagination.size,
      keyword: searchForm.keyword,
      status: searchForm.status
    }

    const response = await adminApi.getUsers(params)
    users.value = response.data.records
    pagination.total = response.data.total

    // 更新统计数据
    totalUsers.value = response.data.total
    activeUsers.value = response.data.records.filter(user => user.status === 1).length
  } catch (error) {
    console.error('加载用户列表失败:', error)
    ElMessage.error('加载用户列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索用户
const handleSearch = () => {
  // 更新快速筛选标签
  quickFilters.value = []

  if (searchForm.keyword) {
    addQuickFilter('keyword', `关键词: ${searchForm.keyword}`, 'primary')
  }

  if (searchForm.status !== '') {
    const statusText = searchForm.status === 1 ? '正常用户' : '禁用用户'
    addQuickFilter('status', `状态: ${statusText}`, searchForm.status === 1 ? 'success' : 'danger')
  }

  pagination.current = 1
  loadUsers()
}

// 重置搜索
const handleReset = () => {
  searchForm.keyword = ''
  searchForm.status = ''
  quickFilters.value = []
  pagination.current = 1
  loadUsers()
  ElMessage.success('搜索条件已重置')
}

// 刷新数据
const handleRefresh = () => {
  // 保持当前搜索条件，重新加载数据
  loadUsers()
  ElMessage({
    message: '数据已刷新',
    type: 'success',
    duration: 1500,
    showClose: false
  })
}

// 处理选择变化
const handleSelectionChange = (selection) => {
  selectedUsers.value = selection
  console.log('已选择用户:', selection.length, '个')
}

// 批量删除
const handleBatchDelete = async () => {
  if (selectedUsers.value.length === 0) {
    ElMessage.warning('请先选择要删除的用户')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedUsers.value.length} 个用户吗？此操作不可恢复。`,
      '批量删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: true
      }
    )

    const ids = selectedUsers.value.map(user => user.id)
    const response = await adminApi.batchDeleteUsers(ids)

    if (response.code === 200) {
      ElMessage.success(response.message || `已删除 ${selectedUsers.value.length} 个用户`)
      selectedUsers.value = []
      await loadUsers() // 重新加载用户列表
    } else {
      ElMessage.error(response.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除用户失败:', error)
      ElMessage.error('删除失败: ' + (error.message || '网络错误'))
    }
  }
}

// 导出用户数据为CSV
const exportToCSV = (data, filename) => {
  // CSV头部
  const headers = ['ID', '昵称', '电话', '性别', '城市', '状态', '注册时间']

  // 转换数据
  const csvData = data.map(user => [
    user.id,
    user.nickName || '未设置昵称',
    user.phone,
    user.gender === 1 ? '男' : user.gender === 2 ? '女' : '未知',
    user.city || '未设置',
    user.status === 1 ? '正常' : '禁用',
    new Date(user.createTime).toLocaleString('zh-CN')
  ])

  // 组合CSV内容
  const csvContent = [headers, ...csvData]
    .map(row => row.map(field => `"${field}"`).join(','))
    .join('\n')

  // 创建并下载文件
  const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')
  const url = URL.createObjectURL(blob)
  link.setAttribute('href', url)
  link.setAttribute('download', filename)
  link.style.visibility = 'hidden'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

// 导出处理
const handleExport = (command) => {
  switch (command) {
    case 'current':
      if (users.value.length === 0) {
        ElMessage.warning('当前页面没有用户数据')
        return
      }
      exportToCSV(users.value, `用户数据_第${pagination.current}页_${new Date().toLocaleDateString()}.csv`)
      ElMessage.success(`已导出当前页 ${users.value.length} 条用户数据`)
      break
    case 'all':
      ElMessage.info('正在导出全部数据，请稍候...')
      // 这里应该调用API获取所有用户数据
      // 为了演示，我们使用当前数据
      exportToCSV(users.value, `全部用户数据_${new Date().toLocaleDateString()}.csv`)
      ElMessage.success('全部用户数据导出完成')
      break
    case 'selected':
      if (selectedUsers.value.length === 0) {
        ElMessage.warning('请先选择要导出的用户')
        return
      }
      exportToCSV(selectedUsers.value, `选中用户数据_${selectedUsers.value.length}条_${new Date().toLocaleDateString()}.csv`)
      ElMessage.success(`已导出选中的 ${selectedUsers.value.length} 条用户数据`)
      break
  }
}

// 分页大小改变
const handleSizeChange = (size) => {
  pagination.size = size
  pagination.current = 1
  loadUsers()
}

// 当前页改变
const handleCurrentChange = (current) => {
  pagination.current = current
  loadUsers()
}

// 查看用户详情
const viewUserDetail = async (user) => {
  try {
    // 如果后端有详情接口，使用接口数据，否则直接使用列表数据
    try {
      const response = await adminApi.getUserDetail(user.id)
      selectedUser.value = response.data
    } catch (apiError) {
      // 如果接口调用失败，使用当前行数据
      console.warn('获取用户详情接口失败，使用列表数据:', apiError)
      selectedUser.value = user
    }
    detailDialogVisible.value = true
  } catch (error) {
    console.error('获取用户详情失败:', error)
    ElMessage.error('获取用户详情失败')
  }
}

// 编辑用户
const editUser = (user) => {
  // 填充编辑表单
  editForm.id = user.id
  editForm.nickname = user.nickname || ''
  editForm.phone = user.phone || ''
  editForm.gender = user.gender || 0
  editForm.status = user.status || 1
  editForm.city = user.city || ''
  editForm.province = user.province || ''
  editForm.country = user.country || ''
  editForm.avatarUrl = user.avatarUrl || ''

  editDialogVisible.value = true
}

// 更新用户信息
const handleUpdateUser = async () => {
  try {
    // 表单验证
    await editFormRef.value.validate()

    updateLoading.value = true

    // 调用更新接口
    await adminApi.updateUser(editForm.id, editForm)

    ElMessage.success('用户信息更新成功')
    editDialogVisible.value = false

    // 刷新列表
    loadUsers()
  } catch (error) {
    if (error !== false) { // 不是表单验证错误
      console.error('更新用户信息失败:', error)
      ElMessage.error('更新用户信息失败')
    }
  } finally {
    updateLoading.value = false
  }
}

// 切换用户状态
const toggleUserStatus = async (user) => {
  const action = user.status === 1 ? '禁用' : '启用'
  const newStatus = user.status === 1 ? 0 : 1

  try {
    await ElMessageBox.confirm(
      `确定要${action}用户 "${user.nickname || user.phone}" 吗？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await adminApi.updateUserStatus(user.id, newStatus)
    ElMessage.success(`${action}成功`)
    loadUsers()
  } catch (error) {
    if (error !== 'cancel') {
      console.error(`${action}用户失败:`, error)
      ElMessage.error(`${action}用户失败`)
    }
  }
}

// 删除用户
const deleteUser = async (user) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除用户 "${user.nickname || user.phone}" 吗？此操作不可恢复！`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await adminApi.deleteUser(user.id)
    ElMessage.success('删除成功')
    loadUsers()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除用户失败:', error)
      ElMessage.error('删除用户失败')
    }
  }
}

onMounted(() => {
  loadUsers()
})
</script>

<style scoped>
.users-page {
  min-height: 100vh;
  background: #f8fafc;
  padding: 20px;
}

/* 用户控制面板样式 */
.user-control-panel {
  margin-bottom: 16px;
}

.control-card {
  border-radius: 20px;
  border: none;
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.08);
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20px);
  overflow: hidden;
  position: relative;
}

.control-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.control-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2px 0;
  gap: 24px;
}

.page-info {
  flex: 1;
}

.page-title-section {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.page-title {
  font-size: 24px;
  font-weight: 700;
  color: #2c3e50;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 10px;
}

.title-icon {
  font-size: 26px;
  color: #667eea;
}

.page-subtitle {
  font-size: 13px;
  color: #7f8c8d;
  margin: 0;
  font-weight: 500;
}

.stats-section {
  flex-shrink: 0;
}

.stats-cards {
  display: flex;
  gap: 12px;
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  border-radius: 12px;
  min-width: 120px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.stat-card.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.stat-card.success {
  background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
  color: white;
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 20px;
  font-weight: 700;
  margin-bottom: 2px;
  line-height: 1;
}

.stat-label {
  font-size: 11px;
  opacity: 0.9;
  font-weight: 500;
}

.stat-icon {
  font-size: 24px;
  opacity: 0.8;
}

.quick-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.search-content {
  padding: 6px 0 4px 0;
  border-top: 1px solid rgba(102, 126, 234, 0.1);
  margin-top: 2px;
}

.search-group {
  margin-bottom: 4px;
}

.search-label {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  font-weight: 600;
  color: #5a6c7d;
  margin-bottom: 8px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.search-label .el-icon {
  font-size: 14px;
  color: #667eea;
}

.search-input-wrapper {
  position: relative;
}

.search-input {
  border-radius: 14px;
  border: 2px solid #e9ecef;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.9);
}

.search-input:focus-within {
  border-color: #667eea;
  box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.08);
  background: rgba(255, 255, 255, 1);
}

.search-icon {
  color: #667eea;
  font-size: 16px;
}

.help-icon {
  color: #bbb;
  font-size: 14px;
  cursor: help;
  transition: color 0.3s ease;
}

.help-icon:hover {
  color: #667eea;
}

.status-select {
  width: 100%;
}

.status-select .el-select__wrapper {
  border-radius: 14px;
  border: 2px solid #e9ecef;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.9);
}

.status-select .el-select__wrapper:hover,
.status-select .el-select__wrapper.is-focused {
  border-color: #667eea;
  box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.08);
  background: rgba(255, 255, 255, 1);
}

.option-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #606266 !important;
}

.option-item span {
  color: inherit !important;
}

.option-item .el-icon {
  color: #909399 !important;
}

.el-option:hover .option-item {
  color: #409eff !important;
}

.el-option:hover .option-item .el-icon {
  color: #409eff !important;
}

.el-option.selected .option-item {
  color: #ffffff !important;
}

.el-option.selected .option-item .el-icon {
  color: #ffffff !important;
}

.status-icon.success {
  color: #67c23a;
}

.status-icon.danger {
  color: #f56c6c;
}

.search-actions {
  display: flex;
  gap: 12px;
  align-items: flex-end;
  height: 100%;
  padding-top: 24px;
}

.search-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 14px;
  padding: 12px 20px;
  font-weight: 600;
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.25);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.search-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.search-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.35);
}

.search-btn:hover::before {
  left: 100%;
}

.reset-btn {
  border: 2px solid #e9ecef;
  border-radius: 14px;
  padding: 12px 20px;
  font-weight: 600;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.9);
}

.reset-btn:hover {
  border-color: #667eea;
  color: #667eea;
  background: rgba(102, 126, 234, 0.05);
  transform: translateY(-2px);
}

.export-btn {
  border: 2px solid #f39c12;
  color: #f39c12;
  border-radius: 14px;
  padding: 12px 16px;
  font-weight: 600;
  transition: all 0.3s ease;
  background: rgba(243, 156, 18, 0.05);
}

.export-btn:hover {
  background: #f39c12;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(243, 156, 18, 0.3);
}

.export-dropdown {
  margin-left: 8px;
}

.dropdown-icon {
  margin-left: 4px;
  font-size: 12px;
  transition: transform 0.3s ease;
}

.export-dropdown.is-active .dropdown-icon {
  transform: rotate(180deg);
}

/* 快速筛选标签 */
.quick-filters {
  margin-top: 4px;
  padding-top: 4px;
  border-top: 1px solid rgba(102, 126, 234, 0.08);
  display: flex;
  align-items: center;
  gap: 10px;
  flex-wrap: wrap;
}

.quick-filters-label {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  font-weight: 600;
  color: #7f8c8d;
  white-space: nowrap;
}

.quick-filters-label .el-icon {
  color: #667eea;
}

.quick-filters-tags {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.quick-filter-tag {
  border-radius: 20px;
  padding: 4px 12px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.quick-filter-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 表格区域样式 */
.table-section {
  margin-bottom: 24px;
}

.table-card {
  border-radius: 20px;
  border: none;
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.08);
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20px);
  overflow: hidden;
  position: relative;
}

.table-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
}

.table-title {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 8px;
}

.table-title .el-icon {
  color: #667eea;
}

.selected-count {
  margin-left: 8px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  color: white;
  font-weight: 600;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.table-actions {
  display: flex;
  gap: 12px;
}

.modern-table {
  border-radius: 12px;
  overflow: hidden;
}

.modern-table :deep(.el-table__header) {
  background: #fafafa;
}

.modern-table :deep(.el-table__row) {
  transition: all 0.3s ease;
}

.modern-table :deep(.el-table__row:hover) {
  background: #f8f9ff;
  transform: scale(1.01);
}

/* 用户信息样式 */
.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-avatar {
  border: 3px solid #e9ecef;
  transition: all 0.3s ease;
}

.user-avatar:hover {
  border-color: #667eea;
  transform: scale(1.1);
}

.user-details {
  flex: 1;
}

.user-name {
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 4px;
}

.user-phone {
  font-size: 12px;
  color: #7f8c8d;
}

.user-id {
  font-weight: 600;
  color: #667eea;
}

.user-email, .user-city {
  color: #5a6c7d;
}

.date-info {
  text-align: center;
}

.date-main {
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 2px;
}

.date-time {
  font-size: 12px;
  color: #7f8c8d;
}

/* 地区信息样式 */
.location-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.location-main {
  font-weight: 500;
  color: #303133;
  font-size: 14px;
}

.location-sub {
  font-size: 12px;
  color: #909399;
}

/* 用户详情弹窗样式 */
.user-detail-header {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 24px;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  color: white;
}

.detail-avatar {
  border: 3px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.user-basic-info {
  flex: 1;
}

.user-nickname {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: white;
}

.user-id {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
}

.detail-descriptions {
  margin-top: 20px;
}

.openid-text, .unionid-text {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  color: #666;
  word-break: break-all;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 手机号信息样式 */
.phone-info {
  display: flex;
  align-items: center;
  gap: 6px;
  justify-content: center;
}

.phone-icon {
  color: #409eff;
  font-size: 14px;
}

.phone-number {
  font-weight: 500;
  color: #303133;
  font-size: 13px;
}

.user-id-text {
  font-size: 12px;
  color: #909399;
}

/* 序号样式 */
.row-number {
  color: #606266;
  font-size: 14px;
  font-weight: 500;
}

/* 编辑表单样式 */
.edit-form {
  padding: 20px 0;
}

.edit-form .el-form-item {
  margin-bottom: 20px;
}

.edit-form .el-input,
.edit-form .el-select {
  width: 100%;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.action-buttons .el-button {
  transition: all 0.3s ease;
}

.action-buttons .el-button:hover {
  transform: translateY(-2px);
}

/* 分页样式 */
.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 24px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* 对话框样式 */
:deep(.el-dialog) {
  border-radius: 16px;
  overflow: hidden;
}

:deep(.el-dialog__header) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px 24px;
}

:deep(.el-dialog__title) {
  color: white;
  font-weight: 600;
}

.user-detail {
  padding: 24px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .users-page {
    padding: 12px;
  }

  .header-content {
    flex-direction: column;
    gap: 16px;
    padding: 20px;
  }

  .stats-cards {
    justify-content: center;
  }

  .search-actions {
    flex-direction: column;
  }

  .table-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.users-page > * {
  animation: fadeInUp 0.6s ease-out;
}

.users-page > *:nth-child(2) {
  animation-delay: 0.1s;
}

.users-page > *:nth-child(3) {
  animation-delay: 0.2s;
}
</style>
