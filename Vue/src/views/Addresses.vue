<template>
  <el-container class="addresses-container">
    <!-- 页面头部 -->
    <el-header class="page-header" height="auto">
      <div class="header-content">
        <div class="title-section">
          <h1 class="page-title">
            <el-icon class="title-icon"><Location /></el-icon>
            地址管理
          </h1>
          <p class="page-subtitle">管理系统中的省市区地址信息</p>
        </div>
      </div>
    </el-header>

    <!-- 主要内容区域 -->
    <el-main class="main-content">
      <!-- 统计卡片区域 - 优化为更紧凑的布局 -->
      <div class="stats-section">
        <el-row :gutter="16">
          <el-col :xs="24" :sm="8" :md="8">
            <div class="stats-card primary">
              <div class="stats-content">
                <el-icon class="stats-icon"><Location /></el-icon>
                <div class="stats-info">
                  <div class="stats-value">{{ provinces.length }}</div>
                  <div class="stats-label">省份总数</div>
                </div>
              </div>
            </div>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8">
            <div class="stats-card success">
              <div class="stats-content">
                <el-icon class="stats-icon"><OfficeBuilding /></el-icon>
                <div class="stats-info">
                  <div class="stats-value">{{ totalCities }}</div>
                  <div class="stats-label">城市总数</div>
                </div>
              </div>
            </div>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8">
            <div class="stats-card warning">
              <div class="stats-content">
                <el-icon class="stats-icon"><MapLocation /></el-icon>
                <div class="stats-info">
                  <div class="stats-value">{{ totalDistricts }}</div>
                  <div class="stats-label">区县总数</div>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 地区数据展示 -->
      <el-card class="content-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span class="card-title">地区数据管理</span>
            <div class="header-actions">
              <el-button @click="handleRefresh" :loading="loading">
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
              <el-button type="primary" @click="showAddProvinceDialog">
                <el-icon><Plus /></el-icon>
                添加省份
              </el-button>
              <el-button
                type="danger"
                :disabled="selectedProvinces.length === 0"
                @click="batchDeleteProvinces"
              >
                <el-icon><Delete /></el-icon>
                批量删除
              </el-button>
            </div>
          </div>
        </template>

        <el-tabs v-model="activeTab" type="border-card" class="address-tabs">
          <el-tab-pane name="provinces">
            <template #label>
              <span class="tab-label">
                <el-icon><Location /></el-icon>
                省份管理
              </span>
            </template>

            <!-- 省份搜索栏 -->
            <div class="search-header">
              <el-input
                v-model="provinceSearchKeyword"
                placeholder="搜索省份名称"
                style="width: 300px"
                clearable
                @input="handleProvinceSearch"
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
            </div>

            <div class="table-container">
              <el-table
                :data="paginatedProvinces"
                :loading="provincesLoading"
                stripe
                style="width: 100%"
                :header-cell-style="{ background: '#f8f9fa', color: '#606266' }"
                @selection-change="handleProvinceSelectionChange"
              >
                <!-- 多选列 -->
                <el-table-column type="selection" width="55" align="center" />

                <!-- 序号列 -->
                <el-table-column label="序号" width="70" align="center">
                  <template #default="{ $index }">
                    <span class="row-number">{{ $index + 1 }}</span>
                  </template>
                </el-table-column>

                <el-table-column prop="code" label="省份代码" width="120" align="center">
                  <template #default="{ row }">
                    <el-tag type="info" size="small">{{ row.code }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="name" label="省份名称" min-width="150">
                  <template #default="{ row }">
                    <div class="name-cell">
                      <el-icon class="location-icon"><Location /></el-icon>
                      <span class="name-text">{{ row.name }}</span>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column prop="createdAt" label="创建时间" width="160" align="center">
                  <template #default="{ row }">
                    <span class="time-text">{{ formatDate(row.createdAt) }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="240" align="center">
                  <template #default="{ row }">
                    <div class="table-actions-inline">
                      <el-button
                        type="primary"
                        size="small"
                        plain
                        @click="viewCities(row)"
                      >
                        <el-icon><View /></el-icon>
                        查看城市
                      </el-button>
                      <el-button
                        type="warning"
                        size="small"
                        plain
                        @click="editProvince(row)"
                      >
                        <el-icon><Edit /></el-icon>
                        编辑
                      </el-button>
                      <el-button
                        type="danger"
                        size="small"
                        plain
                        @click="deleteProvince(row)"
                      >
                        <el-icon><Delete /></el-icon>
                        删除
                      </el-button>
                    </div>
                  </template>
                </el-table-column>
              </el-table>

              <!-- 省份分页 -->
              <div class="pagination-container">
                <el-pagination
                  v-model:current-page="provincesPagination.current"
                  v-model:page-size="provincesPagination.size"
                  :page-sizes="[10, 20, 50, 100]"
                  :total="filteredProvinces.length"
                  layout="total, sizes, prev, pager, next, jumper"
                  @size-change="handleProvincesSizeChange"
                  @current-change="handleProvincesCurrentChange"
                />
              </div>
            </div>
          </el-tab-pane>

          <el-tab-pane name="cities">
            <template #label>
              <span class="tab-label">
                <el-icon><OfficeBuilding /></el-icon>
                城市管理
              </span>
            </template>

            <!-- 城市筛选和操作栏 -->
            <div class="city-header">
              <div class="city-filters">
                <el-select
                  v-model="selectedProvinceCode"
                  placeholder="选择省份"
                  @change="onProvinceChange"
                  style="width: 200px; margin-right: 12px;"
                  clearable
                  filterable
                >
                  <el-option
                    v-for="province in provinces"
                    :key="province.code"
                    :label="province.name"
                    :value="province.code"
                  >
                    <span class="option-item">
                      <el-icon><Location /></el-icon>
                      {{ province.name }}
                    </span>
                  </el-option>
                </el-select>

                <div class="filter-tags">
                  <el-tag v-if="selectedProvinceCode" type="primary" size="small">
                    已选择省份：{{ provinces.find(p => p.code === selectedProvinceCode)?.name }}
                  </el-tag>
                  <span v-else class="hint-text">请选择省份查看对应城市</span>
                </div>
              </div>

              <div class="city-actions">
                <el-button
                  type="primary"
                  :disabled="!selectedProvinceCode"
                  @click="showAddCityDialog"
                >
                  <el-icon><Plus /></el-icon>
                  添加城市
                </el-button>
                <el-button
                  type="danger"
                  :disabled="selectedCities.length === 0"
                  @click="batchDeleteCities"
                >
                  <el-icon><Delete /></el-icon>
                  批量删除
                </el-button>
              </div>
            </div>

            <!-- 城市搜索栏 -->
            <div class="search-header">
              <el-input
                v-model="citySearchKeyword"
                placeholder="搜索城市名称"
                style="width: 300px"
                clearable
                @input="handleCitySearch"
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
            </div>

            <div class="table-container">
              <el-table
                :data="paginatedCities"
                :loading="citiesLoading"
                stripe
                style="width: 100%"
                :header-cell-style="{ background: '#f8f9fa', color: '#606266' }"
                empty-text="请先选择省份"
                @selection-change="handleCitySelectionChange"
              >
                <!-- 多选列 -->
                <el-table-column type="selection" width="55" align="center" />

                <!-- 序号列 -->
                <el-table-column label="序号" width="70" align="center">
                  <template #default="{ $index }">
                    <span class="row-number">{{ (citiesPagination.current - 1) * citiesPagination.size + $index + 1 }}</span>
                  </template>
                </el-table-column>

                <el-table-column prop="code" label="城市代码" width="120" align="center">
                  <template #default="{ row }">
                    <el-tag type="success" size="small">{{ row.code }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="name" label="城市名称" min-width="150">
                  <template #default="{ row }">
                    <div class="name-cell">
                      <el-icon class="location-icon"><OfficeBuilding /></el-icon>
                      <span class="name-text">{{ row.name }}</span>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column prop="provinceCode" label="所属省份" width="120" align="center">
                  <template #default="{ row }">
                    <el-tag type="info" size="small">{{ row.provinceCode }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="createdAt" label="创建时间" width="140" align="center">
                  <template #default="{ row }">
                    <span class="time-text">{{ formatDate(row.createdAt) }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="240" align="center">
                  <template #default="{ row }">
                    <div class="table-actions-inline">
                      <el-button
                        type="success"
                        size="small"
                        plain
                        @click="viewDistricts(row)"
                      >
                        <el-icon><View /></el-icon>
                        查看区县
                      </el-button>
                      <el-button
                        type="warning"
                        size="small"
                        plain
                        @click="editCity(row)"
                      >
                        <el-icon><Edit /></el-icon>
                        编辑
                      </el-button>
                      <el-button
                        type="danger"
                        size="small"
                        plain
                        @click="deleteCity(row)"
                      >
                        <el-icon><Delete /></el-icon>
                        删除
                      </el-button>
                    </div>
                  </template>
                </el-table-column>
              </el-table>

              <!-- 城市分页 -->
              <div class="pagination-container" v-if="cities.length > 0">
                <el-pagination
                  v-model:current-page="citiesPagination.current"
                  v-model:page-size="citiesPagination.size"
                  :page-sizes="[10, 20, 50, 100]"
                  :total="filteredCities.length"
                  layout="total, sizes, prev, pager, next, jumper"
                  @size-change="handleCitiesSizeChange"
                  @current-change="handleCitiesCurrentChange"
                />
              </div>
            </div>
          </el-tab-pane>

          <el-tab-pane name="districts">
            <template #label>
              <span class="tab-label">
                <el-icon><MapLocation /></el-icon>
                区县管理
              </span>
            </template>

            <!-- 区县筛选和操作栏 -->
            <div class="district-header">
              <div class="district-filters">
                <el-select
                  v-model="selectedProvinceCode"
                  placeholder="选择省份"
                  @change="onProvinceChange"
                  style="width: 200px; margin-right: 12px;"
                  clearable
                  filterable
                >
                  <el-option
                    v-for="province in provinces"
                    :key="province.code"
                    :label="province.name"
                    :value="province.code"
                  >
                    <span class="option-item">
                      <el-icon><Location /></el-icon>
                      {{ province.name }}
                    </span>
                  </el-option>
                </el-select>

                <el-select
                  v-model="selectedCityCode"
                  placeholder="选择城市"
                  @change="loadDistricts"
                  style="width: 200px; margin-right: 12px;"
                  :disabled="!selectedProvinceCode"
                  clearable
                  filterable
                >
                  <el-option
                    v-for="city in cities"
                    :key="city.code"
                    :label="city.name"
                    :value="city.code"
                  >
                    <span class="option-item">
                      <el-icon><OfficeBuilding /></el-icon>
                      {{ city.name }}
                    </span>
                  </el-option>
                </el-select>

                <div class="filter-tags">
                  <el-tag v-if="selectedProvinceCode" type="primary" size="small">
                    省份：{{ provinces.find(p => p.code === selectedProvinceCode)?.name }}
                  </el-tag>
                  <el-tag v-if="selectedCityCode" type="success" size="small" style="margin-left: 8px;">
                    城市：{{ cities.find(c => c.code === selectedCityCode)?.name }}
                  </el-tag>
                  <span v-if="!selectedProvinceCode" class="hint-text">请先选择省份和城市</span>
                </div>
              </div>

              <div class="district-actions">
                <el-button
                  type="primary"
                  :disabled="!selectedCityCode"
                  @click="showAddDistrictDialog"
                >
                  <el-icon><Plus /></el-icon>
                  添加区县
                </el-button>
                <el-button
                  type="danger"
                  :disabled="selectedDistricts.length === 0"
                  @click="batchDeleteDistricts"
                >
                  <el-icon><Delete /></el-icon>
                  批量删除
                </el-button>
              </div>
            </div>

            <!-- 区县搜索栏 -->
            <div class="search-header">
              <el-input
                v-model="districtSearchKeyword"
                placeholder="搜索区县名称"
                style="width: 300px"
                clearable
                @input="handleDistrictSearch"
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
            </div>

            <div class="table-container">
              <el-table
                :data="paginatedDistricts"
                :loading="districtsLoading"
                stripe
                style="width: 100%"
                :header-cell-style="{ background: '#f8f9fa', color: '#606266' }"
                empty-text="请先选择省份和城市"
                @selection-change="handleDistrictSelectionChange"
              >
                <!-- 多选列 -->
                <el-table-column type="selection" width="55" align="center" />

                <el-table-column prop="code" label="区县代码" width="120" align="center">
                  <template #default="{ row }">
                    <el-tag type="warning" size="small">{{ row.code }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="name" label="区县名称" min-width="150">
                  <template #default="{ row }">
                    <div class="name-cell">
                      <el-icon class="location-icon"><MapLocation /></el-icon>
                      <span class="name-text">{{ row.name }}</span>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column prop="cityCode" label="所属城市" width="120" align="center">
                  <template #default="{ row }">
                    <el-tag type="success" size="small">{{ row.cityCode }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="createdAt" label="创建时间" width="140" align="center">
                  <template #default="{ row }">
                    <span class="time-text">{{ formatDate(row.createdAt) }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="160" align="center">
                  <template #default="{ row }">
                    <div class="table-actions-inline">
                      <el-button
                        type="warning"
                        size="small"
                        plain
                        @click="editDistrict(row)"
                      >
                        <el-icon><Edit /></el-icon>
                        编辑
                      </el-button>
                      <el-button
                        type="danger"
                        size="small"
                        plain
                        @click="deleteDistrict(row)"
                      >
                        <el-icon><Delete /></el-icon>
                        删除
                      </el-button>
                    </div>
                  </template>
                </el-table-column>
              </el-table>

              <!-- 区县分页 -->
              <div class="pagination-container" v-if="districts.length > 0">
                <el-pagination
                  v-model:current-page="districtsPagination.current"
                  v-model:page-size="districtsPagination.size"
                  :page-sizes="[10, 20, 50, 100]"
                  :total="filteredDistricts.length"
                  layout="total, sizes, prev, pager, next, jumper"
                  @size-change="handleDistrictsSizeChange"
                  @current-change="handleDistrictsCurrentChange"
                />
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </el-card>
    </el-main>

    <!-- 省份添加/编辑对话框 -->
    <el-dialog
      v-model="provinceDialogVisible"
      :title="isEditProvince ? '编辑省份' : '添加省份'"
      width="500px"
    >
      <el-form
        ref="provinceFormRef"
        :model="provinceForm"
        :rules="provinceFormRules"
        label-width="80px"
      >
        <el-form-item label="省份代码" prop="code">
          <el-input
            v-model="provinceForm.code"
            placeholder="请输入省份代码"
            :disabled="isEditProvince"
          />
        </el-form-item>
        <el-form-item label="省份名称" prop="name">
          <el-input v-model="provinceForm.name" placeholder="请输入省份名称" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="provinceDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitProvinceForm" :loading="submitting">
          {{ isEditProvince ? '更新' : '添加' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 城市添加/编辑对话框 -->
    <el-dialog
      v-model="cityDialogVisible"
      :title="isEditCity ? '编辑城市' : '添加城市'"
      width="500px"
    >
      <el-form
        ref="cityFormRef"
        :model="cityForm"
        :rules="cityFormRules"
        label-width="80px"
      >
        <el-form-item label="城市代码" prop="code">
          <el-input
            v-model="cityForm.code"
            placeholder="请输入城市代码"
            :disabled="isEditCity"
          />
        </el-form-item>
        <el-form-item label="城市名称" prop="name">
          <el-input v-model="cityForm.name" placeholder="请输入城市名称" />
        </el-form-item>
        <el-form-item label="所属省份" prop="provinceCode">
          <el-select
            v-model="cityForm.provinceCode"
            placeholder="请选择省份"
            style="width: 100%"
            :disabled="isEditCity"
          >
            <el-option
              v-for="province in provinces"
              :key="province.code"
              :label="province.name"
              :value="province.code"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="cityDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitCityForm" :loading="submitting">
          {{ isEditCity ? '更新' : '添加' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 区县添加/编辑对话框 -->
    <el-dialog
      v-model="districtDialogVisible"
      :title="isEditDistrict ? '编辑区县' : '添加区县'"
      width="500px"
    >
      <el-form
        ref="districtFormRef"
        :model="districtForm"
        :rules="districtFormRules"
        label-width="80px"
      >
        <el-form-item label="区县代码" prop="code">
          <el-input
            v-model="districtForm.code"
            placeholder="请输入区县代码"
            :disabled="isEditDistrict"
          />
        </el-form-item>
        <el-form-item label="区县名称" prop="name">
          <el-input v-model="districtForm.name" placeholder="请输入区县名称" />
        </el-form-item>
        <el-form-item label="所属城市" prop="cityCode">
          <el-select
            v-model="districtForm.cityCode"
            placeholder="请选择城市"
            style="width: 100%"
            :disabled="isEditDistrict"
          >
            <el-option
              v-for="city in cities"
              :key="city.code"
              :label="city.name"
              :value="city.code"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="districtDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitDistrictForm" :loading="submitting">
          {{ isEditDistrict ? '更新' : '添加' }}
        </el-button>
      </template>
    </el-dialog>
  </el-container>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Location,
  OfficeBuilding,
  MapLocation,
  View,
  Plus,
  Delete,
  Edit,
  Search
} from '@element-plus/icons-vue'
import { adminApi } from '@/api/admin'
import { formatDate } from '@/utils'

const activeTab = ref('provinces')
const loading = ref(false)
const provincesLoading = ref(false)
const citiesLoading = ref(false)
const districtsLoading = ref(false)

const provinces = ref([])
const cities = ref([])
const districts = ref([])

const selectedProvinceCode = ref('')
const selectedCityCode = ref('')

// 统计数据
const addressStats = ref({
  provinceCount: 0,
  cityCount: 0,
  districtCount: 0
})

// 分页数据
const provincesPagination = ref({
  current: 1,
  size: 10
})

const citiesPagination = ref({
  current: 1,
  size: 10
})

const districtsPagination = ref({
  current: 1,
  size: 10
})

// 选中的数据
const selectedProvinces = ref([])
const selectedCities = ref([])
const selectedDistricts = ref([])

// 搜索关键词
const provinceSearchKeyword = ref('')
const citySearchKeyword = ref('')
const districtSearchKeyword = ref('')

// 对话框状态
const provinceDialogVisible = ref(false)
const cityDialogVisible = ref(false)
const districtDialogVisible = ref(false)
const isEditProvince = ref(false)
const isEditCity = ref(false)
const isEditDistrict = ref(false)
const submitting = ref(false)

// 表单引用
const provinceFormRef = ref()
const cityFormRef = ref()
const districtFormRef = ref()

// 表单数据
const provinceForm = ref({
  id: null,
  code: '',
  name: ''
})

const cityForm = ref({
  id: null,
  code: '',
  name: '',
  provinceCode: ''
})

const districtForm = ref({
  id: null,
  code: '',
  name: '',
  cityCode: ''
})

// 表单验证规则
const provinceFormRules = {
  code: [
    { required: true, message: '请输入省份代码', trigger: 'blur' },
    { pattern: /^\d{6}$/, message: '省份代码必须是6位数字', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入省份名称', trigger: 'blur' },
    { min: 2, max: 20, message: '省份名称长度在2到20个字符', trigger: 'blur' }
  ]
}

const cityFormRules = {
  code: [
    { required: true, message: '请输入城市代码', trigger: 'blur' },
    { pattern: /^\d{6}$/, message: '城市代码必须是6位数字', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入城市名称', trigger: 'blur' },
    { min: 2, max: 20, message: '城市名称长度在2到20个字符', trigger: 'blur' }
  ],
  provinceCode: [
    { required: true, message: '请选择所属省份', trigger: 'change' }
  ]
}

const districtFormRules = {
  code: [
    { required: true, message: '请输入区县代码', trigger: 'blur' },
    { pattern: /^\d{6}$/, message: '区县代码必须是6位数字', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入区县名称', trigger: 'blur' },
    { min: 2, max: 20, message: '区县名称长度在2到20个字符', trigger: 'blur' }
  ],
  cityCode: [
    { required: true, message: '请选择所属城市', trigger: 'change' }
  ]
}

// 计算总数 - 使用API获取的统计数据
const totalCities = computed(() => {
  return addressStats.value.cityCount
})

const totalDistricts = computed(() => {
  return addressStats.value.districtCount
})

// 搜索过滤后的省份
const filteredProvinces = computed(() => {
  if (!provinceSearchKeyword.value) {
    return provinces.value
  }
  return provinces.value.filter(province =>
    province.name.toLowerCase().includes(provinceSearchKeyword.value.toLowerCase()) ||
    province.code.includes(provinceSearchKeyword.value)
  )
})

// 搜索过滤后的城市
const filteredCities = computed(() => {
  if (!citySearchKeyword.value) {
    return cities.value
  }
  return cities.value.filter(city =>
    city.name.toLowerCase().includes(citySearchKeyword.value.toLowerCase()) ||
    city.code.includes(citySearchKeyword.value)
  )
})

// 搜索过滤后的区县
const filteredDistricts = computed(() => {
  if (!districtSearchKeyword.value) {
    return districts.value
  }
  return districts.value.filter(district =>
    district.name.toLowerCase().includes(districtSearchKeyword.value.toLowerCase()) ||
    district.code.includes(districtSearchKeyword.value)
  )
})

// 分页计算属性
const paginatedProvinces = computed(() => {
  const start = (provincesPagination.value.current - 1) * provincesPagination.value.size
  const end = start + provincesPagination.value.size
  return filteredProvinces.value.slice(start, end)
})

const paginatedCities = computed(() => {
  const start = (citiesPagination.value.current - 1) * citiesPagination.value.size
  const end = start + citiesPagination.value.size
  return filteredCities.value.slice(start, end)
})

const paginatedDistricts = computed(() => {
  const start = (districtsPagination.value.current - 1) * districtsPagination.value.size
  const end = start + districtsPagination.value.size
  return filteredDistricts.value.slice(start, end)
})

// 加载统计数据
const loadAddressStats = async () => {
  try {
    const response = await adminApi.getAddressStats()
    addressStats.value = response.data
  } catch (error) {
    console.error('加载地址统计失败:', error)
    ElMessage.error('加载地址统计失败')
  }
}

// 加载省份列表
const loadProvinces = async () => {
  provincesLoading.value = true
  try {
    const response = await adminApi.getProvinces()
    provinces.value = response.data
  } catch (error) {
    console.error('加载省份列表失败:', error)
    ElMessage.error('加载省份列表失败')
  } finally {
    provincesLoading.value = false
  }
}

// 加载城市列表
const loadCities = async (provinceCode = selectedProvinceCode.value) => {
  if (!provinceCode) {
    console.log('没有选择省份，无法加载城市')
    return
  }

  console.log('开始加载城市，省份代码:', provinceCode)
  citiesLoading.value = true
  try {
    const response = await adminApi.getCities(provinceCode)
    console.log('城市数据响应:', response)
    cities.value = response.data || []
    console.log('设置城市数据:', cities.value)

    // 重置城市分页到第一页
    citiesPagination.value.current = 1
  } catch (error) {
    console.error('加载城市列表失败:', error)
    ElMessage.error('加载城市列表失败: ' + (error.message || '未知错误'))
    cities.value = []
  } finally {
    citiesLoading.value = false
  }
}

// 加载区县列表
const loadDistricts = async (cityCode = selectedCityCode.value) => {
  if (!cityCode) return
  
  districtsLoading.value = true
  try {
    const response = await adminApi.getDistricts(cityCode)
    districts.value = response.data
  } catch (error) {
    console.error('加载区县列表失败:', error)
    ElMessage.error('加载区县列表失败')
  } finally {
    districtsLoading.value = false
  }
}

// 查看城市
const viewCities = (province) => {
  selectedProvinceCode.value = province.code
  activeTab.value = 'cities'
  loadCities(province.code)
}

// 查看区县
const viewDistricts = (city) => {
  selectedCityCode.value = city.code
  activeTab.value = 'districts'
  loadDistricts(city.code)
}

// 省份改变时
const onProvinceChange = (provinceCode) => {
  console.log('省份改变:', provinceCode)
  selectedCityCode.value = ''
  districts.value = []

  // 清空搜索关键词
  citySearchKeyword.value = ''
  districtSearchKeyword.value = ''

  // 重置分页
  citiesPagination.value.current = 1
  districtsPagination.value.current = 1

  if (provinceCode) {
    console.log('开始加载省份对应的城市:', provinceCode)
    loadCities(provinceCode)
  } else {
    console.log('清空城市列表')
    cities.value = []
  }
}

// 分页事件处理
const handleProvincesSizeChange = (size) => {
  provincesPagination.value.size = size
  provincesPagination.value.current = 1
}

const handleProvincesCurrentChange = (current) => {
  provincesPagination.value.current = current
}

const handleCitiesSizeChange = (size) => {
  citiesPagination.value.size = size
  citiesPagination.value.current = 1
}

const handleCitiesCurrentChange = (current) => {
  citiesPagination.value.current = current
}

const handleDistrictsSizeChange = (size) => {
  districtsPagination.value.size = size
  districtsPagination.value.current = 1
}

const handleDistrictsCurrentChange = (current) => {
  districtsPagination.value.current = current
}

// 选择变化处理
const handleProvinceSelectionChange = (selection) => {
  selectedProvinces.value = selection
}

const handleCitySelectionChange = (selection) => {
  selectedCities.value = selection
}

const handleDistrictSelectionChange = (selection) => {
  selectedDistricts.value = selection
}

// 搜索处理
const handleProvinceSearch = () => {
  provincesPagination.value.current = 1
}

const handleCitySearch = () => {
  citiesPagination.value.current = 1
}

const handleDistrictSearch = () => {
  districtsPagination.value.current = 1
}

// 显示对话框方法
const showAddProvinceDialog = () => {
  isEditProvince.value = false
  provinceForm.value = {
    id: null,
    code: '',
    name: ''
  }
  provinceDialogVisible.value = true
}

const showAddCityDialog = () => {
  isEditCity.value = false
  cityForm.value = {
    id: null,
    code: '',
    name: '',
    provinceCode: selectedProvinceCode.value
  }
  cityDialogVisible.value = true
}

const showAddDistrictDialog = () => {
  isEditDistrict.value = false
  districtForm.value = {
    id: null,
    code: '',
    name: '',
    cityCode: selectedCityCode.value
  }
  districtDialogVisible.value = true
}

// 编辑方法
const editProvince = (province) => {
  isEditProvince.value = true
  provinceForm.value = {
    id: province.id,
    code: province.code,
    name: province.name
  }
  provinceDialogVisible.value = true
}

const editCity = (city) => {
  isEditCity.value = true
  cityForm.value = {
    id: city.id,
    code: city.code,
    name: city.name,
    provinceCode: city.provinceCode
  }
  cityDialogVisible.value = true
}

const editDistrict = (district) => {
  isEditDistrict.value = true
  districtForm.value = {
    id: district.id,
    code: district.code,
    name: district.name,
    cityCode: district.cityCode
  }
  districtDialogVisible.value = true
}

// 删除方法
const deleteProvince = async (province) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除省份"${province.name}"吗？删除后不可恢复！`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    await adminApi.deleteProvince(province.id)
    ElMessage.success('省份删除成功')
    await loadProvinces()
    await loadAddressStats()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除省份失败:', error)
      ElMessage.error(error.message || '删除失败')
    }
  }
}

const deleteCity = async (city) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除城市"${city.name}"吗？删除后不可恢复！`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    await adminApi.deleteCity(city.id)
    ElMessage.success('城市删除成功')
    if (selectedProvinceCode.value) {
      await loadCities(selectedProvinceCode.value)
    }
    await loadAddressStats()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除城市失败:', error)
      ElMessage.error(error.message || '删除失败')
    }
  }
}

const deleteDistrict = async (district) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除区县"${district.name}"吗？删除后不可恢复！`,
      '确确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    await adminApi.deleteDistrict(district.id)
    ElMessage.success('区县删除成功')
    if (selectedCityCode.value) {
      await loadDistricts(selectedCityCode.value)
    }
    await loadAddressStats()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除区县失败:', error)
      ElMessage.error(error.message || '删除失败')
    }
  }
}

// 批量删除方法
const batchDeleteProvinces = async () => {
  if (selectedProvinces.value.length === 0) {
    ElMessage.warning('请选择要删除的省份')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedProvinces.value.length} 个省份吗？删除后不可恢复！`,
      '确认批量删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    // 并发删除所有选中的省份
    const deletePromises = selectedProvinces.value.map(province =>
      adminApi.deleteProvince(province.id)
    )

    await Promise.all(deletePromises)
    ElMessage.success(`成功删除 ${selectedProvinces.value.length} 个省份`)
    selectedProvinces.value = []
    await loadProvinces()
    await loadAddressStats()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除省份失败:', error)
      ElMessage.error(error.message || '批量删除失败')
    }
  }
}

const batchDeleteCities = async () => {
  if (selectedCities.value.length === 0) {
    ElMessage.warning('请选择要删除的城市')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedCities.value.length} 个城市吗？删除后不可恢复！`,
      '确认批量删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    const deletePromises = selectedCities.value.map(city =>
      adminApi.deleteCity(city.id)
    )

    await Promise.all(deletePromises)
    ElMessage.success(`成功删除 ${selectedCities.value.length} 个城市`)
    selectedCities.value = []
    if (selectedProvinceCode.value) {
      await loadCities(selectedProvinceCode.value)
    }
    await loadAddressStats()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除城市失败:', error)
      ElMessage.error(error.message || '批量删除失败')
    }
  }
}

const batchDeleteDistricts = async () => {
  if (selectedDistricts.value.length === 0) {
    ElMessage.warning('请选择要删除的区县')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedDistricts.value.length} 个区县吗？删除后不可恢复！`,
      '确认批量删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    const deletePromises = selectedDistricts.value.map(district =>
      adminApi.deleteDistrict(district.id)
    )

    await Promise.all(deletePromises)
    ElMessage.success(`成功删除 ${selectedDistricts.value.length} 个区县`)
    selectedDistricts.value = []
    if (selectedCityCode.value) {
      await loadDistricts(selectedCityCode.value)
    }
    await loadAddressStats()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除区县失败:', error)
      ElMessage.error(error.message || '批量删除失败')
    }
  }
}

// 表单提交方法
const submitProvinceForm = async () => {
  if (!provinceFormRef.value) return

  try {
    await provinceFormRef.value.validate()
    submitting.value = true

    if (isEditProvince.value) {
      await adminApi.updateProvince(provinceForm.value.id, {
        name: provinceForm.value.name
      })
      ElMessage.success('省份更新成功')
    } else {
      await adminApi.addProvince(provinceForm.value)
      ElMessage.success('省份添加成功')
    }

    provinceDialogVisible.value = false
    await loadProvinces()
    await loadAddressStats()
  } catch (error) {
    console.error('提交省份表单失败:', error)
    ElMessage.error(error.message || '操作失败')
  } finally {
    submitting.value = false
  }
}

const submitCityForm = async () => {
  if (!cityFormRef.value) return

  try {
    await cityFormRef.value.validate()
    submitting.value = true

    if (isEditCity.value) {
      await adminApi.updateCity(cityForm.value.id, {
        name: cityForm.value.name
      })
      ElMessage.success('城市更新成功')
    } else {
      await adminApi.addCity(cityForm.value)
      ElMessage.success('城市添加成功')
    }

    cityDialogVisible.value = false
    if (selectedProvinceCode.value) {
      await loadCities(selectedProvinceCode.value)
    }
    await loadAddressStats()
  } catch (error) {
    console.error('提交城市表单失败:', error)
    ElMessage.error(error.message || '操作失败')
  } finally {
    submitting.value = false
  }
}

const submitDistrictForm = async () => {
  if (!districtFormRef.value) return

  try {
    await districtFormRef.value.validate()
    submitting.value = true

    if (isEditDistrict.value) {
      await adminApi.updateDistrict(districtForm.value.id, {
        name: districtForm.value.name
      })
      ElMessage.success('区县更新成功')
    } else {
      await adminApi.addDistrict(districtForm.value)
      ElMessage.success('区县添加成功')
    }

    districtDialogVisible.value = false
    if (selectedCityCode.value) {
      await loadDistricts(selectedCityCode.value)
    }
    await loadAddressStats()
  } catch (error) {
    console.error('提交区县表单失败:', error)
    ElMessage.error(error.message || '操作失败')
  } finally {
    submitting.value = false
  }
}

// 刷新所有数据
const handleRefresh = async () => {
  loading.value = true
  try {
    await Promise.all([
      loadAddressStats(),
      loadProvinces()
    ])
    ElMessage({
      message: '地址数据已刷新',
      type: 'success',
      duration: 1500,
      showClose: false
    })
  } catch (error) {
    console.error('刷新地址数据失败:', error)
    ElMessage.error('刷新地址数据失败')
  } finally {
    loading.value = false
  }
}

onMounted(async () => {
  console.log('地址管理页面已挂载，开始加载数据...')
  try {
    await Promise.all([
      loadAddressStats(),
      loadProvinces()
    ])
    console.log('地址数据加载完成')
  } catch (error) {
    console.error('地址数据加载失败:', error)
    ElMessage.error('地址数据加载失败: ' + error.message)
  }
})
</script>

<style scoped>
/* 页面容器 */
.addresses-container {
  min-height: 100vh;
  background: transparent;
}

/* 页面头部 */
.page-header {
  background: transparent;
  padding: 0 0 24px 0;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title-section {
  flex: 1;
}

.page-title {
  font-size: 28px;
  font-weight: 700;
  color: #2c3e50;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.title-icon {
  color: #667eea;
  font-size: 32px;
}

.page-subtitle {
  font-size: 14px;
  color: #8892b0;
  margin: 0;
  opacity: 0.8;
}

/* 主要内容区域 */
.main-content {
  padding: 0;
}

/* 统计卡片区域 */
.stats-section {
  margin-bottom: 16px;
}

.stats-card {
  border-radius: 12px;
  padding: 16px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

.stats-card.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.stats-card.success {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
}

.stats-card.warning {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  color: white;
}

.stats-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.stats-icon {
  font-size: 24px;
  opacity: 0.8;
}

.stats-info {
  flex: 1;
}

.stats-value {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 4px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.stats-label {
  font-size: 12px;
  opacity: 0.9;
  font-weight: 500;
}

/* 内容卡片 */
.content-card {
  border-radius: 16px;
  border: none;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

/* 标签页样式 */
.address-tabs {
  border: none;
}

.tab-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
}

/* 筛选区域 */
.filter-section {
  margin-bottom: 24px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 12px;
  border: 1px solid #e9ecef;
}

.filter-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.hint-text {
  color: #8892b0;
  font-size: 13px;
}

.option-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 表格容器 */
.table-container {
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid #e9ecef;
}

/* 表格单元格样式 */
.name-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.location-icon {
  color: #667eea;
  font-size: 16px;
}

.name-text {
  font-weight: 500;
  color: #2c3e50;
}

.time-text {
  color: #8892b0;
  font-size: 13px;
}

.stats-icon {
  opacity: 0.8;
}

.mb-16 {
  margin-bottom: 16px;
}

.mt-16 {
  margin-top: 16px;
}

/* 分页容器 */
.pagination-container {
  display: flex;
  justify-content: center;
  padding: 20px 0;
  background: #fafbfc;
  border-top: 1px solid #e9ecef;
}

.pagination-container .el-pagination {
  --el-pagination-font-size: 14px;
  --el-pagination-bg-color: #ffffff;
  --el-pagination-text-color: #606266;
  --el-pagination-border-radius: 6px;
}

.pagination-container .el-pagination .btn-prev,
.pagination-container .el-pagination .btn-next {
  border-radius: 6px;
  border: 1px solid #dcdfe6;
  background: #ffffff;
  transition: all 0.3s;
}

.pagination-container .el-pagination .btn-prev:hover,
.pagination-container .el-pagination .btn-next:hover {
  color: #409eff;
  border-color: #409eff;
}

/* 表格操作栏样式 */
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.header-left {
  display: flex;
  gap: 8px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.table-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

/* 卡片头部样式 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}

.header-actions {
  display: flex;
  gap: 8px;
}

/* 搜索头部样式 */
.search-header {
  margin-bottom: 16px;
  padding: 12px 16px;
  background: #f8f9fa;
  border-radius: 8px;
  display: flex;
  justify-content: flex-end;
}

/* 表格内联操作按钮样式 */
.table-actions-inline {
  display: flex;
  gap: 4px;
  justify-content: center;
  flex-wrap: nowrap;
}

.table-actions-inline .el-button {
  margin: 0;
  padding: 4px 8px;
  font-size: 12px;
}

.table-actions-inline .el-button .el-icon {
  margin-right: 2px;
}

/* 城市和区县管理头部样式 */
.city-header,
.district-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  flex-wrap: wrap;
  gap: 16px;
}

.city-filters,
.district-filters {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}

.filter-tags {
  display: flex;
  align-items: center;
  gap: 8px;
}

.city-actions,
.district-actions {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
}

.hint-text {
  color: #909399;
  font-size: 12px;
}

/* 序号样式 */
.row-number {
  color: #606266;
  font-size: 14px;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .city-header,
  .district-header {
    flex-direction: column;
    align-items: stretch;
  }

  .city-filters,
  .district-filters {
    justify-content: center;
  }

  .city-actions,
  .district-actions {
    justify-content: center;
  }
}
</style>
