<template>
  <div class="dashboard">
    <!-- 页面头部 -->
    <div class="dashboard-header">
      <h2 class="dashboard-title">仪表盘</h2>
      <el-button type="primary" @click="handleRefresh" :loading="refreshLoading" circle>
        <el-icon><Refresh /></el-icon>
      </el-button>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="16" class="stats-row">
      <el-col :xs="24" :sm="12" :md="6" v-for="(stat, index) in stats" :key="index">
        <div class="stats-card" :class="stat.type">
          <div class="stats-content">
            <div class="stats-value">{{ stat.value }}</div>
            <div class="stats-label">{{ stat.label }}</div>
          </div>
          <div class="stats-icon">
            <el-icon :size="32">
              <component :is="stat.icon" />
            </el-icon>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="charts-row">
      <!-- 订单趋势图 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="8" :xl="8">
        <div class="chart-card">
          <div class="chart-header">
            <h3>订单趋势</h3>
            <el-radio-group v-model="orderChartType" size="small" class="chart-controls">
              <el-radio-button label="week">7天</el-radio-button>
              <el-radio-button label="month">30天</el-radio-button>
            </el-radio-group>
          </div>
          <div class="chart-container line-chart">
            <v-chart
              :option="orderChartOption"
              :loading="orderChartLoading"
              class="chart-instance"
            />
          </div>
        </div>
      </el-col>

      <!-- 销售额趋势图 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="8" :xl="8">
        <div class="chart-card">
          <div class="chart-header">
            <h3>销售额趋势</h3>
          </div>
          <div class="chart-container line-chart">
            <v-chart
              :option="salesChartOption"
              :loading="salesChartLoading"
              class="chart-instance"
            />
          </div>
        </div>
      </el-col>

      <!-- 用户增长趋势图 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="8" :xl="8">
        <div class="chart-card">
          <div class="chart-header">
            <h3>用户增长</h3>
          </div>
          <div class="chart-container line-chart">
            <v-chart
              :option="userGrowthChartOption"
              :loading="userGrowthChartLoading"
              class="chart-instance"
            />
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 第二行图表 -->
    <el-row :gutter="20" class="charts-row">
      <!-- 商品分类销量 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="8" :xl="8">
        <div class="chart-card">
          <div class="chart-header">
            <h3>商品分类销量</h3>
          </div>
          <div class="chart-container pie-chart">
            <v-chart
              :option="categoryChartOption"
              :loading="categoryChartLoading"
              class="chart-instance"
            />
          </div>
        </div>
      </el-col>

      <!-- 库存状态分布 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="8" :xl="8">
        <div class="chart-card">
          <div class="chart-header">
            <h3>库存状态分布</h3>
          </div>
          <div class="chart-container pie-chart">
            <v-chart
              :option="stockChartOption"
              :loading="stockChartLoading"
              class="chart-instance"
            />
          </div>
        </div>
      </el-col>

      <!-- 订单状态分布 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="8" :xl="8">
        <div class="chart-card">
          <div class="chart-header">
            <h3>订单状态分布</h3>
          </div>
          <div class="chart-container pie-chart">
            <v-chart
              :option="orderStatusChartOption"
              :loading="orderStatusChartLoading"
              class="chart-instance"
            />
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 热销商品排行榜 -->
    <el-row :gutter="20" class="charts-row">
      <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
        <div class="chart-card">
          <div class="chart-header">
            <h3>热销商品排行榜</h3>
          </div>
          <div class="chart-container bar-chart">
            <v-chart
              :option="hotProductsChartOption"
              :loading="hotProductsChartLoading"
              class="chart-instance"
            />
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 最近订单 -->
    <div class="page-container">
      <div class="page-header">
        <h3 class="page-title">最近订单</h3>
        <el-button type="primary" @click="$router.push('/orders')">
          查看全部
        </el-button>
      </div>
      
      <el-table
        :data="recentOrders"
        :loading="ordersLoading"
        stripe
      >
        <!-- 序号列 -->
        <el-table-column label="序号" width="70" align="center">
          <template #default="{ $index }">
            <div class="row-number">{{ $index + 1 }}</div>
          </template>
        </el-table-column>

        <el-table-column prop="id" label="订单号" width="120" />
        <el-table-column prop="userName" label="用户" />
        <el-table-column prop="totalAmount" label="金额">
          <template #default="{ row }">
            ¥{{ formatMoney(row.finalAmount || row.totalAmount) }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态">
          <template #default="{ row }">
            <el-tag :type="getOrderStatusType(row.status)">
              {{ getOrderStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="创建时间">
          <template #default="{ row }">
            {{ formatDate(row.createdAt) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120">
          <template #default="{ row }">
            <el-button
              type="text"
              size="small"
              @click="$router.push(`/orders/${row.id}`)"
            >
              查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { LineChart, PieChart, BarChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
} from 'echarts/components'
import VChart from 'vue-echarts'
import { adminApi } from '@/api/admin'
import { formatDate, formatMoney, getOrderStatusText, getOrderStatusType } from '@/utils'

// 注册 ECharts 组件
use([
  CanvasRenderer,
  LineChart,
  PieChart,
  BarChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
])

// 统计数据
const stats = ref([
  { label: '总用户数', value: 0, icon: 'User', type: 'primary' },
  { label: '总订单数', value: 0, icon: 'ShoppingCart', type: 'success' },
  { label: '总商品数', value: 0, icon: 'Goods', type: 'warning' },
  { label: '总销售额', value: '¥0', icon: 'Money', type: 'danger' }
])

// 订单图表
const orderChartType = ref('week')
const orderChartLoading = ref(false)
const orderChartOption = ref({})

// 分类图表
const categoryChartLoading = ref(false)
const categoryChartOption = ref({})

// 销售额趋势图表
const salesChartLoading = ref(false)
const salesChartOption = ref({})

// 用户增长图表
const userGrowthChartLoading = ref(false)
const userGrowthChartOption = ref({})

// 库存状态图表
const stockChartLoading = ref(false)
const stockChartOption = ref({})

// 订单状态图表
const orderStatusChartLoading = ref(false)
const orderStatusChartOption = ref({})

// 热销商品图表
const hotProductsChartLoading = ref(false)
const hotProductsChartOption = ref({})

// 最近订单
const recentOrders = ref([])
const ordersLoading = ref(false)

// 刷新状态
const refreshLoading = ref(false)

// 加载统计数据
const loadStats = async () => {
  try {
    const response = await adminApi.getStats()
    const data = response.data
    
    stats.value[0].value = data.userCount || 0
    stats.value[1].value = data.orderCount || 0
    stats.value[2].value = data.flowerCount || 0
    stats.value[3].value = `¥${formatMoney(data.totalSales || 0)}`
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

// 加载订单图表数据
const loadOrderChart = async () => {
  orderChartLoading.value = true
  try {
    const response = await adminApi.getChartData(`order-${orderChartType.value}`)
    const data = response.data
    
    orderChartOption.value = {
      grid: {
        left: '8%',
        right: '4%',
        bottom: '8%',
        top: '5%',
        containLabel: true
      },
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(50, 50, 50, 0.9)',
        borderColor: '#409EFF',
        borderWidth: 1,
        textStyle: {
          color: '#fff'
        }
      },
      xAxis: {
        type: 'category',
        data: data.dates || [],
        axisLine: {
          lineStyle: {
            color: '#E4E7ED'
          }
        },
        axisLabel: {
          color: '#606266'
        }
      },
      yAxis: {
        type: 'value',
        axisLine: {
          lineStyle: {
            color: '#E4E7ED'
          }
        },
        axisLabel: {
          color: '#606266'
        },
        splitLine: {
          lineStyle: {
            color: '#F2F6FC'
          }
        }
      },
      series: [
        {
          name: '订单数量',
          type: 'line',
          data: data.counts || [],
          smooth: true,
          symbol: 'circle',
          symbolSize: 6,
          lineStyle: {
            width: 3,
            color: '#409EFF'
          },
          itemStyle: {
            color: '#409EFF',
            borderColor: '#fff',
            borderWidth: 2
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: 'rgba(64, 158, 255, 0.3)' },
                { offset: 1, color: 'rgba(64, 158, 255, 0.05)' }
              ]
            }
          }
        }
      ]
    }
  } catch (error) {
    console.error('加载订单图表数据失败:', error)
  } finally {
    orderChartLoading.value = false
  }
}

// 加载分类图表数据
const loadCategoryChart = async () => {
  categoryChartLoading.value = true
  try {
    const response = await adminApi.getChartData('category-sales')
    const data = response.data

    categoryChartOption.value = {
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)',
        backgroundColor: 'rgba(50, 50, 50, 0.9)',
        borderColor: '#67C23A',
        borderWidth: 1,
        textStyle: {
          color: '#fff'
        }
      },
      legend: {
        orient: 'horizontal',
        bottom: '2%',
        left: 'center',
        itemWidth: 10,
        itemHeight: 10,
        textStyle: {
          color: '#606266',
          fontSize: 11
        },
        formatter: function(name) {
          return name.length > 6 ? name.substring(0, 6) + '...' : name
        }
      },
      series: [
        {
          name: '销量',
          type: 'pie',
          radius: ['35%', '70%'],
          center: ['50%', '42%'],
          data: data.categories || [],
          itemStyle: {
            borderRadius: 4,
            borderColor: '#fff',
            borderWidth: 2
          },
          label: {
            show: true,
            position: 'outside',
            formatter: '{b}\n{d}%',
            fontSize: 10,
            color: '#606266'
          },
          labelLine: {
            show: true,
            length: 8,
            length2: 3
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 15,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.3)'
            },
            label: {
              show: true,
              fontSize: 12,
              fontWeight: 'bold'
            }
          }
        }
      ],
      color: ['#409EFF', '#67C23A', '#E6A23C', '#F56C6C', '#909399', '#C0C4CC', '#79BBFF']
    }
  } catch (error) {
    console.error('加载分类图表数据失败:', error)
  } finally {
    categoryChartLoading.value = false
  }
}

// 加载销售额趋势图表数据
const loadSalesChart = async () => {
  salesChartLoading.value = true
  try {
    const response = await adminApi.getChartData('sales-trend')
    const data = response.data

    salesChartOption.value = {
      grid: {
        left: '8%',
        right: '4%',
        bottom: '8%',
        top: '5%',
        containLabel: true
      },
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(50, 50, 50, 0.9)',
        borderColor: '#67C23A',
        borderWidth: 1,
        textStyle: {
          color: '#fff'
        },
        formatter: function(params) {
          return `${params[0].name}<br/>销售额: ¥${params[0].value}`
        }
      },
      xAxis: {
        type: 'category',
        data: data.dates || [],
        axisLine: {
          lineStyle: {
            color: '#E4E7ED'
          }
        },
        axisLabel: {
          color: '#606266'
        }
      },
      yAxis: {
        type: 'value',
        axisLine: {
          lineStyle: {
            color: '#E4E7ED'
          }
        },
        axisLabel: {
          color: '#606266',
          formatter: '¥{value}'
        },
        splitLine: {
          lineStyle: {
            color: '#F2F6FC'
          }
        }
      },
      series: [
        {
          name: '销售额',
          type: 'line',
          data: data.amounts || [],
          smooth: true,
          symbol: 'circle',
          symbolSize: 6,
          lineStyle: {
            width: 3,
            color: '#67C23A'
          },
          itemStyle: {
            color: '#67C23A',
            borderColor: '#fff',
            borderWidth: 2
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: 'rgba(103, 194, 58, 0.3)' },
                { offset: 1, color: 'rgba(103, 194, 58, 0.05)' }
              ]
            }
          }
        }
      ]
    }
  } catch (error) {
    console.error('加载销售额图表数据失败:', error)
  } finally {
    salesChartLoading.value = false
  }
}

// 加载用户增长图表数据
const loadUserGrowthChart = async () => {
  userGrowthChartLoading.value = true
  try {
    const response = await adminApi.getChartData('user-growth')
    const data = response.data

    userGrowthChartOption.value = {
      grid: {
        left: '8%',
        right: '4%',
        bottom: '8%',
        top: '5%',
        containLabel: true
      },
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(50, 50, 50, 0.9)',
        borderColor: '#E6A23C',
        borderWidth: 1,
        textStyle: {
          color: '#fff'
        }
      },
      xAxis: {
        type: 'category',
        data: data.dates || [],
        axisLine: {
          lineStyle: {
            color: '#E4E7ED'
          }
        },
        axisLabel: {
          color: '#606266'
        }
      },
      yAxis: {
        type: 'value',
        axisLine: {
          lineStyle: {
            color: '#E4E7ED'
          }
        },
        axisLabel: {
          color: '#606266'
        },
        splitLine: {
          lineStyle: {
            color: '#F2F6FC'
          }
        }
      },
      series: [
        {
          name: '用户总数',
          type: 'line',
          data: data.counts || [],
          smooth: true,
          symbol: 'circle',
          symbolSize: 6,
          lineStyle: {
            width: 3,
            color: '#E6A23C'
          },
          itemStyle: {
            color: '#E6A23C',
            borderColor: '#fff',
            borderWidth: 2
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: 'rgba(230, 162, 60, 0.3)' },
                { offset: 1, color: 'rgba(230, 162, 60, 0.05)' }
              ]
            }
          }
        }
      ]
    }
  } catch (error) {
    console.error('加载用户增长图表数据失败:', error)
  } finally {
    userGrowthChartLoading.value = false
  }
}

// 加载库存状态图表数据
const loadStockChart = async () => {
  stockChartLoading.value = true
  try {
    const response = await adminApi.getChartData('stock-status')
    const data = response.data

    stockChartOption.value = {
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)',
        backgroundColor: 'rgba(50, 50, 50, 0.9)',
        borderColor: '#F56C6C',
        borderWidth: 1,
        textStyle: {
          color: '#fff'
        }
      },
      legend: {
        orient: 'horizontal',
        bottom: '2%',
        left: 'center',
        itemWidth: 10,
        itemHeight: 10,
        textStyle: {
          color: '#606266',
          fontSize: 11
        }
      },
      series: [
        {
          name: '商品数量',
          type: 'pie',
          radius: ['35%', '70%'],
          center: ['50%', '42%'],
          data: data.stockStatus || [],
          itemStyle: {
            borderRadius: 4,
            borderColor: '#fff',
            borderWidth: 2
          },
          label: {
            show: true,
            position: 'outside',
            formatter: '{b}\n{d}%',
            fontSize: 10,
            color: '#606266'
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 15,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.3)'
            }
          }
        }
      ],
      color: ['#F56C6C', '#E6A23C', '#67C23A', '#409EFF']
    }
  } catch (error) {
    console.error('加载库存状态图表数据失败:', error)
  } finally {
    stockChartLoading.value = false
  }
}

// 加载最近订单
const loadRecentOrders = async () => {
  ordersLoading.value = true
  try {
    const response = await adminApi.getOrders({
      current: 1,
      size: 10,
      sortBy: 'created_at',
      sortOrder: 'desc'
    })
    recentOrders.value = response.data.records || []
  } catch (error) {
    console.error('加载最近订单失败:', error)
  } finally {
    ordersLoading.value = false
  }
}

// 加载订单状态图表数据
const loadOrderStatusChart = async () => {
  orderStatusChartLoading.value = true
  try {
    const response = await adminApi.getChartData('order-status')
    const data = response.data

    orderStatusChartOption.value = {
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)',
        backgroundColor: 'rgba(50, 50, 50, 0.9)',
        borderColor: '#909399',
        borderWidth: 1,
        textStyle: {
          color: '#fff'
        }
      },
      legend: {
        orient: 'horizontal',
        bottom: '2%',
        left: 'center',
        itemWidth: 10,
        itemHeight: 10,
        textStyle: {
          color: '#606266',
          fontSize: 11
        }
      },
      series: [
        {
          name: '订单数量',
          type: 'pie',
          radius: ['35%', '70%'],
          center: ['50%', '42%'],
          data: data.orderStatus || [],
          itemStyle: {
            borderRadius: 4,
            borderColor: '#fff',
            borderWidth: 2
          },
          label: {
            show: true,
            position: 'outside',
            formatter: '{b}\n{d}%',
            fontSize: 10,
            color: '#606266'
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 15,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.3)'
            }
          }
        }
      ],
      color: ['#E6A23C', '#67C23A', '#409EFF', '#909399', '#F56C6C']
    }
  } catch (error) {
    console.error('加载订单状态图表数据失败:', error)
  } finally {
    orderStatusChartLoading.value = false
  }
}

// 加载热销商品图表数据
const loadHotProductsChart = async () => {
  hotProductsChartLoading.value = true
  try {
    const response = await adminApi.getChartData('hot-products')
    const data = response.data

    const products = data.hotProducts || []
    const names = products.map(item => item.name)
    const sales = products.map(item => item.sales)

    hotProductsChartOption.value = {
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '5%',
        containLabel: true
      },
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(50, 50, 50, 0.9)',
        borderColor: '#409EFF',
        borderWidth: 1,
        textStyle: {
          color: '#fff'
        },
        formatter: function(params) {
          const product = products[params[0].dataIndex]
          return `${params[0].name}<br/>销量: ${params[0].value}<br/>价格: ¥${product.price}`
        }
      },
      xAxis: {
        type: 'category',
        data: names,
        axisLine: {
          lineStyle: {
            color: '#E4E7ED'
          }
        },
        axisLabel: {
          color: '#606266',
          interval: 0,
          rotate: 45,
          formatter: function(value) {
            return value.length > 8 ? value.substring(0, 8) + '...' : value
          }
        }
      },
      yAxis: {
        type: 'value',
        axisLine: {
          lineStyle: {
            color: '#E4E7ED'
          }
        },
        axisLabel: {
          color: '#606266'
        },
        splitLine: {
          lineStyle: {
            color: '#F2F6FC'
          }
        }
      },
      series: [
        {
          name: '销量',
          type: 'bar',
          data: sales,
          itemStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: '#409EFF' },
                { offset: 1, color: '#79BBFF' }
              ]
            },
            borderRadius: [4, 4, 0, 0]
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.3)'
            }
          }
        }
      ]
    }
  } catch (error) {
    console.error('加载热销商品图表数据失败:', error)
  } finally {
    hotProductsChartLoading.value = false
  }
}

// 刷新所有数据
const handleRefresh = async () => {
  refreshLoading.value = true
  try {
    // 使用分批加载策略刷新数据
    await loadDataInBatches()
    ElMessage({
      message: '仪表盘数据已刷新',
      type: 'success',
      duration: 1500,
      showClose: false
    })
  } catch (error) {
    console.error('刷新数据失败:', error)
    ElMessage.error('刷新数据失败')
  } finally {
    refreshLoading.value = false
  }
}

// 监听订单图表类型变化
watch(orderChartType, () => {
  loadOrderChart()
})

// 分批加载数据，避免同时发起过多请求
const loadDataInBatches = async () => {
  try {
    // 第一批：核心数据（优先级最高）
    await Promise.all([
      loadStats(),
      loadRecentOrders()
    ])

    // 短暂延迟，让用户先看到核心数据
    await new Promise(resolve => setTimeout(resolve, 200))

    // 第二批：主要图表
    await Promise.all([
      loadOrderChart(),
      loadSalesChart()
    ])

    // 再次延迟
    await new Promise(resolve => setTimeout(resolve, 300))

    // 第三批：次要图表
    await Promise.all([
      loadUserGrowthChart(),
      loadCategoryChart()
    ])

    // 最后延迟
    await new Promise(resolve => setTimeout(resolve, 300))

    // 第四批：其他图表
    await Promise.all([
      loadStockChart(),
      loadOrderStatusChart(),
      loadHotProductsChart()
    ])
  } catch (error) {
    console.error('加载仪表盘数据失败:', error)
  }
}

onMounted(() => {
  loadDataInBatches()
})
</script>

<style scoped>
.dashboard {
  padding: 20px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding: 24px 32px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.dashboard-title {
  font-size: 28px;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0;
}

.stats-row {
  margin-bottom: 32px;
  margin-left: 0;
}

.stats-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32px 24px;
  border-radius: 20px;
  color: white;
  margin-bottom: 16px;
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stats-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.1);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.stats-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
}

.stats-card:hover::before {
  opacity: 1;
}

.stats-card.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stats-card.success {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stats-card.warning {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stats-card.danger {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stats-content {
  flex: 1;
  z-index: 1;
  position: relative;
}

.stats-value {
  font-size: 36px;
  font-weight: 800;
  margin-bottom: 8px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stats-label {
  font-size: 16px;
  opacity: 0.95;
  font-weight: 500;
}

.stats-icon {
  opacity: 0.9;
  z-index: 1;
  position: relative;
}

.charts-row {
  margin-bottom: 24px;
}

.chart-card {
  margin-bottom: 20px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  padding: 20px;
  position: relative;
  overflow: hidden;
}

.chart-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px 16px 0 0;
}

.chart-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 2px solid rgba(102, 126, 234, 0.1);
  position: relative;
}

.chart-header::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 60px;
  height: 2px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 1px;
}

.chart-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
}

.chart-controls {
  flex-shrink: 0;
}

.chart-container {
  height: 380px;
  position: relative;
}

.chart-instance {
  width: 100%;
  height: 100%;
}

.line-chart {
  padding: 5px;
}

.pie-chart {
  padding: 0px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chart-card {
    padding: 16px;
    border-radius: 12px;
  }

  .chart-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
    margin-bottom: 12px;
  }

  .chart-header h3 {
    font-size: 16px;
  }

  .chart-container {
    height: 300px;
  }

  .chart-controls {
    align-self: stretch;
  }

  .chart-controls .el-radio-group {
    width: 100%;
    display: flex;
  }

  .chart-controls .el-radio-button {
    flex: 1;
  }
}

@media (max-width: 480px) {
  .chart-card {
    padding: 12px;
  }

  .chart-container {
    height: 260px;
  }

  .chart-header h3 {
    font-size: 14px;
  }
}

/* 图表加载状态优化 */
.chart-instance .el-loading-mask {
  border-radius: 8px;
}
</style>