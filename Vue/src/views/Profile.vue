<template>
  <div class="profile-container">
    <div class="profile-header">
      <h2 class="page-title">个人设置</h2>
      <p class="page-desc">管理您的个人信息和账户设置</p>
    </div>

    <el-row :gutter="24">
      <!-- 左侧个人信息 -->
      <el-col :xs="24" :sm="24" :md="8" :lg="8">
        <el-card class="profile-card">
          <div class="profile-avatar-section">
            <div class="avatar-container">
              <el-avatar
                :size="120"
                :src="avatarUrl"
                class="profile-avatar"
                @error="handleAvatarError"
              >
                <el-icon size="60"><User /></el-icon>
              </el-avatar>
              <el-button class="avatar-upload-btn" circle @click="handleAvatarUpload">
                <el-icon><Camera /></el-icon>
              </el-button>
            </div>
            <div class="profile-info">
              <h3 class="profile-name">{{ userInfo.realName || userInfo.username }}</h3>
              <p class="profile-role">{{ getRoleText(userInfo.role) }}</p>
              <el-tag :type="userInfo.status === 1 ? 'success' : 'danger'" size="small">
                {{ userInfo.status === 1 ? '正常' : '禁用' }}
              </el-tag>
            </div>
          </div>
          
          <div class="profile-stats">
            <div class="stat-item">
              <div class="stat-value">{{ loginStats.totalLogins }}</div>
              <div class="stat-label">登录次数</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ formatDate(userInfo.lastLoginTime) }}</div>
              <div class="stat-label">最后登录</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ formatDate(userInfo.createdAt) }}</div>
              <div class="stat-label">注册时间</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 右侧设置表单 -->
      <el-col :xs="24" :sm="24" :md="16" :lg="16">
        <el-card class="settings-card">
          <el-tabs v-model="activeTab" class="profile-tabs">
            <!-- 基本信息 -->
            <el-tab-pane label="基本信息" name="basic">
              <el-form
                ref="basicFormRef"
                :model="basicForm"
                :rules="basicRules"
                label-width="100px"
                class="profile-form"
              >
                <el-form-item label="用户名" prop="username">
                  <el-input v-model="basicForm.username" disabled />
                </el-form-item>
                
                <el-form-item label="真实姓名" prop="realName">
                  <el-input v-model="basicForm.realName" placeholder="请输入真实姓名" />
                </el-form-item>
                
                <el-form-item label="邮箱" prop="email">
                  <el-input v-model="basicForm.email" placeholder="请输入邮箱地址" />
                </el-form-item>
                
                <el-form-item label="手机号" prop="phone">
                  <el-input v-model="basicForm.phone" placeholder="请输入手机号" />
                </el-form-item>
                
                <el-form-item>
                  <el-button type="primary" @click="updateBasicInfo" :loading="basicLoading">
                    保存基本信息
                  </el-button>
                </el-form-item>
              </el-form>
            </el-tab-pane>

            <!-- 修改密码 -->
            <el-tab-pane label="修改密码" name="password">
              <el-form
                ref="passwordFormRef"
                :model="passwordForm"
                :rules="passwordRules"
                label-width="100px"
                class="profile-form"
              >
                <el-form-item label="当前密码" prop="currentPassword">
                  <el-input
                    v-model="passwordForm.currentPassword"
                    type="password"
                    placeholder="请输入当前密码"
                    show-password
                  />
                </el-form-item>
                
                <el-form-item label="新密码" prop="newPassword">
                  <el-input
                    v-model="passwordForm.newPassword"
                    type="password"
                    placeholder="请输入新密码"
                    show-password
                  />
                </el-form-item>
                
                <el-form-item label="确认密码" prop="confirmPassword">
                  <el-input
                    v-model="passwordForm.confirmPassword"
                    type="password"
                    placeholder="请再次输入新密码"
                    show-password
                  />
                </el-form-item>
                
                <el-form-item>
                  <el-button type="primary" @click="updatePassword" :loading="passwordLoading">
                    修改密码
                  </el-button>
                </el-form-item>
              </el-form>
            </el-tab-pane>

            <!-- 安全设置 -->
            <el-tab-pane label="安全设置" name="security">
              <div class="security-settings">
                <div class="security-item">
                  <div class="security-info">
                    <h4>登录保护</h4>
                    <p>开启后需要验证码才能登录</p>
                  </div>
                  <el-switch v-model="securitySettings.loginProtection" />
                </div>
                
                <div class="security-item">
                  <div class="security-info">
                    <h4>操作日志</h4>
                    <p>记录重要操作的日志信息</p>
                  </div>
                  <el-switch v-model="securitySettings.operationLog" />
                </div>
                
                <div class="security-item">
                  <div class="security-info">
                    <h4>自动登出</h4>
                    <p>长时间无操作自动退出登录</p>
                  </div>
                  <el-switch v-model="securitySettings.autoLogout" />
                </div>
                
                <el-button type="primary" @click="updateSecuritySettings" :loading="securityLoading">
                  保存安全设置
                </el-button>
              </div>
            </el-tab-pane>
          </el-tabs>
        </el-card>
      </el-col>
    </el-row>

    <!-- 头像上传对话框 -->
    <el-dialog v-model="avatarDialogVisible" title="上传头像" width="400px">
      <el-upload
        class="avatar-uploader"
        :auto-upload="false"
        :show-file-list="false"
        :on-change="handleFileChange"
        accept="image/jpeg,image/png,image/jpg"
        :limit="1"
      >
        <img v-if="previewAvatar" :src="previewAvatar" class="avatar-preview" />
        <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
      </el-upload>
      <template #footer>
        <el-button @click="avatarDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmAvatarUpload">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useAuthStore } from '@/stores/auth'
import { adminApi } from '@/api/admin'
import { formatDate } from '@/utils'
import {
  User,
  Camera,
  Plus
} from '@element-plus/icons-vue'

const authStore = useAuthStore()

// 响应式数据
const activeTab = ref('basic')
const basicLoading = ref(false)
const passwordLoading = ref(false)
const securityLoading = ref(false)
const avatarDialogVisible = ref(false)
const previewAvatar = ref('')
const selectedFile = ref(null)
const avatarTimestamp = ref(Date.now())

// 计算属性 - 头像URL（带时间戳防止缓存）
const avatarUrl = computed(() => {
  if (!userInfo.value.avatar) return ''

  // 如果URL已经包含时间戳，直接返回
  if (userInfo.value.avatar.includes('?t=')) {
    return userInfo.value.avatar
  }

  // 添加时间戳防止缓存
  return userInfo.value.avatar + '?t=' + avatarTimestamp.value
})

// 用户信息
const userInfo = ref({
  id: 1,
  username: 'admin',
  realName: '系统管理员',
  email: '<EMAIL>',
  phone: '13800138000',
  avatar: '',
  role: 'super',
  status: 1,
  lastLoginTime: new Date(),
  createdAt: new Date()
})

// 登录统计
const loginStats = ref({
  totalLogins: 156,
  lastLoginIp: '127.0.0.1'
})

// 基本信息表单
const basicForm = reactive({
  username: '',
  realName: '',
  email: '',
  phone: ''
})

// 密码表单
const passwordForm = reactive({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 安全设置
const securitySettings = reactive({
  loginProtection: false,
  operationLog: true,
  autoLogout: true
})

// 表单引用
const basicFormRef = ref()
const passwordFormRef = ref()

// 上传配置
const uploadUrl = ref('http://localhost:8080/api/admin/upload/avatar')
const uploadHeaders = computed(() => ({
  'Authorization': `Bearer ${authStore.token}`
}))

// 表单验证规则
const basicRules = {
  realName: [
    { required: true, message: '请输入真实姓名', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号格式', trigger: 'blur' }
  ]
}

const passwordRules = {
  currentPassword: [
    { required: true, message: '请输入当前密码', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请再次输入新密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== passwordForm.newPassword) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 方法
const getRoleText = (role) => {
  const roleMap = {
    'super': '超级管理员',
    'admin': '管理员',
    'editor': '编辑员'
  }
  return roleMap[role] || '未知角色'
}

const loadUserInfo = async () => {
  try {
    const response = await adminApi.getProfile()
    if (response.code === 200) {
      userInfo.value = response.data

      // 更新表单数据
      Object.assign(basicForm, {
        username: response.data.username,
        realName: response.data.realName || '',
        email: response.data.email || '',
        phone: response.data.phone || ''
      })
    } else {
      ElMessage.error(response.message || '加载用户信息失败')
    }
  } catch (error) {
    console.error('加载用户信息失败:', error)
    ElMessage.error('加载用户信息失败')
  }
}

const updateBasicInfo = async () => {
  if (!basicFormRef.value) return

  try {
    await basicFormRef.value.validate()
    basicLoading.value = true

    const response = await adminApi.updateProfile({
      realName: basicForm.realName,
      email: basicForm.email,
      phone: basicForm.phone
    })

    if (response.code === 200) {
      Object.assign(userInfo.value, basicForm)
      ElMessage.success('基本信息更新成功')
    } else {
      ElMessage.error(response.message || '更新基本信息失败')
    }
  } catch (error) {
    console.error('更新基本信息失败:', error)
    if (error !== 'validation failed') {
      ElMessage.error('更新基本信息失败')
    }
  } finally {
    basicLoading.value = false
  }
}

const updatePassword = async () => {
  if (!passwordFormRef.value) return

  try {
    await passwordFormRef.value.validate()
    passwordLoading.value = true

    const response = await adminApi.updatePassword({
      currentPassword: passwordForm.currentPassword,
      newPassword: passwordForm.newPassword
    })

    if (response.code === 200) {
      // 重置表单
      Object.assign(passwordForm, {
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      })

      ElMessage.success('密码修改成功')
    } else {
      ElMessage.error(response.message || '密码修改失败')
    }
  } catch (error) {
    console.error('修改密码失败:', error)
    if (error !== 'validation failed') {
      ElMessage.error('修改密码失败')
    }
  } finally {
    passwordLoading.value = false
  }
}

const updateSecuritySettings = async () => {
  try {
    securityLoading.value = true

    // 这里应该调用API更新安全设置
    // await adminApi.updateSecuritySettings(securitySettings)

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    ElMessage.success('安全设置更新成功')
  } catch (error) {
    console.error('更新安全设置失败:', error)
    ElMessage.error('更新安全设置失败')
  } finally {
    securityLoading.value = false
  }
}

const handleAvatarUpload = () => {
  avatarDialogVisible.value = true
  previewAvatar.value = userInfo.value.avatar
  selectedFile.value = null
}

const handleFileChange = (file) => {
  const rawFile = file.raw

  // 验证文件类型
  const isJPG = rawFile.type === 'image/jpeg' || rawFile.type === 'image/png'
  const isLt2M = rawFile.size / 1024 / 1024 < 2

  if (!isJPG) {
    ElMessage.error('头像只能是 JPG/PNG 格式!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('头像大小不能超过 2MB!')
    return false
  }

  selectedFile.value = rawFile

  // 预览图片
  const reader = new FileReader()
  reader.onload = (e) => {
    previewAvatar.value = e.target.result
  }
  reader.readAsDataURL(rawFile)
}

const confirmAvatarUpload = async () => {
  if (!selectedFile.value) {
    ElMessage.error('请先选择头像文件')
    return
  }

  try {
    const response = await adminApi.uploadAvatar(selectedFile.value)
    console.log('头像上传响应:', response)

    if (response.code === 200) {
      const newAvatarUrl = response.data.url
      console.log('新头像URL:', newAvatarUrl)

      // 更新用户信息中的头像（不带时间戳，让计算属性处理）
      userInfo.value.avatar = newAvatarUrl

      // 更新时间戳以刷新头像显示
      avatarTimestamp.value = Date.now()

      // 更新认证store中的用户信息，这会触发顶部导航栏头像的更新
      authStore.updateUser({ avatar: newAvatarUrl })

      console.log('已更新AuthStore中的头像:', newAvatarUrl)

      // 关闭对话框
      avatarDialogVisible.value = false
      selectedFile.value = null
      previewAvatar.value = ''

      ElMessage.success('头像更新成功')

      // 延迟一下再重新加载用户信息，确保后端数据已更新
      setTimeout(async () => {
        await loadUserInfo()
        avatarTimestamp.value = Date.now() // 再次刷新时间戳
      }, 500)
    } else {
      ElMessage.error(response.message || '头像上传失败')
    }
  } catch (error) {
    console.error('头像上传失败:', error)
    ElMessage.error('头像上传失败')
  }
}

const handleAvatarError = (e) => {
  console.error('头像加载失败:', e)
  console.log('头像URL:', userInfo.value.avatar)
  console.log('完整头像URL:', avatarUrl.value)

  // 尝试刷新时间戳
  avatarTimestamp.value = Date.now()
}

// 初始化
onMounted(() => {
  loadUserInfo()
})
</script>

<style scoped>
.profile-container {
  padding: 24px;
  background: #f5f7fa;
  min-height: 100vh;
}

.profile-header {
  margin-bottom: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 8px 0;
}

.page-desc {
  color: #64748b;
  margin: 0;
}

.profile-card,
.settings-card {
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  border: none;
}

.profile-avatar-section {
  text-align: center;
  padding: 20px 0;
}

.avatar-container {
  position: relative;
  display: inline-block;
  margin-bottom: 16px;
}

.profile-avatar {
  border: 4px solid #fff;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.avatar-upload-btn {
  position: absolute;
  bottom: 0;
  right: 0;
  background: #409EFF;
  border-color: #409EFF;
  color: white;
}

.profile-info {
  margin-bottom: 24px;
}

.profile-name {
  font-size: 20px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 8px 0;
}

.profile-role {
  color: #64748b;
  margin: 0 0 8px 0;
}

.profile-stats {
  display: flex;
  justify-content: space-around;
  padding: 20px 0;
  border-top: 1px solid #f0f0f0;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #64748b;
}

.profile-form {
  max-width: 500px;
}

.security-settings {
  max-width: 500px;
}

.security-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
}

.security-item:last-child {
  border-bottom: none;
  margin-bottom: 20px;
}

.security-info h4 {
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: 500;
  color: #2c3e50;
}

.security-info p {
  margin: 0;
  font-size: 12px;
  color: #64748b;
}

.avatar-uploader {
  text-align: center;
}

.avatar-preview {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  object-fit: cover;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 120px;
  height: 120px;
  line-height: 120px;
  text-align: center;
  border: 1px dashed #d9d9d9;
  border-radius: 50%;
}
</style>
