<template>
  <div class="price-categories-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1 class="page-title">价格分类管理</h1>
      <el-button type="primary" @click="showCreateDialog">
        <el-icon><Plus /></el-icon>
        新增价格分类
      </el-button>
    </div>

    <!-- 搜索栏 -->
    <div class="search-bar">
      <el-form :model="searchForm" inline>
        <el-form-item label="关键词">
          <el-input
            v-model="searchForm.keyword"
            placeholder="请输入分类名称或描述"
            clearable
            style="width: 200px"
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable style="width: 120px">
            <el-option label="启用" :value="1" />
            <el-option label="禁用" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetSearch">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
          <el-button @click="handleRefresh">
            <el-icon><RefreshRight /></el-icon>
            刷新
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 批量操作栏 -->
    <div class="batch-operations" v-if="selectedPriceCategories.length > 0">
      <div class="selection-info">
        <span class="selected-count">已选择 {{ selectedPriceCategories.length }} 项</span>
        <span class="selection-stats">
          {{ selectionStats.enabled }} 个启用，{{ selectionStats.disabled }} 个禁用
        </span>
      </div>
      <div class="batch-buttons">
        <el-button type="success" size="small" @click="batchUpdateStatus(1)">
          批量启用
        </el-button>
        <el-button type="warning" size="small" @click="batchUpdateStatus(0)">
          批量禁用
        </el-button>
        <el-button type="danger" size="small" @click="batchDelete">
          批量删除
        </el-button>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="table-container">
      <el-table
        :data="priceCategories"
        v-loading="loading"
        stripe
        border
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        
        <el-table-column prop="id" label="ID" width="80" align="center" />
        
        <el-table-column prop="name" label="分类名称" width="150" align="center">
          <template #default="{ row }">
            <div class="category-name">
              <div class="name-text">{{ row.name }}</div>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="description" label="描述" min-width="200" align="center">
          <template #default="{ row }">
            <div class="description-text">{{ row.description || '-' }}</div>
          </template>
        </el-table-column>

        <el-table-column label="价格范围" width="180" align="center">
          <template #default="{ row }">
            <div class="price-range">
              <span class="price-value">¥{{ row.minPrice }}</span>
              <span class="separator"> - </span>
              <span class="price-value">¥{{ row.maxPrice === 99999 ? '∞' : row.maxPrice }}</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="sortOrder" label="排序" width="100" align="center" />

        <el-table-column prop="status" label="状态" width="80" align="center">
          <template #default="{ row }">
            <el-switch
              v-model="row.status"
              :active-value="1"
              :inactive-value="0"
              @change="togglePriceCategoryStatus(row)"
            />
          </template>
        </el-table-column>

        <el-table-column prop="createdAt" label="创建时间" width="180" align="center">
          <template #default="{ row }">
            <div class="time-text">{{ formatDateTime(row.createdAt) }}</div>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="200" align="center" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="showEditDialog(row)">
              <el-icon><Edit /></el-icon>
              编辑
            </el-button>
            <el-button type="danger" size="small" @click="deletePriceCategory(row)">
              <el-icon><Delete /></el-icon>
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 表格信息 -->
      <div class="table-info">
        <span>共 {{ pagination.total }} 条记录</span>
      </div>
    </div>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="pagination.current"
        v-model:page-size="pagination.size"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 创建/编辑弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑价格分类' : '新增价格分类'"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="formRules"
        label-width="100px"
      >
        <el-form-item label="分类名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入分类名称" />
        </el-form-item>

        <el-form-item label="描述" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            :rows="3"
            placeholder="请输入分类描述"
          />
        </el-form-item>

        <el-form-item label="最低价格" prop="minPrice">
          <el-input-number
            v-model="form.minPrice"
            :min="0"
            :precision="2"
            placeholder="请输入最低价格"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="最高价格" prop="maxPrice">
          <el-input-number
            v-model="form.maxPrice"
            :min="0"
            :precision="2"
            placeholder="请输入最高价格"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="排序顺序" prop="sortOrder">
          <el-input-number
            v-model="form.sortOrder"
            :min="0"
            placeholder="请输入排序顺序"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitting">
            {{ isEdit ? '更新' : '创建' }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search, Refresh, RefreshRight, Edit, Delete } from '@element-plus/icons-vue'
import { adminApi } from '@/api/admin'

// 页面数据
const loading = ref(false)
const priceCategories = ref([])
const selectedPriceCategories = ref([])

// 搜索表单
const searchForm = reactive({
  keyword: '',
  status: null
})

// 分页数据
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

// 弹窗相关
const dialogVisible = ref(false)
const isEdit = ref(false)
const submitting = ref(false)
const formRef = ref()

// 表单数据
const form = reactive({
  id: null,
  name: '',
  description: '',
  minPrice: 0,
  maxPrice: 0,
  sortOrder: 0,
  status: 1
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入分类名称', trigger: 'blur' },
    { min: 1, max: 100, message: '分类名称长度在 1 到 100 个字符', trigger: 'blur' }
  ],
  minPrice: [
    { required: true, message: '请输入最低价格', trigger: 'blur' },
    { type: 'number', min: 0, message: '最低价格不能小于0', trigger: 'blur' }
  ],
  maxPrice: [
    { required: true, message: '请输入最高价格', trigger: 'blur' },
    { type: 'number', min: 0, message: '最高价格不能小于0', trigger: 'blur' }
  ],
  sortOrder: [
    { type: 'number', min: 0, message: '排序顺序不能小于0', trigger: 'blur' }
  ]
}

// 计算选中数据的统计信息
const selectionStats = computed(() => {
  const enabled = selectedPriceCategories.value.filter(item => item.status === 1).length
  const disabled = selectedPriceCategories.value.filter(item => item.status === 0).length
  return {
    enabled,
    disabled
  }
})

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 加载价格分类列表
const loadPriceCategories = async () => {
  loading.value = true
  try {
    const params = {
      current: pagination.current,
      size: pagination.size,
      keyword: searchForm.keyword,
      status: searchForm.status
    }

    const response = await adminApi.getPriceCategories(params)
    if (response && response.data) {
      priceCategories.value = response.data.records || []
      pagination.total = response.data.total || 0
    } else {
      priceCategories.value = []
      pagination.total = 0
    }
  } catch (error) {
    console.error('加载价格分类列表失败:', error)
    ElMessage.error('加载价格分类列表失败')
    priceCategories.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.current = 1
  loadPriceCategories()
}

// 重置搜索
const resetSearch = () => {
  searchForm.keyword = ''
  searchForm.status = null
  pagination.current = 1
  loadPriceCategories()
  ElMessage.success('搜索条件已重置')
}

// 刷新数据
const handleRefresh = () => {
  loadPriceCategories()
  ElMessage({
    message: '价格分类数据已刷新',
    type: 'success',
    duration: 1500,
    showClose: false
  })
}

// 分页处理
const handleSizeChange = (size) => {
  pagination.size = size
  pagination.current = 1
  loadPriceCategories()
}

const handleCurrentChange = (current) => {
  pagination.current = current
  loadPriceCategories()
}

// 多选变化处理
const handleSelectionChange = (selection) => {
  selectedPriceCategories.value = selection
}

// 切换价格分类状态
const togglePriceCategoryStatus = async (priceCategory) => {
  try {
    const action = priceCategory.status === 1 ? '启用' : '禁用'
    await adminApi.updatePriceCategoryStatus(priceCategory.id, priceCategory.status)
    ElMessage.success(`${action}成功`)
    loadPriceCategories()
  } catch (error) {
    console.error('切换价格分类状态失败:', error)
    ElMessage.error('操作失败')
    // 恢复原状态
    priceCategory.status = priceCategory.status === 1 ? 0 : 1
  }
}

// 删除价格分类
const deletePriceCategory = async (priceCategory) => {
  try {
    await ElMessageBox.confirm(`确定要删除价格分类"${priceCategory.name}"吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await adminApi.deletePriceCategory(priceCategory.id)
    ElMessage.success('删除成功')
    loadPriceCategories()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除价格分类失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 批量更新状态
const batchUpdateStatus = async (status) => {
  if (selectedPriceCategories.value.length === 0) {
    ElMessage.warning('请先选择要操作的价格分类')
    return
  }

  try {
    const action = status === 1 ? '启用' : '禁用'
    await ElMessageBox.confirm(
      `确定要${action}选中的 ${selectedPriceCategories.value.length} 个价格分类吗？`,
      '批量操作确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const promises = selectedPriceCategories.value.map(priceCategory =>
      adminApi.updatePriceCategoryStatus(priceCategory.id, status)
    )

    await Promise.all(promises)
    ElMessage.success(`批量${action}成功`)
    selectedPriceCategories.value = []
    loadPriceCategories()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量操作失败:', error)
      ElMessage.error('批量操作失败')
    }
  }
}

// 批量删除
const batchDelete = async () => {
  if (selectedPriceCategories.value.length === 0) {
    ElMessage.warning('请先选择要删除的价格分类')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedPriceCategories.value.length} 个价格分类吗？此操作不可恢复！`,
      '批量删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const promises = selectedPriceCategories.value.map(priceCategory =>
      adminApi.deletePriceCategory(priceCategory.id)
    )

    await Promise.all(promises)
    ElMessage.success('批量删除成功')
    selectedPriceCategories.value = []
    loadPriceCategories()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败:', error)
      ElMessage.error('批量删除失败')
    }
  }
}

// 显示创建弹窗
const showCreateDialog = () => {
  isEdit.value = false
  resetForm()
  dialogVisible.value = true
}

// 显示编辑弹窗
const showEditDialog = (priceCategory) => {
  isEdit.value = true
  Object.assign(form, {
    id: priceCategory.id,
    name: priceCategory.name,
    description: priceCategory.description,
    minPrice: priceCategory.minPrice,
    maxPrice: priceCategory.maxPrice,
    sortOrder: priceCategory.sortOrder,
    status: priceCategory.status
  })
  dialogVisible.value = true
}

// 重置表单
const resetForm = () => {
  Object.assign(form, {
    id: null,
    name: '',
    description: '',
    minPrice: 0,
    maxPrice: 0,
    sortOrder: 0,
    status: 1
  })
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    
    // 验证价格范围
    if (form.maxPrice <= form.minPrice) {
      ElMessage.error('最高价格必须大于最低价格')
      return
    }

    submitting.value = true

    if (isEdit.value) {
      await adminApi.updatePriceCategory(form.id, form)
      ElMessage.success('价格分类更新成功！')
    } else {
      await adminApi.createPriceCategory(form)
      ElMessage.success('价格分类创建成功！')
    }

    dialogVisible.value = false
    await loadPriceCategories()
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('提交失败: ' + error.message)
  } finally {
    submitting.value = false
  }
}

// 初始化
onMounted(async () => {
  console.log('价格分类管理页面已挂载，开始加载数据...')
  try {
    await loadPriceCategories()
    console.log('价格分类数据加载完成')
  } catch (error) {
    console.error('价格分类数据加载失败:', error)
    ElMessage.error('价格分类数据加载失败: ' + error.message)
  }
})
</script>

<style scoped>
.price-categories-page {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  margin: 0;
  color: #1f2937;
}

.search-bar {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.batch-operations {
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 8px;
  padding: 16px 20px;
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.selection-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.selected-count {
  font-size: 16px;
  font-weight: 600;
  color: #0369a1;
}

.selection-stats {
  font-size: 14px;
  color: #64748b;
}

.batch-buttons {
  display: flex;
  gap: 12px;
}

.table-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.category-name {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.name-text {
  font-weight: 500;
  color: #2c3e50;
}

.description-text {
  font-size: 13px;
  color: #8892b0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 180px;
}

.price-range {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.price-value {
  font-weight: 600;
  color: #e74c3c;
}

.separator {
  color: #909399;
}

.time-text {
  color: #8892b0;
  font-size: 13px;
}



.table-info {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 16px 20px;
  background: #f8f9fa;
  border-top: 1px solid #ebeef5;
  color: #909399;
  font-size: 14px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
