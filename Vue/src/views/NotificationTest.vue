<template>
  <div class="page-container">
    <div class="page-header">
      <h2 class="page-title">通知功能测试</h2>
    </div>

    <el-card class="test-card">
      <template #header>
        <div class="card-header">
          <span>WebSocket连接状态</span>
          <el-tag :type="notificationState.isConnected ? 'success' : 'danger'">
            {{ notificationState.isConnected ? '已连接' : '未连接' }}
          </el-tag>
        </div>
      </template>

      <div class="connection-info">
        <p><strong>在线连接数:</strong> {{ onlineCount }}</p>
        <p><strong>未读通知数:</strong> {{ notificationState.unreadCount }}</p>
        <p><strong>总通知数:</strong> {{ notificationState.notifications.length }}</p>
      </div>
    </el-card>

    <el-card class="test-card">
      <template #header>
        <span>测试通知功能</span>
      </template>

      <div class="test-buttons">
        <el-button type="primary" @click="testNewOrderNotification">
          <el-icon><ShoppingCart /></el-icon>
          测试新订单通知
        </el-button>
        
        <el-button type="warning" @click="testOrderStatusNotification">
          <el-icon><Bell /></el-icon>
          测试订单状态变更
        </el-button>
        
        <el-button type="danger" @click="testLowStockNotification">
          <el-icon><Warning /></el-icon>
          测试库存预警
        </el-button>
        
        <el-button type="info" @click="testBrowserNotification">
          <el-icon><Message /></el-icon>
          测试浏览器通知
        </el-button>
        
        <el-button type="success" @click="testSound">
          <el-icon><VideoPlay /></el-icon>
          测试提示音
        </el-button>
      </div>
    </el-card>

    <el-card class="test-card">
      <template #header>
        <span>通知权限管理</span>
      </template>

      <div class="permission-info">
        <p><strong>浏览器通知权限:</strong> 
          <el-tag :type="getPermissionType(notificationPermission)">
            {{ getPermissionText(notificationPermission) }}
          </el-tag>
        </p>
        
        <el-button 
          v-if="notificationPermission === 'default'" 
          type="primary" 
          @click="requestPermission"
        >
          请求通知权限
        </el-button>
      </div>
    </el-card>

    <el-card class="test-card">
      <template #header>
        <div class="card-header">
          <span>最近通知</span>
          <el-button text size="small" @click="clearAllNotifications">清空所有</el-button>
        </div>
      </template>

      <div class="notifications-list">
        <div v-if="notificationState.notifications.length === 0" class="empty-notifications">
          <el-empty description="暂无通知" :image-size="80" />
        </div>
        <div v-else>
          <div
            v-for="notification in notificationState.notifications.slice(0, 10)"
            :key="notification.id"
            class="notification-item"
            :class="{ unread: !notification.read }"
          >
            <div class="notification-icon">
              <el-icon :color="notification.iconColor">
                <component :is="notification.icon" />
              </el-icon>
            </div>
            <div class="notification-content">
              <div class="notification-title">{{ notification.title }}</div>
              <div class="notification-desc">{{ notification.description }}</div>
              <div class="notification-time">{{ formatTime(notification.time) }}</div>
            </div>
            <div class="notification-actions">
              <el-button 
                v-if="!notification.read" 
                text 
                size="small" 
                @click="markAsRead(notification.id)"
              >
                标记已读
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElNotification } from 'element-plus'
import { 
  ShoppingCart, 
  Bell, 
  Warning, 
  Message, 
  VideoPlay 
} from '@element-plus/icons-vue'
import { 
  notificationState, 
  sendTestNotification,
  markNotificationAsRead,
  clearAllNotifications
} from '@/services/notificationService'

// 响应式数据
const onlineCount = ref(0)
const notificationPermission = ref(Notification.permission)

// 计算属性
const formatTime = (time) => {
  return new Date(time).toLocaleString('zh-CN')
}

const getPermissionType = (permission) => {
  switch (permission) {
    case 'granted': return 'success'
    case 'denied': return 'danger'
    default: return 'warning'
  }
}

const getPermissionText = (permission) => {
  switch (permission) {
    case 'granted': return '已授权'
    case 'denied': return '已拒绝'
    default: return '未设置'
  }
}

// 方法
const testNewOrderNotification = async () => {
  try {
    const response = await fetch('/api/notification/test/new-order', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' }
    })
    
    if (response.ok) {
      ElMessage.success('新订单通知测试发送成功')
    } else {
      throw new Error('API调用失败')
    }
  } catch (error) {
    console.error('测试新订单通知失败:', error)
    // 备用方案
    await sendTestNotification()
    ElMessage.success('新订单通知测试发送成功（本地模拟）')
  }
}

const testOrderStatusNotification = async () => {
  try {
    const response = await fetch('/api/notification/test/order-status', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' }
    })
    
    if (response.ok) {
      ElMessage.success('订单状态变更通知测试发送成功')
    } else {
      throw new Error('API调用失败')
    }
  } catch (error) {
    console.error('测试订单状态通知失败:', error)
    ElMessage.error('订单状态变更通知测试失败')
  }
}

const testLowStockNotification = async () => {
  try {
    const response = await fetch('/api/notification/test/low-stock', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' }
    })
    
    if (response.ok) {
      ElMessage.success('库存预警通知测试发送成功')
    } else {
      throw new Error('API调用失败')
    }
  } catch (error) {
    console.error('测试库存预警通知失败:', error)
    ElMessage.error('库存预警通知测试失败')
  }
}

const testBrowserNotification = () => {
  if ('Notification' in window && Notification.permission === 'granted') {
    new Notification('测试通知', {
      body: '这是一个测试的浏览器通知',
      icon: '/favicon.ico'
    })
    ElMessage.success('浏览器通知测试发送成功')
  } else {
    ElMessage.warning('浏览器通知权限未授权')
  }
}

const testSound = () => {
  // 这里调用通知服务中的播放提示音功能
  try {
    const audioContext = new (window.AudioContext || window.webkitAudioContext)()
    const oscillator = audioContext.createOscillator()
    const gainNode = audioContext.createGain()
    
    oscillator.connect(gainNode)
    gainNode.connect(audioContext.destination)
    
    oscillator.frequency.setValueAtTime(800, audioContext.currentTime)
    oscillator.frequency.setValueAtTime(600, audioContext.currentTime + 0.1)
    
    gainNode.gain.setValueAtTime(0.3, audioContext.currentTime)
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3)
    
    oscillator.start(audioContext.currentTime)
    oscillator.stop(audioContext.currentTime + 0.3)
    
    ElMessage.success('提示音测试播放成功')
  } catch (error) {
    console.error('播放提示音失败:', error)
    ElMessage.error('提示音测试播放失败')
  }
}

const requestPermission = async () => {
  if ('Notification' in window) {
    const permission = await Notification.requestPermission()
    notificationPermission.value = permission
    
    if (permission === 'granted') {
      ElMessage.success('通知权限已授权')
    } else {
      ElMessage.warning('通知权限被拒绝')
    }
  }
}

const markAsRead = (notificationId) => {
  markNotificationAsRead(notificationId)
  ElMessage.success('通知已标记为已读')
}

const loadOnlineCount = async () => {
  try {
    const response = await fetch('/api/notification/status')
    if (response.ok) {
      const data = await response.json()
      onlineCount.value = data.data.onlineCount
    }
  } catch (error) {
    console.error('获取在线连接数失败:', error)
  }
}

// 生命周期
onMounted(() => {
  loadOnlineCount()
  // 定期更新在线连接数
  setInterval(loadOnlineCount, 5000)
})
</script>

<style scoped>
.page-container {
  padding: 20px;
}

.test-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.connection-info p {
  margin: 8px 0;
  font-size: 14px;
}

.test-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.test-buttons .el-button {
  margin: 0;
}

.permission-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.notifications-list {
  max-height: 400px;
  overflow-y: auto;
}

.notification-item {
  display: flex;
  align-items: flex-start;
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.3s;
}

.notification-item:hover {
  background-color: #f8f9fa;
}

.notification-item.unread {
  background-color: #f0f9ff;
  border-left: 3px solid #409eff;
}

.notification-icon {
  margin-right: 12px;
  font-size: 20px;
}

.notification-content {
  flex: 1;
}

.notification-title {
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.notification-desc {
  color: #606266;
  font-size: 13px;
  margin-bottom: 4px;
}

.notification-time {
  color: #909399;
  font-size: 12px;
}

.notification-actions {
  margin-left: 12px;
}

.empty-notifications {
  text-align: center;
  padding: 40px 0;
}
</style>
