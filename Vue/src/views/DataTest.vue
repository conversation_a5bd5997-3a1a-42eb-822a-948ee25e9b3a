<template>
  <div class="data-test">
    <div class="page-header">
      <h2 class="page-title">数据加载测试</h2>
      <p class="page-description">测试所有页面的数据加载功能</p>
    </div>

    <el-row :gutter="24">
      <!-- 分类数据测试 -->
      <el-col :span="12">
        <el-card class="test-card">
          <template #header>
            <div class="card-header">
              <span>分类数据测试</span>
              <el-button type="primary" size="small" @click="testCategories">
                <el-icon><Refresh /></el-icon>
                测试
              </el-button>
            </div>
          </template>
          
          <div v-if="categoriesLoading" class="loading">
            <el-skeleton :rows="3" animated />
          </div>
          
          <div v-else-if="categoriesData.length > 0" class="data-list">
            <div v-for="item in categoriesData" :key="item.id" class="data-item">
              <el-tag :type="item.status === 1 ? 'success' : 'danger'">
                {{ item.name }}
              </el-tag>
              <span class="item-desc">{{ item.description }}</span>
            </div>
          </div>
          
          <el-empty v-else description="暂无数据" />
          
          <div class="test-result">
            <el-tag :type="categoriesStatus === 'success' ? 'success' : 'danger'">
              {{ categoriesMessage }}
            </el-tag>
          </div>
        </el-card>
      </el-col>

      <!-- 商品数据测试 -->
      <el-col :span="12">
        <el-card class="test-card">
          <template #header>
            <div class="card-header">
              <span>商品数据测试</span>
              <el-button type="primary" size="small" @click="testFlowers">
                <el-icon><Refresh /></el-icon>
                测试
              </el-button>
            </div>
          </template>
          
          <div v-if="flowersLoading" class="loading">
            <el-skeleton :rows="3" animated />
          </div>
          
          <div v-else-if="flowersData.length > 0" class="data-list">
            <div v-for="item in flowersData" :key="item.id" class="data-item">
              <el-tag :type="item.status === 1 ? 'success' : 'danger'">
                {{ item.name }}
              </el-tag>
              <span class="item-price">¥{{ item.price }}</span>
            </div>
          </div>
          
          <el-empty v-else description="暂无数据" />
          
          <div class="test-result">
            <el-tag :type="flowersStatus === 'success' ? 'success' : 'danger'">
              {{ flowersMessage }}
            </el-tag>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="24" style="margin-top: 24px;">
      <!-- 评价数据测试 -->
      <el-col :span="12">
        <el-card class="test-card">
          <template #header>
            <div class="card-header">
              <span>评价数据测试</span>
              <el-button type="primary" size="small" @click="testReviews">
                <el-icon><Refresh /></el-icon>
                测试
              </el-button>
            </div>
          </template>
          
          <div v-if="reviewsLoading" class="loading">
            <el-skeleton :rows="3" animated />
          </div>
          
          <div v-else-if="reviewsData.length > 0" class="data-list">
            <div v-for="item in reviewsData" :key="item.id" class="data-item">
              <el-tag :type="item.status === 1 ? 'success' : 'danger'">
                {{ item.userName }}
              </el-tag>
              <el-rate v-model="item.rating" disabled size="small" />
            </div>
          </div>
          
          <el-empty v-else description="暂无数据" />
          
          <div class="test-result">
            <el-tag :type="reviewsStatus === 'success' ? 'success' : 'danger'">
              {{ reviewsMessage }}
            </el-tag>
          </div>
        </el-card>
      </el-col>

      <!-- 地址数据测试 -->
      <el-col :span="12">
        <el-card class="test-card">
          <template #header>
            <div class="card-header">
              <span>地址数据测试</span>
              <el-button type="primary" size="small" @click="testAddresses">
                <el-icon><Refresh /></el-icon>
                测试
              </el-button>
            </div>
          </template>
          
          <div v-if="addressesLoading" class="loading">
            <el-skeleton :rows="3" animated />
          </div>
          
          <div v-else-if="addressesData.length > 0" class="data-list">
            <div v-for="item in addressesData.slice(0, 5)" :key="item.code" class="data-item">
              <el-tag type="info">{{ item.name }}</el-tag>
              <span class="item-code">{{ item.code }}</span>
            </div>
          </div>
          
          <el-empty v-else description="暂无数据" />
          
          <div class="test-result">
            <el-tag :type="addressesStatus === 'success' ? 'success' : 'danger'">
              {{ addressesMessage }}
            </el-tag>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 全部测试按钮 -->
    <div class="test-actions">
      <el-button type="primary" size="large" @click="testAll" :loading="allTesting">
        <el-icon><PlayArrow /></el-icon>
        测试所有数据加载
      </el-button>
      
      <el-button type="success" size="large" @click="goToPages">
        <el-icon><View /></el-icon>
        查看实际页面
      </el-button>
    </div>

    <!-- 测试结果汇总 -->
    <el-card class="summary-card" v-if="testSummary.length > 0">
      <template #header>
        <span>测试结果汇总</span>
      </template>
      
      <el-timeline>
        <el-timeline-item
          v-for="(result, index) in testSummary"
          :key="index"
          :type="result.status === 'success' ? 'success' : 'danger'"
          :timestamp="result.timestamp"
        >
          <h4>{{ result.title }}</h4>
          <p>{{ result.message }}</p>
          <p v-if="result.data">数据量: {{ result.data }}条</p>
        </el-timeline-item>
      </el-timeline>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { adminApi } from '@/api/admin'

// 分类数据
const categoriesLoading = ref(false)
const categoriesData = ref([])
const categoriesStatus = ref('')
const categoriesMessage = ref('未测试')

// 商品数据
const flowersLoading = ref(false)
const flowersData = ref([])
const flowersStatus = ref('')
const flowersMessage = ref('未测试')

// 评价数据
const reviewsLoading = ref(false)
const reviewsData = ref([])
const reviewsStatus = ref('')
const reviewsMessage = ref('未测试')

// 地址数据
const addressesLoading = ref(false)
const addressesData = ref([])
const addressesStatus = ref('')
const addressesMessage = ref('未测试')

// 全部测试状态
const allTesting = ref(false)

// 测试结果汇总
const testSummary = ref([])

// 测试分类数据
const testCategories = async () => {
  categoriesLoading.value = true
  categoriesStatus.value = ''
  categoriesMessage.value = '测试中...'
  
  try {
    const response = await adminApi.getCategories()
    categoriesData.value = response.data.records || response.data || []
    categoriesStatus.value = 'success'
    categoriesMessage.value = `成功加载 ${categoriesData.value.length} 条分类数据`
    
    addTestResult('分类数据测试', 'success', categoriesMessage.value, categoriesData.value.length)
  } catch (error) {
    console.error('分类数据测试失败:', error)
    categoriesStatus.value = 'error'
    categoriesMessage.value = '加载失败: ' + error.message
    
    addTestResult('分类数据测试', 'error', categoriesMessage.value)
  } finally {
    categoriesLoading.value = false
  }
}

// 测试商品数据
const testFlowers = async () => {
  flowersLoading.value = true
  flowersStatus.value = ''
  flowersMessage.value = '测试中...'
  
  try {
    const response = await adminApi.getFlowers({ current: 1, size: 10 })
    flowersData.value = response.data.records || response.data || []
    flowersStatus.value = 'success'
    flowersMessage.value = `成功加载 ${flowersData.value.length} 条商品数据`
    
    addTestResult('商品数据测试', 'success', flowersMessage.value, flowersData.value.length)
  } catch (error) {
    console.error('商品数据测试失败:', error)
    flowersStatus.value = 'error'
    flowersMessage.value = '加载失败: ' + error.message
    
    addTestResult('商品数据测试', 'error', flowersMessage.value)
  } finally {
    flowersLoading.value = false
  }
}

// 测试评价数据
const testReviews = async () => {
  reviewsLoading.value = true
  reviewsStatus.value = ''
  reviewsMessage.value = '测试中...'
  
  try {
    const response = await adminApi.getReviews({ current: 1, size: 10 })
    reviewsData.value = response.data.records || response.data || []
    reviewsStatus.value = 'success'
    reviewsMessage.value = `成功加载 ${reviewsData.value.length} 条评价数据`
    
    addTestResult('评价数据测试', 'success', reviewsMessage.value, reviewsData.value.length)
  } catch (error) {
    console.error('评价数据测试失败:', error)
    reviewsStatus.value = 'error'
    reviewsMessage.value = '加载失败: ' + error.message
    
    addTestResult('评价数据测试', 'error', reviewsMessage.value)
  } finally {
    reviewsLoading.value = false
  }
}

// 测试地址数据
const testAddresses = async () => {
  addressesLoading.value = true
  addressesStatus.value = ''
  addressesMessage.value = '测试中...'
  
  try {
    const response = await adminApi.getProvinces()
    addressesData.value = response.data || []
    addressesStatus.value = 'success'
    addressesMessage.value = `成功加载 ${addressesData.value.length} 条省份数据`
    
    addTestResult('地址数据测试', 'success', addressesMessage.value, addressesData.value.length)
  } catch (error) {
    console.error('地址数据测试失败:', error)
    addressesStatus.value = 'error'
    addressesMessage.value = '加载失败: ' + error.message
    
    addTestResult('地址数据测试', 'error', addressesMessage.value)
  } finally {
    addressesLoading.value = false
  }
}

// 测试所有数据
const testAll = async () => {
  allTesting.value = true
  testSummary.value = []
  
  ElMessage.info('开始测试所有数据加载功能...')
  
  await testCategories()
  await new Promise(resolve => setTimeout(resolve, 500))
  
  await testFlowers()
  await new Promise(resolve => setTimeout(resolve, 500))
  
  await testReviews()
  await new Promise(resolve => setTimeout(resolve, 500))
  
  await testAddresses()
  
  allTesting.value = false
  ElMessage.success('所有数据加载测试完成！')
}

// 添加测试结果
const addTestResult = (title, status, message, dataCount = null) => {
  testSummary.value.unshift({
    title,
    status,
    message,
    data: dataCount,
    timestamp: new Date().toLocaleTimeString()
  })
}

// 跳转到实际页面
const goToPages = () => {
  ElMessage.info('请通过左侧菜单访问各个管理页面查看实际效果')
}
</script>

<style scoped>
.data-test {
  padding: 24px;
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
}

.page-description {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
}

.test-card {
  margin-bottom: 24px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.loading {
  padding: 16px 0;
}

.data-list {
  max-height: 200px;
  overflow-y: auto;
}

.data-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 0;
  border-bottom: 1px solid #f3f4f6;
}

.data-item:last-child {
  border-bottom: none;
}

.item-desc {
  font-size: 12px;
  color: #6b7280;
  flex: 1;
}

.item-price {
  font-weight: 600;
  color: #ef4444;
}

.item-code {
  font-size: 12px;
  color: #6b7280;
  font-family: monospace;
}

.test-result {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #f3f4f6;
}

.test-actions {
  text-align: center;
  margin: 32px 0;
  padding: 24px;
  background: #f9fafb;
  border-radius: 8px;
}

.test-actions .el-button {
  margin: 0 8px;
}

.summary-card {
  margin-top: 24px;
}

.el-timeline {
  padding-left: 0;
}
</style>
