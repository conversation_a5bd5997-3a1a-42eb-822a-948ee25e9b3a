// 页面数据渲染检查工具
// 用于检查各个页面的数据加载和渲染状态

import { ElMessage } from 'element-plus'

// 页面数据检查配置
const pageConfigs = {
  dashboard: {
    name: '仪表盘',
    dataChecks: [
      {
        name: '统计卡片',
        selector: '.stats-card',
        minCount: 4,
        dataAttribute: 'data-value'
      },
      {
        name: '图表容器',
        selector: '.chart-container',
        minCount: 2
      },
      {
        name: '最近订单',
        selector: '.recent-orders .el-table-row',
        minCount: 0 // 可能为空
      }
    ]
  },
  users: {
    name: '用户管理',
    dataChecks: [
      {
        name: '用户表格行',
        selector: '.el-table__body .el-table__row',
        minCount: 0
      },
      {
        name: '分页组件',
        selector: '.el-pagination',
        minCount: 1
      },
      {
        name: '搜索表单',
        selector: '.search-form',
        minCount: 1
      },
      {
        name: '统计数据',
        selector: '.control-header .stat-item',
        minCount: 2
      }
    ]
  },
  categories: {
    name: '分类管理',
    dataChecks: [
      {
        name: '分类表格行',
        selector: '.el-table__body .el-table__row',
        minCount: 0
      },
      {
        name: '添加按钮',
        selector: '.add-category-btn',
        minCount: 1
      },
      {
        name: '操作按钮',
        selector: '.el-table__body .el-button',
        minCount: 0
      }
    ]
  },
  flowers: {
    name: '商品管理',
    dataChecks: [
      {
        name: '商品表格行',
        selector: '.el-table__body .el-table__row',
        minCount: 0
      },
      {
        name: '分类筛选',
        selector: '.category-filter',
        minCount: 1
      },
      {
        name: '状态筛选',
        selector: '.status-filter',
        minCount: 1
      },
      {
        name: '分页组件',
        selector: '.el-pagination',
        minCount: 1
      }
    ]
  },
  orders: {
    name: '订单管理',
    dataChecks: [
      {
        name: '订单表格行',
        selector: '.el-table__body .el-table__row',
        minCount: 0
      },
      {
        name: '状态标签',
        selector: '.order-status .el-tag',
        minCount: 0
      },
      {
        name: '搜索框',
        selector: '.search-input',
        minCount: 1
      }
    ]
  },
  reviews: {
    name: '评价管理',
    dataChecks: [
      {
        name: '评价表格行',
        selector: '.el-table__body .el-table__row',
        minCount: 0
      },
      {
        name: '评分组件',
        selector: '.el-rate',
        minCount: 0
      },
      {
        name: '评分筛选',
        selector: '.rating-filter',
        minCount: 1
      }
    ]
  },
  addresses: {
    name: '地址管理',
    dataChecks: [
      {
        name: '省份列表项',
        selector: '.province-list .province-item',
        minCount: 0
      },
      {
        name: '城市列表项',
        selector: '.city-list .city-item',
        minCount: 0
      },
      {
        name: '统计信息',
        selector: '.address-stats .stat-item',
        minCount: 3
      }
    ]
  }
}

// 数据检查器类
class PageDataChecker {
  constructor() {
    this.results = {}
    this.isChecking = false
  }

  // 检查单个页面的数据渲染
  async checkPage(pageName, config = null) {
    if (!config) {
      config = pageConfigs[pageName]
    }

    if (!config) {
      console.warn(`未找到页面 ${pageName} 的检查配置`)
      return null
    }

    console.log(`🔍 检查页面: ${config.name}`)

    const pageResult = {
      name: config.name,
      pageName,
      checks: [],
      passed: 0,
      failed: 0,
      warnings: [],
      timestamp: new Date().toISOString()
    }

    // 等待页面加载完成
    await this.waitForPageLoad()

    // 执行各项检查
    for (const check of config.dataChecks) {
      const checkResult = await this.performCheck(check)
      pageResult.checks.push(checkResult)

      if (checkResult.passed) {
        pageResult.passed++
      } else {
        pageResult.failed++
      }

      if (checkResult.warning) {
        pageResult.warnings.push(checkResult.warning)
      }
    }

    this.results[pageName] = pageResult
    return pageResult
  }

  // 执行单项检查
  async performCheck(check) {
    const elements = document.querySelectorAll(check.selector)
    const count = elements.length

    const result = {
      name: check.name,
      selector: check.selector,
      expectedMin: check.minCount,
      actualCount: count,
      passed: count >= check.minCount,
      elements: Array.from(elements),
      warning: null
    }

    // 检查数据属性
    if (check.dataAttribute && elements.length > 0) {
      const elementsWithData = Array.from(elements).filter(el => 
        el.getAttribute(check.dataAttribute) || el.textContent.trim()
      )
      
      if (elementsWithData.length < elements.length) {
        result.warning = `${elements.length - elementsWithData.length} 个元素缺少数据`
      }
    }

    // 检查空状态
    if (count === 0 && check.minCount > 0) {
      // 检查是否显示了空状态组件
      const emptyState = document.querySelector('.el-empty, .empty-state, .no-data')
      if (emptyState) {
        result.warning = '显示空状态，可能是正常的（无数据）'
        result.passed = true // 空状态也算通过
      }
    }

    console.log(`  ${result.passed ? '✅' : '❌'} ${check.name}: ${count}/${check.minCount}`)
    if (result.warning) {
      console.log(`    ⚠️  ${result.warning}`)
    }

    return result
  }

  // 等待页面加载完成
  async waitForPageLoad(timeout = 5000) {
    return new Promise((resolve) => {
      const startTime = Date.now()
      
      const checkLoading = () => {
        // 检查是否还有加载中的元素
        const loadingElements = document.querySelectorAll('.el-loading-mask, .loading, [loading="true"]')
        const skeletonElements = document.querySelectorAll('.el-skeleton')
        
        if (loadingElements.length === 0 && skeletonElements.length === 0) {
          resolve()
          return
        }

        if (Date.now() - startTime > timeout) {
          console.warn('页面加载超时，继续检查')
          resolve()
          return
        }

        setTimeout(checkLoading, 100)
      }

      // 等待一小段时间让页面初始化
      setTimeout(checkLoading, 500)
    })
  }

  // 检查所有页面
  async checkAllPages() {
    console.log('🚀 开始检查所有页面的数据渲染...')
    
    const allResults = {}
    
    for (const [pageName, config] of Object.entries(pageConfigs)) {
      // 这里需要导航到对应页面，在实际使用中需要配合路由
      console.log(`\n📄 准备检查页面: ${config.name}`)
      console.log(`   请确保当前在 ${pageName} 页面`)
      
      const result = await this.checkPage(pageName, config)
      allResults[pageName] = result
    }

    return allResults
  }

  // 生成检查报告
  generateReport(results = null) {
    if (!results) {
      results = this.results
    }

    console.log('\n📊 页面数据渲染检查报告')
    console.log('==========================')

    let totalPages = 0
    let passedPages = 0
    let totalChecks = 0
    let passedChecks = 0

    for (const [pageName, result] of Object.entries(results)) {
      totalPages++
      totalChecks += result.checks.length
      passedChecks += result.passed

      const pageStatus = result.failed === 0 ? '✅' : '❌'
      console.log(`\n${pageStatus} ${result.name}:`)
      console.log(`   通过: ${result.passed}/${result.checks.length}`)
      
      if (result.failed > 0) {
        console.log(`   失败的检查:`)
        result.checks.filter(c => !c.passed).forEach(check => {
          console.log(`     - ${check.name}: ${check.actualCount}/${check.expectedMin}`)
        })
      }

      if (result.warnings.length > 0) {
        console.log(`   警告:`)
        result.warnings.forEach(warning => {
          console.log(`     ⚠️  ${warning}`)
        })
      }

      if (result.failed === 0) {
        passedPages++
      }
    }

    console.log(`\n📈 总体统计:`)
    console.log(`   页面: ${passedPages}/${totalPages} 通过`)
    console.log(`   检查项: ${passedChecks}/${totalChecks} 通过`)

    const successRate = totalChecks > 0 ? (passedChecks / totalChecks * 100).toFixed(1) : 0
    console.log(`   成功率: ${successRate}%`)

    if (passedPages === totalPages) {
      console.log(`\n🎉 所有页面数据渲染正常！`)
    } else {
      console.log(`\n⚠️  有 ${totalPages - passedPages} 个页面需要检查`)
    }

    return {
      totalPages,
      passedPages,
      totalChecks,
      passedChecks,
      successRate: parseFloat(successRate),
      results
    }
  }

  // 实时检查当前页面
  checkCurrentPage() {
    const path = window.location.pathname
    let pageName = 'unknown'

    // 根据路径判断页面类型
    if (path.includes('/dashboard')) pageName = 'dashboard'
    else if (path.includes('/users')) pageName = 'users'
    else if (path.includes('/categories')) pageName = 'categories'
    else if (path.includes('/flowers')) pageName = 'flowers'
    else if (path.includes('/orders')) pageName = 'orders'
    else if (path.includes('/reviews')) pageName = 'reviews'
    else if (path.includes('/addresses')) pageName = 'addresses'

    if (pageName === 'unknown') {
      console.warn('无法识别当前页面类型')
      return null
    }

    return this.checkPage(pageName)
  }

  // 开始自动检查
  startAutoCheck(interval = 10000) {
    if (this.isChecking) {
      console.log('自动检查已在运行中')
      return
    }

    this.isChecking = true
    console.log(`🔄 开始自动检查，间隔 ${interval/1000} 秒`)

    const checkInterval = setInterval(() => {
      if (!this.isChecking) {
        clearInterval(checkInterval)
        return
      }

      this.checkCurrentPage().then(result => {
        if (result && result.failed > 0) {
          ElMessage.warning(`${result.name} 页面有 ${result.failed} 项检查失败`)
        }
      })
    }, interval)

    return checkInterval
  }

  // 停止自动检查
  stopAutoCheck() {
    this.isChecking = false
    console.log('⏹️  自动检查已停止')
  }
}

// 创建全局实例
const pageDataChecker = new PageDataChecker()

// 导出
export default pageDataChecker
export { PageDataChecker, pageConfigs }

// 在浏览器环境中添加到全局对象
if (typeof window !== 'undefined') {
  window.pageDataChecker = pageDataChecker
  console.log('💡 页面数据检查器已加载，使用 pageDataChecker.checkCurrentPage() 检查当前页面')
}
