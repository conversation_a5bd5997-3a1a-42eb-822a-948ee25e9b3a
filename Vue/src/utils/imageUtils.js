/**
 * 图片URL处理工具
 * 统一处理图片URL的显示问题
 */

/**
 * 获取完整的图片URL
 * @param {string} url - 原始图片URL
 * @returns {string} - 完整的图片URL
 */
export function getImageUrl(url) {
  if (!url) return ''

  // 如果已经是完整URL，直接返回
  if (url.startsWith('http')) {
    return url
  }

  // 对于相对路径，构建完整的URL
  const baseUrl = import.meta.env.VITE_API_BASE_URL || 'https://mxm.qiangs.xyz/api'
  
  // 移除baseUrl末尾的/api，因为图片路径通常包含完整路径
  const cleanBaseUrl = baseUrl.replace('/api', '')
  
  let fullUrl
  if (url.startsWith('/')) {
    fullUrl = `${cleanBaseUrl}${url}`
  } else {
    fullUrl = `${cleanBaseUrl}/${url}`
  }

  return fullUrl
}

/**
 * 处理图片加载错误
 * @param {Event} event - 图片加载错误事件
 * @param {string} fallbackUrl - 备用图片URL
 */
export function handleImageError(event, fallbackUrl = '/default-image.png') {
  console.warn('图片加载失败:', event.target.src)
  if (fallbackUrl) {
    event.target.src = getImageUrl(fallbackUrl)
  }
}

/**
 * 预加载图片
 * @param {string} url - 图片URL
 * @returns {Promise} - 预加载Promise
 */
export function preloadImage(url) {
  return new Promise((resolve, reject) => {
    const img = new Image()
    img.onload = () => resolve(img)
    img.onerror = reject
    img.src = getImageUrl(url)
  })
}

/**
 * 批量预加载图片
 * @param {string[]} urls - 图片URL数组
 * @returns {Promise} - 批量预加载Promise
 */
export function preloadImages(urls) {
  const promises = urls.map(url => preloadImage(url))
  return Promise.allSettled(promises)
}

/**
 * 获取图片的显示尺寸
 * @param {string} url - 图片URL
 * @returns {Promise<{width: number, height: number}>} - 图片尺寸
 */
export function getImageSize(url) {
  return new Promise((resolve, reject) => {
    const img = new Image()
    img.onload = () => {
      resolve({
        width: img.naturalWidth,
        height: img.naturalHeight
      })
    }
    img.onerror = reject
    img.src = getImageUrl(url)
  })
}

/**
 * 压缩图片文件
 * @param {File} file - 原始图片文件
 * @param {number} quality - 压缩质量 (0-1)
 * @param {number} maxWidth - 最大宽度
 * @param {number} maxHeight - 最大高度
 * @returns {Promise<Blob>} - 压缩后的图片Blob
 */
export function compressImage(file, quality = 0.8, maxWidth = 1920, maxHeight = 1080) {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    const img = new Image()

    img.onload = () => {
      // 计算压缩后的尺寸
      let { width, height } = img
      
      if (width > maxWidth) {
        height = (height * maxWidth) / width
        width = maxWidth
      }
      
      if (height > maxHeight) {
        width = (width * maxHeight) / height
        height = maxHeight
      }

      canvas.width = width
      canvas.height = height

      // 绘制并压缩
      ctx.drawImage(img, 0, 0, width, height)
      canvas.toBlob(resolve, file.type, quality)
    }

    img.onerror = reject
    img.src = URL.createObjectURL(file)
  })
}

/**
 * 验证图片文件
 * @param {File} file - 图片文件
 * @param {Object} options - 验证选项
 * @returns {Object} - 验证结果
 */
export function validateImageFile(file, options = {}) {
  const {
    maxSize = 5 * 1024 * 1024, // 5MB
    allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
    maxWidth = 4096,
    maxHeight = 4096
  } = options

  const result = {
    valid: true,
    errors: []
  }

  // 检查文件类型
  if (!allowedTypes.includes(file.type)) {
    result.valid = false
    result.errors.push(`不支持的文件类型: ${file.type}`)
  }

  // 检查文件大小
  if (file.size > maxSize) {
    result.valid = false
    result.errors.push(`文件大小超过限制: ${(file.size / 1024 / 1024).toFixed(2)}MB > ${(maxSize / 1024 / 1024).toFixed(2)}MB`)
  }

  return result
}

/**
 * 生成图片缩略图
 * @param {File} file - 原始图片文件
 * @param {number} width - 缩略图宽度
 * @param {number} height - 缩略图高度
 * @returns {Promise<string>} - 缩略图DataURL
 */
export function generateThumbnail(file, width = 150, height = 150) {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    const img = new Image()

    img.onload = () => {
      canvas.width = width
      canvas.height = height

      // 计算裁剪区域（居中裁剪）
      const scale = Math.max(width / img.width, height / img.height)
      const scaledWidth = img.width * scale
      const scaledHeight = img.height * scale
      const x = (width - scaledWidth) / 2
      const y = (height - scaledHeight) / 2

      ctx.drawImage(img, x, y, scaledWidth, scaledHeight)
      resolve(canvas.toDataURL())
    }

    img.onerror = reject
    img.src = URL.createObjectURL(file)
  })
}
