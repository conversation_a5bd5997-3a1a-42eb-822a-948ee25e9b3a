import { defineStore } from 'pinia'
import { ref } from 'vue'
import { adminApi } from '@/api/admin'

export const useAuthStore = defineStore('auth', () => {
  const isAuthenticated = ref(false)
  const user = ref(null)
  const token = ref('')

  // 登录
  const login = async (credentials) => {
    try {
      const response = await adminApi.login({
        username: credentials.username,
        password: credentials.password,
        captchaId: credentials.captchaId,
        captchaCode: credentials.captchaCode,
        remember: credentials.remember
      })
      const { token: authToken, user: userInfo } = response.data

      token.value = authToken
      user.value = userInfo
      isAuthenticated.value = true

      // 保存到本地存储
      localStorage.setItem('admin_token', authToken)
      localStorage.setItem('admin_user', JSON.stringify(userInfo))

      return response
    } catch (error) {
      throw error
    }
  }

  // 登出
  const logout = async () => {
    try {
      await adminApi.logout()
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      // 清除状态
      isAuthenticated.value = false
      user.value = null
      token.value = ''
      
      // 清除本地存储
      localStorage.removeItem('admin_token')
      localStorage.removeItem('admin_user')
    }
  }

  // 检查认证状态
  const checkAuth = async () => {
    const savedToken = localStorage.getItem('admin_token')
    const savedUser = localStorage.getItem('admin_user')

    if (savedToken && savedUser) {
      try {
        // 先设置本地状态，避免阻塞页面加载
        token.value = savedToken
        user.value = JSON.parse(savedUser)
        isAuthenticated.value = true

        // 异步验证token是否有效（不阻塞页面渲染）
        setTimeout(async () => {
          try {
            await adminApi.verifyToken()
            console.log('Token验证成功')
          } catch (error) {
            console.warn('Token验证失败，清除登录状态:', error)
            // token无效，清除所有状态
            token.value = ''
            user.value = null
            isAuthenticated.value = false
            localStorage.removeItem('admin_token')
            localStorage.removeItem('admin_user')
            // 如果当前不在登录页，跳转到登录页
            if (window.location.pathname !== '/login') {
              window.location.href = '/login'
            }
          }
        }, 100)

        return true
      } catch (error) {
        console.error('解析用户信息失败:', error)
        // 清除无效数据
        localStorage.removeItem('admin_token')
        localStorage.removeItem('admin_user')
        return false
      }
    }

    return false
  }

  // 更新用户信息
  const updateUser = (userInfo) => {
    user.value = { ...user.value, ...userInfo }
    localStorage.setItem('admin_user', JSON.stringify(user.value))
  }

  return {
    isAuthenticated,
    user,
    token,
    login,
    logout,
    checkAuth,
    updateUser
  }
})
