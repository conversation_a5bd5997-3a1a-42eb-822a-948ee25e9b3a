/**
 * 性能优化配置
 */

// 数据加载策略配置
export const loadingConfig = {
  // 是否启用分批加载
  enableBatchLoading: true,
  
  // 批次间延迟时间（毫秒）
  batchDelay: 200,
  
  // 每批最大并发请求数
  maxConcurrentRequests: 2,
  
  // 是否启用懒加载
  enableLazyLoading: true,
  
  // 是否启用缓存
  enableCache: true,
  
  // 缓存过期时间（毫秒）
  cacheExpiration: 5 * 60 * 1000, // 5分钟
}

// API请求优化配置
export const apiConfig = {
  // 请求超时时间
  timeout: 5000,
  
  // 重试次数
  retryCount: 2,
  
  // 重试延迟
  retryDelay: 1000,
  
  // 是否启用请求去重
  enableDeduplication: true,
}

// 组件加载优化配置
export const componentConfig = {
  // 是否启用组件懒加载
  enableLazyComponents: true,
  
  // 预加载延迟时间
  preloadDelay: 1000,
  
  // 是否启用虚拟滚动
  enableVirtualScroll: true,
  
  // 虚拟滚动项目高度
  virtualScrollItemHeight: 60,
}

// 图表加载优化配置
export const chartConfig = {
  // 是否启用图表懒加载
  enableLazyCharts: true,
  
  // 图表加载延迟
  chartLoadDelay: 300,
  
  // 是否启用图表数据缓存
  enableChartCache: true,
  
  // 图表动画持续时间
  animationDuration: 1000,
}

// 获取优化后的加载策略
export function getOptimizedLoadingStrategy() {
  return {
    // Dashboard数据加载批次
    dashboardBatches: [
      {
        name: 'core',
        priority: 1,
        apis: ['stats', 'recentOrders'],
        delay: 0
      },
      {
        name: 'primary',
        priority: 2,
        apis: ['orderChart', 'salesChart'],
        delay: loadingConfig.batchDelay
      },
      {
        name: 'secondary',
        priority: 3,
        apis: ['userGrowthChart', 'categoryChart'],
        delay: loadingConfig.batchDelay * 2
      },
      {
        name: 'tertiary',
        priority: 4,
        apis: ['stockChart', 'orderStatusChart', 'hotProductsChart'],
        delay: loadingConfig.batchDelay * 3
      }
    ]
  }
}

// 性能监控配置
export const performanceConfig = {
  // 是否启用性能监控
  enableMonitoring: true,
  
  // 慢请求阈值（毫秒）
  slowRequestThreshold: 2000,
  
  // 是否记录性能日志
  enablePerformanceLog: true,
  
  // 性能数据上报间隔
  reportInterval: 30000,
}

// 创建性能监控器
export function createPerformanceMonitor() {
  if (!performanceConfig.enableMonitoring) {
    return {
      start: () => {},
      end: () => {},
      log: () => {}
    }
  }
  
  const metrics = {
    apiCalls: [],
    pageLoads: [],
    componentRenders: []
  }
  
  return {
    // 开始监控API调用
    startApiCall: (url) => {
      const startTime = performance.now()
      return {
        end: () => {
          const duration = performance.now() - startTime
          metrics.apiCalls.push({
            url,
            duration,
            timestamp: Date.now(),
            slow: duration > performanceConfig.slowRequestThreshold
          })
          
          if (duration > performanceConfig.slowRequestThreshold) {
            console.warn(`慢请求检测: ${url} 耗时 ${duration.toFixed(2)}ms`)
          }
        }
      }
    },
    
    // 开始监控页面加载
    startPageLoad: (pageName) => {
      const startTime = performance.now()
      return {
        end: () => {
          const duration = performance.now() - startTime
          metrics.pageLoads.push({
            page: pageName,
            duration,
            timestamp: Date.now()
          })
          
          if (performanceConfig.enablePerformanceLog) {
            console.log(`页面加载: ${pageName} 耗时 ${duration.toFixed(2)}ms`)
          }
        }
      }
    },
    
    // 获取性能报告
    getReport: () => {
      return {
        ...metrics,
        summary: {
          avgApiTime: metrics.apiCalls.reduce((sum, call) => sum + call.duration, 0) / metrics.apiCalls.length || 0,
          slowApiCalls: metrics.apiCalls.filter(call => call.slow).length,
          avgPageLoadTime: metrics.pageLoads.reduce((sum, load) => sum + load.duration, 0) / metrics.pageLoads.length || 0
        }
      }
    },
    
    // 清除监控数据
    clear: () => {
      metrics.apiCalls = []
      metrics.pageLoads = []
      metrics.componentRenders = []
    }
  }
}

// 导出默认性能监控器实例
export const performanceMonitor = createPerformanceMonitor()
