# 花语小铺前端项目 - 部署检查清单

## 📋 部署前检查

### ✅ 配置文件检查
- [x] **vite.config.js**: API代理地址已设置为 `https://www.mxm.qiangs.xyz:8080`
- [x] **.env.production**: 生产环境API地址已设置为 `https://www.mxm.qiangs.xyz:8080/api`
- [x] **src/api/request.js**: 已配置使用环境变量 `VITE_API_BASE_URL`
- [x] **src/services/notificationService.js**: WebSocket地址已配置为环境变量

### ✅ 打包文件检查
- [x] **打包成功**: npm run build 执行成功
- [x] **文件完整**: dist目录包含 index.html、assets文件夹等
- [x] **文件大小**: 总大小约 2.5MB，正常范围

## 🚀 宝塔部署步骤检查清单

### 第一步：创建网站
- [ ] 登录宝塔面板
- [ ] 添加站点：`www.mxm.qiangs.xyz`
- [ ] 设置根目录：`/www/wwwroot/www.mxm.qiangs.xyz`
- [ ] 选择纯静态网站

### 第二步：上传文件
- [ ] 清空网站根目录默认文件
- [ ] 上传 `Vue/dist/` 目录下所有文件到网站根目录
- [ ] 确认文件结构正确：
  ```
  /www/wwwroot/www.mxm.qiangs.xyz/
  ├── index.html
  ├── assets/
  │   ├── css/
  │   └── js/
  ├── notification-sound.mp3
  └── image-upload-test.html
  ```

### 第三步：配置SSL证书
- [ ] 申请Let's Encrypt免费证书
- [ ] 域名验证通过
- [ ] 开启强制HTTPS
- [ ] 测试HTTPS访问正常

### 第四步：配置Nginx
- [ ] 添加API代理配置（/api/路径）
- [ ] 添加图片代理配置（/image/路径）
- [ ] 配置SPA路由支持（try_files）
- [ ] 添加WebSocket支持
- [ ] 保存并重载Nginx配置

### 第五步：防火墙配置
- [ ] 开放8080端口（后端API）
- [ ] 开放80端口（HTTP）
- [ ] 开放443端口（HTTPS）

### 第六步：域名解析
- [ ] 添加A记录：www.mxm.qiangs.xyz → 服务器IP
- [ ] DNS解析生效（可用nslookup测试）

## 🔍 部署后验证清单

### 基础访问测试
- [ ] **前端首页**: https://www.mxm.qiangs.xyz 能正常访问
- [ ] **HTTPS重定向**: http://www.mxm.qiangs.xyz 自动跳转到HTTPS
- [ ] **页面加载**: 登录页面正常显示，无404错误

### 功能测试
- [ ] **登录功能**: 能正常登录管理后台
- [ ] **API请求**: 浏览器Network显示API请求成功
- [ ] **数据加载**: 各个管理页面数据正常显示
- [ ] **图片显示**: 商品图片、轮播图等正常显示
- [ ] **WebSocket**: 实时通知功能正常

### 浏览器兼容性测试
- [ ] **Chrome**: 功能正常
- [ ] **Firefox**: 功能正常
- [ ] **Safari**: 功能正常
- [ ] **Edge**: 功能正常

### 性能测试
- [ ] **页面加载速度**: 首次加载 < 3秒
- [ ] **资源缓存**: 静态资源正确缓存
- [ ] **压缩**: Gzip压缩正常工作

## 🚨 故障排查清单

### 如果前端无法访问
- [ ] 检查域名解析是否正确
- [ ] 检查SSL证书是否有效
- [ ] 检查网站文件是否上传完整
- [ ] 检查Nginx配置是否正确

### 如果API请求失败
- [ ] 检查后端服务是否启动（端口8080）
- [ ] 检查防火墙8080端口是否开放
- [ ] 检查Nginx代理配置是否正确
- [ ] 检查后端CORS配置

### 如果页面显示异常
- [ ] 检查浏览器控制台错误信息
- [ ] 检查Network请求状态
- [ ] 检查静态资源是否加载成功
- [ ] 清除浏览器缓存重试

## 📊 关键配置参数

### 域名配置
```
前端域名: www.mxm.qiangs.xyz
后端API: www.mxm.qiangs.xyz:8080/api
WebSocket: wss://www.mxm.qiangs.xyz:8080/api/ws
```

### 目录结构
```
网站根目录: /www/wwwroot/www.mxm.qiangs.xyz/
SSL证书目录: /www/server/panel/vhost/cert/www.mxm.qiangs.xyz/
Nginx配置: /www/server/panel/vhost/nginx/www.mxm.qiangs.xyz.conf
```

### 端口配置
```
HTTP: 80
HTTPS: 443
后端API: 8080
```

## ✅ 部署完成确认

当以下所有项目都完成时，部署即为成功：

- [ ] **前端访问正常**: https://www.mxm.qiangs.xyz 显示登录页面
- [ ] **后端连接正常**: 能够成功登录管理后台
- [ ] **数据显示正常**: 所有管理页面数据正确加载
- [ ] **功能测试通过**: 增删改查功能正常
- [ ] **实时通知正常**: WebSocket连接成功
- [ ] **图片显示正常**: 所有图片资源正确显示
- [ ] **性能表现良好**: 页面加载速度满足要求

## 📞 技术支持信息

### 日志文件位置
```
Nginx访问日志: /www/wwwroot/logs/www.mxm.qiangs.xyz.log
Nginx错误日志: /www/wwwroot/logs/www.mxm.qiangs.xyz.error.log
宝塔面板日志: 面板 → 日志
```

### 常用命令
```bash
# 重启Nginx
systemctl restart nginx

# 查看端口占用
netstat -tlnp | grep :8080

# 测试域名解析
nslookup www.mxm.qiangs.xyz

# 测试SSL证书
openssl s_client -connect www.mxm.qiangs.xyz:443
```

---
**检查清单版本**: v1.0
**最后更新**: 2025-08-02
**状态**: ✅ 准备就绪
