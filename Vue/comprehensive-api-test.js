// 全面的API接口测试脚本
// 用于验证所有后端接口的响应和前端页面的数据渲染

const API_BASE_URL = 'https://mxm.qiangs.xyz:8080/api'
const FRONTEND_URL = 'http://localhost:3000'

// 测试接口列表
const testEndpoints = [
  // 管理员认证接口
  {
    name: '管理员登录',
    method: 'POST',
    url: `${API_BASE_URL}/admin/login`,
    body: { username: 'admin', password: '123456' },
    expectedStatus: 200,
    critical: true
  },
  {
    name: 'Token验证',
    method: 'GET',
    url: `${API_BASE_URL}/admin/verify`,
    headers: { 'Authorization': 'Bearer test-token' },
    expectedStatus: 200,
    critical: true
  },
  
  // 统计数据接口
  {
    name: '获取统计数据',
    method: 'GET',
    url: `${API_BASE_URL}/admin/stats`,
    expectedStatus: 200,
    critical: true
  },
  {
    name: '获取图表数据',
    method: 'GET',
    url: `${API_BASE_URL}/admin/chart/order-week`,
    expectedStatus: 200,
    critical: false
  },
  
  // 用户管理接口
  {
    name: '获取用户列表',
    method: 'GET',
    url: `${API_BASE_URL}/admin/users?current=1&size=10`,
    expectedStatus: 200,
    critical: true
  },
  
  // 分类管理接口
  {
    name: '获取分类列表',
    method: 'GET',
    url: `${API_BASE_URL}/admin/categories?current=1&size=10`,
    expectedStatus: 200,
    critical: true
  },
  
  // 商品管理接口
  {
    name: '获取商品列表',
    method: 'GET',
    url: `${API_BASE_URL}/admin/flowers?current=1&size=10`,
    expectedStatus: 200,
    critical: true
  },
  
  // 订单管理接口
  {
    name: '获取订单列表',
    method: 'GET',
    url: `${API_BASE_URL}/admin/orders?current=1&size=10`,
    expectedStatus: 200,
    critical: true
  },
  
  // 评价管理接口
  {
    name: '获取评价列表',
    method: 'GET',
    url: `${API_BASE_URL}/admin/reviews?current=1&size=10`,
    expectedStatus: 200,
    critical: true
  },
  
  // 地址管理接口
  {
    name: '获取省份列表',
    method: 'GET',
    url: `${API_BASE_URL}/admin/provinces`,
    expectedStatus: 200,
    critical: true
  },
  {
    name: '获取城市列表',
    method: 'GET',
    url: `${API_BASE_URL}/admin/cities/440000`,
    expectedStatus: 200,
    critical: true
  },
  {
    name: '获取区县列表',
    method: 'GET',
    url: `${API_BASE_URL}/admin/districts/440100`,
    expectedStatus: 200,
    critical: true
  }
]

// 前端页面测试列表
const frontendPages = [
  {
    name: '仪表盘',
    url: `${FRONTEND_URL}/dashboard`,
    dataElements: ['.stats-card', '.chart-container', '.recent-orders']
  },
  {
    name: '用户管理',
    url: `${FRONTEND_URL}/users`,
    dataElements: ['.el-table', '.pagination', '.search-form']
  },
  {
    name: '分类管理',
    url: `${FRONTEND_URL}/categories`,
    dataElements: ['.el-table', '.category-list']
  },
  {
    name: '商品管理',
    url: `${FRONTEND_URL}/flowers`,
    dataElements: ['.el-table', '.flower-list']
  },
  {
    name: '订单管理',
    url: `${FRONTEND_URL}/orders`,
    dataElements: ['.el-table', '.order-list']
  },
  {
    name: '评价管理',
    url: `${FRONTEND_URL}/reviews`,
    dataElements: ['.el-table', '.review-list']
  },
  {
    name: '地址管理',
    url: `${FRONTEND_URL}/addresses`,
    dataElements: ['.province-list', '.city-list']
  },
  {
    name: '数据测试页面',
    url: `${FRONTEND_URL}/data-test`,
    dataElements: ['.test-card', '.test-result']
  }
]

// 测试结果存储
let testResults = {
  api: [],
  frontend: [],
  summary: {
    total: 0,
    passed: 0,
    failed: 0,
    critical_failed: 0
  }
}

// API接口测试函数
async function testApiEndpoint(endpoint) {
  console.log(`🔍 测试接口: ${endpoint.name}`)
  
  try {
    const options = {
      method: endpoint.method,
      headers: {
        'Content-Type': 'application/json',
        ...endpoint.headers
      }
    }
    
    if (endpoint.body) {
      options.body = JSON.stringify(endpoint.body)
    }
    
    const response = await fetch(endpoint.url, options)
    const data = await response.json()
    
    const result = {
      name: endpoint.name,
      url: endpoint.url,
      method: endpoint.method,
      status: response.status,
      expectedStatus: endpoint.expectedStatus,
      success: response.status === endpoint.expectedStatus,
      critical: endpoint.critical,
      data: data,
      error: null
    }
    
    if (result.success) {
      console.log(`✅ ${endpoint.name} - 成功 (${response.status})`)
      if (data && data.data) {
        const dataCount = Array.isArray(data.data) 
          ? data.data.length 
          : (data.data.records ? data.data.records.length : '未知')
        console.log(`   数据量: ${dataCount}`)
      }
    } else {
      console.log(`❌ ${endpoint.name} - 失败 (${response.status}, 期望: ${endpoint.expectedStatus})`)
      if (endpoint.critical) {
        console.log(`   ⚠️  这是关键接口，需要立即修复！`)
      }
    }
    
    testResults.api.push(result)
    return result
    
  } catch (error) {
    console.log(`❌ ${endpoint.name} - 网络错误: ${error.message}`)
    
    const result = {
      name: endpoint.name,
      url: endpoint.url,
      method: endpoint.method,
      status: 'ERROR',
      expectedStatus: endpoint.expectedStatus,
      success: false,
      critical: endpoint.critical,
      data: null,
      error: error.message
    }
    
    testResults.api.push(result)
    return result
  }
}

// 前端页面测试函数（需要在浏览器环境中运行）
async function testFrontendPage(page) {
  console.log(`🌐 测试页面: ${page.name}`)
  
  // 这部分需要在浏览器环境中运行
  if (typeof window === 'undefined') {
    console.log(`   ⚠️  页面测试需要在浏览器环境中运行`)
    return {
      name: page.name,
      url: page.url,
      success: null,
      message: '需要在浏览器环境中测试'
    }
  }
  
  try {
    // 在浏览器中测试页面加载和数据渲染
    const result = {
      name: page.name,
      url: page.url,
      success: true,
      elements: [],
      message: '页面测试需要手动验证'
    }
    
    testResults.frontend.push(result)
    return result
    
  } catch (error) {
    const result = {
      name: page.name,
      url: page.url,
      success: false,
      elements: [],
      message: error.message
    }
    
    testResults.frontend.push(result)
    return result
  }
}

// 运行所有API测试
async function runApiTests() {
  console.log('🚀 开始API接口测试...\n')
  
  for (const endpoint of testEndpoints) {
    await testApiEndpoint(endpoint)
    console.log('') // 空行分隔
    
    // 添加延迟避免请求过快
    await new Promise(resolve => setTimeout(resolve, 500))
  }
}

// 运行所有前端测试
async function runFrontendTests() {
  console.log('🌐 开始前端页面测试...\n')
  
  for (const page of frontendPages) {
    await testFrontendPage(page)
    console.log('') // 空行分隔
  }
}

// 生成测试报告
function generateReport() {
  console.log('📊 测试结果汇总:')
  console.log('================')
  
  // API测试统计
  const apiTotal = testResults.api.length
  const apiPassed = testResults.api.filter(r => r.success).length
  const apiFailed = testResults.api.filter(r => !r.success).length
  const criticalFailed = testResults.api.filter(r => !r.success && r.critical).length
  
  console.log(`\n📡 API接口测试:`)
  console.log(`   总计: ${apiTotal}`)
  console.log(`   ✅ 成功: ${apiPassed}`)
  console.log(`   ❌ 失败: ${apiFailed}`)
  console.log(`   🚨 关键失败: ${criticalFailed}`)
  
  // 详细失败信息
  if (apiFailed > 0) {
    console.log(`\n❌ 失败的接口:`)
    testResults.api.filter(r => !r.success).forEach(result => {
      console.log(`   - ${result.name}: ${result.status} (${result.error || '状态码不匹配'})`)
      if (result.critical) {
        console.log(`     ⚠️  关键接口，需要立即修复！`)
      }
    })
  }
  
  // 成功的接口
  if (apiPassed > 0) {
    console.log(`\n✅ 成功的接口:`)
    testResults.api.filter(r => r.success).forEach(result => {
      console.log(`   - ${result.name}: ${result.status}`)
    })
  }
  
  // 前端测试统计
  console.log(`\n🌐 前端页面测试:`)
  console.log(`   总计: ${frontendPages.length}`)
  console.log(`   需要手动验证各页面的数据加载和渲染`)
  
  // 总体评估
  console.log(`\n🎯 总体评估:`)
  if (criticalFailed === 0) {
    console.log(`   ✅ 所有关键接口正常，系统可以正常使用`)
  } else {
    console.log(`   🚨 有 ${criticalFailed} 个关键接口失败，需要立即修复`)
  }
  
  if (apiFailed === 0) {
    console.log(`   🎉 所有API接口测试通过！`)
  } else {
    console.log(`   ⚠️  有 ${apiFailed} 个接口需要修复`)
  }
  
  // 建议
  console.log(`\n💡 建议:`)
  console.log(`   1. 访问 ${FRONTEND_URL}/data-test 进行可视化测试`)
  console.log(`   2. 逐个访问各管理页面验证数据加载`)
  console.log(`   3. 测试搜索、筛选、分页等功能`)
  console.log(`   4. 检查控制台是否有JavaScript错误`)
  
  return testResults
}

// 主测试函数
async function runAllTests() {
  console.log('🔧 花卉管理系统 - 全面接口和页面测试')
  console.log('==========================================\n')
  
  // 运行API测试
  await runApiTests()
  
  // 运行前端测试
  await runFrontendTests()
  
  // 生成报告
  const results = generateReport()
  
  console.log('\n🔗 快速链接:')
  console.log(`   - 前端应用: ${FRONTEND_URL}`)
  console.log(`   - 数据测试: ${FRONTEND_URL}/data-test`)
  console.log(`   - 后端API: ${API_BASE_URL}`)
  
  return results
}

// 浏览器环境检测
if (typeof window !== 'undefined') {
  // 浏览器环境
  window.comprehensiveTest = {
    runAllTests,
    runApiTests,
    runFrontendTests,
    generateReport,
    testResults
  }
  console.log('💡 在浏览器控制台中运行 comprehensiveTest.runAllTests() 开始测试')
} else {
  // Node.js环境
  if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
      runAllTests,
      runApiTests,
      runFrontendTests,
      generateReport,
      testResults
    }
  }
}

// 自动运行（如果直接执行此文件）
if (typeof require !== 'undefined' && require.main === module) {
  runAllTests().then(() => {
    console.log('\n✨ 测试完成！')
  }).catch(error => {
    console.error('❌ 测试过程中出现错误:', error)
  })
}
