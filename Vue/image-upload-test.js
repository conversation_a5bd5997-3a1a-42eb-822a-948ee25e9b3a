// 图片上传功能测试脚本
// 测试商品图片上传和修改功能

const API_BASE_URL = 'http://localhost:8080/api'

// 获取Token（模拟登录）
async function getAuthToken() {
  try {
    const response = await fetch(`${API_BASE_URL}/admin/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        username: 'admin',
        password: '123456'
      })
    })
    
    const data = await response.json()
    if (data.code === 200) {
      return data.data.token
    } else {
      throw new Error('登录失败: ' + data.message)
    }
  } catch (error) {
    console.error('获取Token失败:', error)
    return null
  }
}

// 创建测试图片文件
function createTestImageFile(name = 'test-image.jpg', size = 1024) {
  // 创建一个简单的测试图片数据
  const canvas = document.createElement('canvas')
  canvas.width = 200
  canvas.height = 200
  const ctx = canvas.getContext('2d')
  
  // 绘制一个简单的测试图片
  ctx.fillStyle = '#4CAF50'
  ctx.fillRect(0, 0, 200, 200)
  ctx.fillStyle = '#FFFFFF'
  ctx.font = '20px Arial'
  ctx.textAlign = 'center'
  ctx.fillText('TEST IMAGE', 100, 100)
  ctx.fillText(name, 100, 130)
  
  return new Promise((resolve) => {
    canvas.toBlob((blob) => {
      const file = new File([blob], name, { type: 'image/jpeg' })
      resolve(file)
    }, 'image/jpeg', 0.8)
  })
}

// 测试图片上传接口
async function testImageUpload(token) {
  console.log('🔍 测试图片上传接口...')
  
  try {
    // 创建测试图片
    const testFile = await createTestImageFile('test-shop-image.jpg')
    console.log(`   创建测试图片: ${testFile.name} (${testFile.size} bytes)`)
    
    // 创建FormData
    const formData = new FormData()
    formData.append('file', testFile)
    
    // 发送上传请求
    const response = await fetch(`${API_BASE_URL}/admin/upload/image`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`
      },
      body: formData
    })
    
    const data = await response.json()
    
    if (response.status === 200 && data.code === 200) {
      console.log('✅ 图片上传成功!')
      console.log(`   文件名: ${data.data.fileName}`)
      console.log(`   访问URL: ${data.data.url}`)
      console.log(`   文件大小: ${data.data.size} bytes`)
      
      // 测试图片访问
      await testImageAccess(data.data.url)
      
      return data.data
    } else {
      console.log(`❌ 图片上传失败: ${response.status}`)
      console.log(`   错误信息: ${data.message || '未知错误'}`)
      return null
    }
    
  } catch (error) {
    console.log(`❌ 图片上传异常: ${error.message}`)
    return null
  }
}

// 测试图片访问
async function testImageAccess(imageUrl) {
  console.log('🔍 测试图片访问...')
  
  try {
    const response = await fetch(imageUrl)
    
    if (response.status === 200) {
      console.log('✅ 图片访问成功!')
      console.log(`   状态码: ${response.status}`)
      console.log(`   内容类型: ${response.headers.get('content-type')}`)
    } else {
      console.log(`❌ 图片访问失败: ${response.status}`)
    }
    
  } catch (error) {
    console.log(`❌ 图片访问异常: ${error.message}`)
  }
}

// 测试批量图片上传
async function testBatchImageUpload(token) {
  console.log('🔍 测试批量图片上传...')
  
  try {
    // 创建多个测试图片
    const testFiles = await Promise.all([
      createTestImageFile('batch-test-1.jpg'),
      createTestImageFile('batch-test-2.jpg'),
      createTestImageFile('batch-test-3.jpg')
    ])
    
    console.log(`   创建 ${testFiles.length} 个测试图片`)
    
    // 创建FormData
    const formData = new FormData()
    testFiles.forEach(file => {
      formData.append('files', file)
    })
    
    // 发送批量上传请求
    const response = await fetch(`${API_BASE_URL}/admin/upload/shop-images`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`
      },
      body: formData
    })
    
    const data = await response.json()
    
    if (response.status === 200 && data.code === 200) {
      console.log('✅ 批量图片上传成功!')
      console.log(`   成功上传 ${data.data.length} 张图片`)
      
      data.data.forEach((item, index) => {
        console.log(`   图片${index + 1}: ${item.fileName} -> ${item.url}`)
      })
      
      return data.data
    } else {
      console.log(`❌ 批量图片上传失败: ${response.status}`)
      console.log(`   错误信息: ${data.message || '未知错误'}`)
      return null
    }
    
  } catch (error) {
    console.log(`❌ 批量图片上传异常: ${error.message}`)
    return null
  }
}

// 测试商品创建（包含图片）
async function testFlowerCreation(token, imageData) {
  console.log('🔍 测试商品创建（包含图片）...')
  
  try {
    const flowerData = {
      name: '测试商品（带图片）',
      description: '这是一个包含图片的测试商品',
      price: 99.99,
      originalPrice: 129.99,
      categoryId: 1,
      stockQuantity: 100,
      status: 1,
      mainImage: imageData ? imageData.url : null
    }
    
    const response = await fetch(`${API_BASE_URL}/admin/flowers`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(flowerData)
    })
    
    const data = await response.json()
    
    if (response.status === 200 && data.code === 200) {
      console.log('✅ 商品创建成功!')
      console.log(`   商品ID: ${data.data.id}`)
      console.log(`   商品名称: ${data.data.name}`)
      console.log(`   主图URL: ${data.data.mainImage}`)
      
      return data.data
    } else {
      console.log(`❌ 商品创建失败: ${response.status}`)
      console.log(`   错误信息: ${data.message || '未知错误'}`)
      return null
    }
    
  } catch (error) {
    console.log(`❌ 商品创建异常: ${error.message}`)
    return null
  }
}

// 测试商品图片更新
async function testFlowerImageUpdate(token, flowerId, newImageData) {
  console.log('🔍 测试商品图片更新...')
  
  try {
    const updateData = {
      mainImage: newImageData.url
    }
    
    const response = await fetch(`${API_BASE_URL}/admin/flowers/${flowerId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(updateData)
    })
    
    const data = await response.json()
    
    if (response.status === 200 && data.code === 200) {
      console.log('✅ 商品图片更新成功!')
      console.log(`   商品ID: ${data.data.id}`)
      console.log(`   新主图URL: ${data.data.mainImage}`)
      
      return data.data
    } else {
      console.log(`❌ 商品图片更新失败: ${response.status}`)
      console.log(`   错误信息: ${data.message || '未知错误'}`)
      return null
    }
    
  } catch (error) {
    console.log(`❌ 商品图片更新异常: ${error.message}`)
    return null
  }
}

// 运行完整的图片上传测试
async function runImageUploadTests() {
  console.log('🚀 开始图片上传功能全面测试...\n')
  
  // 获取认证Token
  console.log('🔐 获取认证Token...')
  const token = await getAuthToken()
  if (!token) {
    console.log('❌ 无法获取认证Token，测试终止')
    return
  }
  console.log('✅ Token获取成功\n')
  
  let testResults = {
    singleUpload: null,
    batchUpload: null,
    flowerCreation: null,
    imageUpdate: null
  }
  
  // 1. 测试单张图片上传
  console.log('📋 测试1: 单张图片上传')
  console.log('========================')
  testResults.singleUpload = await testImageUpload(token)
  console.log('')
  
  // 2. 测试批量图片上传
  console.log('📋 测试2: 批量图片上传')
  console.log('========================')
  testResults.batchUpload = await testBatchImageUpload(token)
  console.log('')
  
  // 3. 测试商品创建（包含图片）
  if (testResults.singleUpload) {
    console.log('📋 测试3: 商品创建（包含图片）')
    console.log('==============================')
    testResults.flowerCreation = await testFlowerCreation(token, testResults.singleUpload)
    console.log('')
  }
  
  // 4. 测试商品图片更新
  if (testResults.flowerCreation && testResults.batchUpload && testResults.batchUpload.length > 0) {
    console.log('📋 测试4: 商品图片更新')
    console.log('======================')
    testResults.imageUpdate = await testFlowerImageUpdate(
      token, 
      testResults.flowerCreation.id, 
      testResults.batchUpload[0]
    )
    console.log('')
  }
  
  // 生成测试报告
  generateImageUploadReport(testResults)
  
  return testResults
}

// 生成图片上传测试报告
function generateImageUploadReport(results) {
  console.log('📊 图片上传功能测试报告')
  console.log('========================')
  
  const tests = [
    { name: '单张图片上传', result: results.singleUpload },
    { name: '批量图片上传', result: results.batchUpload },
    { name: '商品创建（含图片）', result: results.flowerCreation },
    { name: '商品图片更新', result: results.imageUpdate }
  ]
  
  let passedCount = 0
  let totalCount = tests.length
  
  tests.forEach(test => {
    const status = test.result ? '✅ 通过' : '❌ 失败'
    console.log(`   ${status} ${test.name}`)
    if (test.result) passedCount++
  })
  
  console.log(`\n📈 总体统计:`)
  console.log(`   总测试数: ${totalCount}`)
  console.log(`   ✅ 通过: ${passedCount}`)
  console.log(`   ❌ 失败: ${totalCount - passedCount}`)
  
  const successRate = totalCount > 0 ? (passedCount / totalCount * 100).toFixed(1) : 0
  console.log(`   成功率: ${successRate}%`)
  
  if (passedCount === totalCount) {
    console.log(`\n🎉 所有图片上传功能测试通过！`)
    console.log(`\n💡 现在可以在前端页面测试:`)
    console.log(`   1. 访问 http://localhost:3000/flowers/create 测试商品创建`)
    console.log(`   2. 测试主图上传功能`)
    console.log(`   3. 测试详情图片上传功能`)
    console.log(`   4. 测试图片预览和删除功能`)
  } else {
    console.log(`\n⚠️  有 ${totalCount - passedCount} 个测试失败，需要检查`)
  }
  
  return {
    total: totalCount,
    passed: passedCount,
    failed: totalCount - passedCount,
    successRate: parseFloat(successRate)
  }
}

// 浏览器环境检测
if (typeof window !== 'undefined') {
  // 浏览器环境
  window.imageUploadTest = {
    runImageUploadTests,
    testImageUpload,
    testBatchImageUpload,
    testFlowerCreation,
    testFlowerImageUpdate,
    createTestImageFile
  }
  console.log('💡 在浏览器控制台中运行 imageUploadTest.runImageUploadTests() 开始测试')
} else {
  // Node.js环境 - 需要额外的依赖来创建图片
  console.log('⚠️  此测试脚本需要在浏览器环境中运行以创建测试图片')
}

// 导出
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    runImageUploadTests,
    testImageUpload,
    testBatchImageUpload,
    testFlowerCreation,
    testFlowerImageUpdate
  }
}
