# 花语小铺前端项目 - 宝塔部署详细指南

## 📋 部署前准备

### 1. 项目配置确认
✅ **已完成配置修正**：
- **前端访问地址**: https://www.mxm.qiangs.xyz
- **后端API地址**: https://www.mxm.qiangs.xyz:8080/api
- **WebSocket地址**: wss://www.mxm.qiangs.xyz:8080/api/ws

### 2. 打包文件位置
- **打包目录**: `c:\Users\<USER>\Desktop\flower\Vue\dist\`
- **状态**: ✅ 已成功打包

## 🚀 宝塔面板部署步骤

### 第一步：创建网站

1. **登录宝塔面板**
   - 访问您的宝塔面板地址
   - 输入用户名和密码登录

2. **添加站点**
   ```
   网站 → 添加站点
   ```
   
3. **填写站点信息**
   ```
   域名: www.mxm.qiangs.xyz
   根目录: /www/wwwroot/www.mxm.qiangs.xyz
   FTP: 不创建
   数据库: 不创建
   PHP版本: 纯静态
   ```

4. **点击提交创建**

### 第二步：上传文件

1. **进入文件管理**
   ```
   文件 → 进入网站根目录
   /www/wwwroot/www.mxm.qiangs.xyz
   ```

2. **清空默认文件**
   - 删除默认的 `index.html` 和其他文件

3. **上传打包文件**
   
   **方法一：直接上传（推荐）**
   - 将本地 `Vue/dist/` 目录下的所有文件上传到网站根目录
   - 包括：`index.html`、`assets/` 文件夹、`notification-sound.mp3` 等

   **方法二：压缩包上传**
   - 将 `Vue/dist/` 目录打包为 `dist.zip`
   - 上传到网站根目录
   - 解压缩并将内容移动到根目录

### 第三步：配置SSL证书

1. **申请SSL证书**
   ```
   网站 → 找到 www.mxm.qiangs.xyz → SSL
   ```

2. **选择证书类型**
   - **Let's Encrypt（免费）**: 推荐，自动续期
   - **其他证书**: 如果有付费证书可以上传

3. **申请Let's Encrypt证书**
   ```
   域名: www.mxm.qiangs.xyz
   验证方式: 文件验证
   点击申请
   ```

4. **强制HTTPS**
   - 证书申请成功后，开启"强制HTTPS"

### 第四步：配置Nginx反向代理

1. **编辑网站配置**
   ```
   网站 → www.mxm.qiangs.xyz → 设置 → 配置文件
   ```

2. **添加API代理配置**
   在 `server` 块中添加以下配置：

   ```nginx
   # API代理配置
   location /api/ {
       proxy_pass http://127.0.0.1:8080/api/;
       proxy_set_header Host $host;
       proxy_set_header X-Real-IP $remote_addr;
       proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
       proxy_set_header X-Forwarded-Proto $scheme;
       
       # 支持WebSocket
       proxy_http_version 1.1;
       proxy_set_header Upgrade $http_upgrade;
       proxy_set_header Connection "upgrade";
       
       # 超时设置
       proxy_connect_timeout 60s;
       proxy_send_timeout 60s;
       proxy_read_timeout 60s;
   }

   # 图片代理配置
   location /image/ {
       proxy_pass http://127.0.0.1:8080/image/;
       proxy_set_header Host $host;
       proxy_set_header X-Real-IP $remote_addr;
       proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
       proxy_set_header X-Forwarded-Proto $scheme;
   }

   # 前端路由支持（SPA应用）
   location / {
       try_files $uri $uri/ /index.html;
   }
   ```

3. **保存并重载配置**
   - 点击保存
   - 重载Nginx配置

### 第五步：配置防火墙

1. **开放端口**
   ```
   安全 → 防火墙 → 添加规则
   ```

2. **添加规则**
   ```
   端口: 8080
   协议: TCP
   策略: 放行
   备注: 后端API端口
   ```

### 第六步：域名解析配置

1. **添加A记录**
   在您的域名服务商处添加：
   ```
   类型: A
   主机记录: www
   记录值: 您的服务器IP地址
   TTL: 600
   ```

2. **添加根域名记录（可选）**
   ```
   类型: A
   主机记录: @
   记录值: 您的服务器IP地址
   TTL: 600
   ```

## 🔧 完整Nginx配置示例

```nginx
server {
    listen 80;
    listen 443 ssl http2;
    server_name www.mxm.qiangs.xyz;
    index index.html;
    root /www/wwwroot/www.mxm.qiangs.xyz;

    # SSL证书配置
    ssl_certificate /www/server/panel/vhost/cert/www.mxm.qiangs.xyz/fullchain.pem;
    ssl_certificate_key /www/server/panel/vhost/cert/www.mxm.qiangs.xyz/privkey.pem;
    ssl_protocols TLSv1.1 TLSv1.2 TLSv1.3;
    ssl_ciphers EECDH+CHACHA20:EECDH+CHACHA20-draft:EECDH+AES128:RSA+AES128:EECDH+AES256:RSA+AES256:EECDH+3DES:RSA+3DES:!MD5;
    ssl_prefer_server_ciphers on;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # 强制HTTPS
    if ($server_port !~ 443){
        rewrite ^(/.*)$ https://$host$1 permanent;
    }

    # API代理配置
    location /api/ {
        proxy_pass http://127.0.0.1:8080/api/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 支持WebSocket
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        
        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # 图片代理配置
    location /image/ {
        proxy_pass http://127.0.0.1:8080/image/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # 前端路由支持（SPA应用）
    location / {
        try_files $uri $uri/ /index.html;
    }

    # 安全配置
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src * data: 'unsafe-eval' 'unsafe-inline'" always;

    # 禁止访问敏感文件
    location ~ /\.ht {
        deny all;
    }
}
```

## ✅ 部署验证

### 1. 访问测试
- **前端地址**: https://www.mxm.qiangs.xyz
- **应该能正常显示登录页面**

### 2. API连接测试
- **登录功能**: 测试管理员登录
- **数据加载**: 检查各个页面数据是否正常加载

### 3. 浏览器控制台检查
- **F12打开开发者工具**
- **检查Console是否有错误**
- **检查Network请求是否正常**

## 🚨 常见问题解决

### 1. 404错误
- **检查文件上传是否完整**
- **确认index.html在根目录**

### 2. API请求失败
- **检查后端服务是否启动**
- **确认8080端口是否开放**
- **检查Nginx代理配置**

### 3. SSL证书问题
- **重新申请Let's Encrypt证书**
- **检查域名解析是否正确**

### 4. 跨域问题
- **确认后端CORS配置**
- **检查Nginx代理头设置**

## 📞 技术支持

如遇到问题，请检查：
1. **宝塔面板日志**: 网站 → 日志
2. **Nginx错误日志**: `/www/wwwroot/logs/`
3. **浏览器控制台错误信息**

---
**部署完成时间**: 2025-08-02
**状态**: ✅ 配置已优化，可直接部署
