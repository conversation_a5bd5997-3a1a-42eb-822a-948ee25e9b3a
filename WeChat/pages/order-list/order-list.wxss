/* pages/order-list/order-list.wxss */

/* 基础样式重置 */
page {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}

/* 页面容器 */
.page-container {
  min-height: 100vh;
  background: #f5f5f5;
  display: flex;
  flex-direction: column;
  width: 100%;
  box-sizing: border-box;
  padding: 0;
}

/* 标签栏 */
.tabs-container {
  background: white;
  border-bottom: 1rpx solid #e8e8e8;
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.tabs {
  display: flex;
  padding: 0;
}

.tab-item {
  flex: 1;
  padding: 30rpx 20rpx;
  position: relative;
  text-align: center;
}

.tab-item.active .tab-text {
  color: #ff6b9d;
  font-weight: 600;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background: linear-gradient(135deg, #ff6b9d, #ff8fab);
  border-radius: 2rpx;
}

.tab-text {
  font-size: 28rpx;
  color: #666;
  transition: all 0.2s ease;
  white-space: nowrap;
}

/* 内容区域 */
.content {
  flex: 1;
  padding: 20rpx 30rpx;
  box-sizing: border-box;
  width: 100%;
  overflow-x: hidden;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 60rpx;
  text-align: center;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 20rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: #999;
}

/* 订单列表 */
.order-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
  width: 100%;
  box-sizing: border-box;
}

.order-item {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin: 0;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
  transition: all 0.2s ease;
  width: 100%;
  box-sizing: border-box;
}

.order-item:active {
  transform: scale(0.98);
  box-shadow: 0 1rpx 8rpx rgba(0, 0, 0, 0.12);
}

/* 订单头部 */
.order-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.order-info {
  flex: 1;
}

.order-no {
  font-size: 26rpx;
  color: #333;
  font-family: monospace;
  display: block;
  margin-bottom: 8rpx;
}

.order-time {
  font-size: 24rpx;
  color: #999;
  display: block;
}

.order-status {
  flex-shrink: 0;
}

.status-text {
  font-size: 26rpx;
  color: #ff6b9d;
  font-weight: 600;
  background: rgba(255, 107, 157, 0.1);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  border: 1rpx solid rgba(255, 107, 157, 0.2);
}

/* 配送方式 */
.delivery-type {
  margin-bottom: 20rpx;
}

.delivery-text {
  font-size: 24rpx;
  color: #666;
  background: #f8f8f8;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
}

/* 商品信息 */
.order-products {
  margin-bottom: 30rpx;
}

.product-summary {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.product-count {
  font-size: 26rpx;
  color: #666;
}

.order-amount {
  font-size: 32rpx;
  font-weight: 600;
  color: #ff6b9d;
}

/* 订单操作按钮 */
.order-actions {
  display: flex;
  justify-content: flex-end;
  gap: 16rpx;
  margin-top: 8rpx;
  width: 100%;
  box-sizing: border-box;
}

.action-btn {
  padding: 18rpx 36rpx;
  border-radius: 50rpx;
  font-size: 24rpx;
  text-align: center;
  transition: all 0.2s ease;
  min-width: 120rpx;
}

.action-btn.primary {
  background: linear-gradient(135deg, #ff6b9d, #ff8fab);
  color: white;
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 157, 0.3);
}

.action-btn.secondary {
  background: #f8f8f8;
  color: #666;
  border: 1rpx solid #e8e8e8;
}

.action-btn:active {
  transform: scale(0.95);
}

.btn-text {
  font-size: 24rpx;
}

/* 加载状态 */
.loading-more {
  text-align: center;
  padding: 40rpx;
}

.loading-text {
  font-size: 26rpx;
  color: #999;
}

.no-more {
  text-align: center;
  padding: 40rpx;
}

.no-more-text {
  font-size: 24rpx;
  color: #ccc;
}