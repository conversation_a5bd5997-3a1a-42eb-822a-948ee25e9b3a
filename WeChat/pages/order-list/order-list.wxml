<!-- pages/order-list/order-list.wxml -->
<view class="page-container">
  <!-- 标签栏 -->
  <view class="tabs-container">
    <view class="tabs">
      <view class="tab-item {{currentTab === index ? 'active' : ''}}"
            wx:for="{{tabs}}" wx:key="index"
            bindtap="switchTab" data-index="{{index}}">
        <text class="tab-text">{{item.name}}</text>
      </view>
    </view>
  </view>

  <!-- 订单列表 -->
  <scroll-view class="content" scroll-y="true" bindscrolltolower="onReachBottom">
    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{isEmpty && !isLoading}}">
      <image class="empty-image" src="/images/empty-order.png" mode="aspectFit" />
      <text class="empty-text">暂无订单</text>
      <text class="empty-desc">快去选购心仪的花束吧~</text>
    </view>

    <!-- 订单列表 -->
    <view class="order-list" wx:else>
      <view class="order-item" wx:for="{{orders}}" wx:key="id" bindtap="viewOrderDetail" data-id="{{item.id}}">
        <!-- 订单头部 -->
        <view class="order-header">
          <view class="order-info">
            <text class="order-no">订单号：{{item.orderNo}}</text>
            <text class="order-time">{{item.createdAt}}</text>
          </view>
          <view class="order-status">
            <text class="status-text">{{statusText[item.status]}}</text>
          </view>
        </view>

        <!-- 配送方式 -->
        <view class="delivery-type">
          <text class="delivery-text">{{deliveryTypeText[item.deliveryType]}}</text>
        </view>

        <!-- 商品信息（这里简化显示，实际需要获取订单商品） -->
        <view class="order-products">
          <view class="product-summary">
            <text class="product-count">共{{item.itemCount || 1}}件商品</text>
            <text class="order-amount">¥{{item.finalAmount}}</text>
          </view>
        </view>

        <!-- 订单操作按钮 -->
        <view class="order-actions" catchtap="stopPropagation">
          <view class="action-btn secondary" bindtap="contactService">
            <text class="btn-text">联系客服</text>
          </view>

          <!-- 取消订单：已下单状态可以取消 -->
          <view class="action-btn secondary" wx:if="{{item.status === 2}}" bindtap="cancelOrder" data-id="{{item.id}}">
            <text class="btn-text">取消订单</text>
          </view>

          <!-- 货到付款：配送完成后显示 -->
          <view class="action-btn primary" wx:if="{{item.status === 2 && item.deliveryStatus === 2}}" bindtap="payOnDelivery" data-id="{{item.id}}">
            <text class="btn-text">货到付款</text>
          </view>

          <!-- 确认收货：付款后显示 -->
          <view class="action-btn primary" wx:if="{{item.status === 1 && item.paymentStatus === 1}}" bindtap="confirmReceive" data-id="{{item.id}}">
            <text class="btn-text">确认{{item.deliveryType === 1 ? '收货' : '取货'}}</text>
          </view>

          <view class="action-btn primary" wx:if="{{item.status === 4 && item.reviewStatus === 0}}" bindtap="goToReview" data-id="{{item.id}}">
            <text class="btn-text">去评价</text>
          </view>

          <view class="action-btn primary" bindtap="buyAgain" data-id="{{item.id}}">
            <text class="btn-text">再次购买</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 加载更多 -->
    <view class="loading-more" wx:if="{{isLoading}}">
      <text class="loading-text">加载中...</text>
    </view>

    <view class="no-more" wx:if="{{!hasMore && orders.length > 0}}">
      <text class="no-more-text">没有更多订单了</text>
    </view>
  </scroll-view>
</view>