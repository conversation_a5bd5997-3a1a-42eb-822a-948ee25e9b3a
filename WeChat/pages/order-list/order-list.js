// pages/order-list/order-list.js
const app = getApp()

Page({
  data: {
    currentTab: 2, // 默认显示"已下单"页面
    tabs: [
      { name: '全部', status: null },
      { name: '已下单', status: 2 },
      { name: '配送中', status: 3 },
      { name: '待付款', status: 1 },
      { name: '已完成', status: 4 }
    ],
    orders: [],
    currentPage: 1,
    pageSize: 10,
    hasMore: true,
    isLoading: false,
    isEmpty: false,
    statusText: {
      1: '待付款',
      2: '已下单',
      3: '配送中/取货中',
      4: '已完成',
      5: '已取消'
    },
    deliveryTypeText: {
      1: '外卖配送',
      2: '到店自取'
    }
  },

  onLoad(options) {
    // 如果传入了状态参数，切换到对应tab
    if (options.status) {
      // 特殊处理待收货状态（状态1且已支付）
      if (options.status == '1' && options.paymentStatus == '1') {
        // 待收货状态，显示全部tab但过滤条件为状态1且已支付
        this.setData({
          currentTab: 0, // 显示全部tab
          filterStatus: 1,
          filterPaymentStatus: 1,
          fromBadgeStatus: 'toReceive' // 记录来源状态
        })
      } else {
        const tabIndex = this.data.tabs.findIndex(tab => tab.status == options.status)
        if (tabIndex !== -1) {
          this.setData({
            currentTab: tabIndex,
            fromBadgeStatus: this.getStatusKey(options.status) // 记录来源状态
          })
        }
      }
    }
    this.loadOrders(true)
  },

  // 根据状态值获取状态键
  getStatusKey(status) {
    const statusMap = {
      '1': 'pending',
      '2': 'ordered',
      '3': 'shipping',
      '4': 'completed'
    }
    return statusMap[status] || null
  },

  onShow() {
    // 每次显示页面时刷新订单列表
    this.loadOrders(true)

    // 如果是从徽章点击进入的，标记为已读
    if (this.data.fromBadgeStatus) {
      this.markAsRead()
    }
  },

  // 标记当前状态为已读
  markAsRead() {
    const fromStatus = this.data.fromBadgeStatus
    if (!fromStatus) return

    console.log('标记订单状态为已读:', fromStatus)

    // 获取当前状态的订单数量
    app.request({
      url: `/order/stats/${app.globalData.userId}`,
      method: 'GET'
    }).then(res => {
      const stats = res.data || {}
      const currentCount = stats[fromStatus] || 0

      console.log('当前订单数量:', currentCount, '状态:', fromStatus)

      // 更新已读状态
      app.updateOrderBadgeReadStatus(fromStatus, currentCount)

      // 清除来源状态标记，避免重复标记
      this.setData({
        fromBadgeStatus: null
      })
    }).catch(err => {
      console.error('获取订单统计失败', err)
    })
  },

  onPullDownRefresh() {
    this.loadOrders(true)
    wx.stopPullDownRefresh()
  },

  onReachBottom() {
    if (this.data.hasMore && !this.data.isLoading) {
      this.loadOrders(false)
    }
  },

  // 切换标签
  switchTab(e) {
    const index = e.currentTarget.dataset.index
    if (index !== this.data.currentTab) {
      this.setData({
        currentTab: index
      })
      this.loadOrders(true)
    }
  },

  // 加载订单列表
  loadOrders(refresh = false) {
    if (this.data.isLoading) return

    if (refresh) {
      this.setData({
        currentPage: 1,
        orders: [],
        hasMore: true,
        isEmpty: false
      })
    }

    this.setData({
      isLoading: true
    })

    const currentTab = this.data.tabs[this.data.currentTab]
    const params = {
      current: this.data.currentPage,
      size: this.data.pageSize
    }

    if (currentTab.status !== null) {
      params.status = currentTab.status
    }

    app.request({
      url: `/order/list/${app.globalData.userId}`,
      method: 'GET',
      data: params
    }).then(res => {
      const newOrders = res.data.records || []
      const orders = refresh ? newOrders : [...this.data.orders, ...newOrders]

      this.setData({
        orders: orders,
        currentPage: this.data.currentPage + 1,
        hasMore: newOrders.length === this.data.pageSize,
        isLoading: false,
        isEmpty: orders.length === 0
      })
    }).catch(err => {
      console.error('加载订单列表失败', err)
      this.setData({
        isLoading: false
      })
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      })
    })
  },

  // 查看订单详情
  viewOrderDetail(e) {
    const orderId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/order-detail/order-detail?id=${orderId}`
    })
  },

  // 取消订单
  cancelOrder(e) {
    const orderId = e.currentTarget.dataset.id
    wx.showModal({
      title: '确认取消',
      content: '确定要取消这个订单吗？',
      success: (res) => {
        if (res.confirm) {
          wx.showLoading({
            title: '取消中...'
          })

          app.request({
            url: '/order/cancel',
            method: 'PUT',
            data: {
              orderId: orderId,
              userId: app.globalData.userId
            }
          }).then(res => {
            wx.hideLoading()
            wx.showToast({
              title: '订单已取消',
              icon: 'success'
            })
            this.loadOrders(true)
          }).catch(err => {
            wx.hideLoading()
            console.error('取消订单失败', err)
            wx.showToast({
              title: '取消失败',
              icon: 'none'
            })
          })
        }
      }
    })
  },

  // 货到付款
  payOnDelivery(e) {
    const orderId = e.currentTarget.dataset.id

    wx.showModal({
      title: '货到付款',
      content: '确认已收到商品并完成付款？',
      success: (res) => {
        if (res.confirm) {
          wx.showLoading({
            title: '付款中...'
          })

          // 更新订单状态为待支付（表示已付款）
          app.request({
            url: '/admin/orders/' + orderId + '/status',
            method: 'PUT',
            data: {
              status: 1 // 待支付状态（实际表示已付款）
            }
          }).then(res => {
            wx.hideLoading()
            wx.showToast({
              title: '付款成功',
              icon: 'success'
            })
            this.loadOrders(true)
          }).catch(err => {
            wx.hideLoading()
            wx.showToast({
              title: err.message || '付款失败',
              icon: 'none'
            })
          })
        }
      }
    })
  },

  // 确认收货
  confirmReceive(e) {
    const orderId = e.currentTarget.dataset.id
    wx.showModal({
      title: '确认收货',
      content: '确认已收到商品吗？',
      success: (res) => {
        if (res.confirm) {
          wx.showLoading({
            title: '确认中...'
          })

          app.request({
            url: '/order/confirm-receipt',
            method: 'PUT',
            data: {
              orderId: orderId,
              userId: app.globalData.userId
            }
          }).then(res => {
            wx.hideLoading()
            wx.showToast({
              title: '确认收货成功',
              icon: 'success'
            })
            this.loadOrders(true)
          }).catch(err => {
            wx.hideLoading()
            console.error('确认收货失败', err)
            wx.showToast({
              title: '操作失败',
              icon: 'none'
            })
          })
        }
      }
    })
  },

  // 去评价
  goToReview(e) {
    const orderId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/review/review?orderId=${orderId}`
    })
  },

  // 再次购买
  buyAgain(e) {
    const orderId = e.currentTarget.dataset.id
    // 先获取订单商品信息
    app.request({
      url: `/order/items/${orderId}`,
      method: 'GET'
    }).then(res => {
      const orderItems = res.data || []
      const orderData = {
        type: 'cart',
        items: orderItems.map(item => ({
          flowerId: item.flowerId,
          quantity: item.quantity,
          flower: {
            id: item.flowerId,
            name: item.flowerName,
            price: item.price,
            mainImage: item.flowerImage
          }
        }))
      }

      wx.navigateTo({
        url: `/pages/order-confirm/order-confirm?data=${encodeURIComponent(JSON.stringify(orderData))}`
      })
    }).catch(err => {
      console.error('获取订单商品失败', err)
      wx.showToast({
        title: '操作失败',
        icon: 'none'
      })
    })
  },

  // 联系客服
  contactService() {
    wx.showModal({
      title: '联系客服',
      content: '客服电话：400-123-4567\n工作时间：9:00-18:00',
      showCancel: false,
      confirmText: '知道了'
    })
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 空函数，用于阻止事件冒泡
  }
})