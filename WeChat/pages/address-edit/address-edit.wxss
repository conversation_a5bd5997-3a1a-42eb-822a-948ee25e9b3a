/* pages/address-edit/address-edit.wxss */

/* 页面容器 */
.page-container {
  min-height: 100vh;
  background: linear-gradient(180deg, #fef7f0 0%, #f8f8f8 100%);
  display: flex;
  flex-direction: column;
}

/* 页面头部 */
.page-header {
  background: white;
  padding: 25rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  text-align: center;
}

/* 表单容器 */
.form-container {
  flex: 1;
  padding: 20rpx;
  padding-bottom: 200rpx; /* 进一步增加底部空间，确保内容完全可见 */
  max-height: calc(100vh - 120rpx); /* 限制最大高度，减去头部高度 */
}

.address-form {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

/* 表单区块 */
.form-section {
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.section-header {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 25rpx 30rpx 20rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.section-icon {
  font-size: 24rpx;
}

.section-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
}

/* 表单项 */
.form-item {
  padding: 25rpx 30rpx;
  border-bottom: 1rpx solid #f8f8f8;
}

.form-item:last-child {
  border-bottom: none;
}

.input-container {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.input-label {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.label-text {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.required {
  color: #ff3b30;
  font-size: 24rpx;
  font-weight: bold;
}

.optional {
  color: #999;
  font-size: 22rpx;
}

/* 输入框样式 */
.form-input {
  width: 100%;
  padding: 20rpx 24rpx;
  font-size: 28rpx;
  color: #333;
  background: #f8f8f8;
  border: 2rpx solid #f0f0f0;
  border-radius: 12rpx;
  transition: all 0.2s ease;
}

.form-input:focus {
  background: white;
  border-color: #ff6b9d;
  box-shadow: 0 0 0 4rpx rgba(255, 107, 157, 0.1);
}

.form-input.error {
  border-color: #ff3b30;
  background: rgba(255, 59, 48, 0.05);
}

.input-placeholder {
  color: #999;
  font-size: 26rpx;
}

/* 文本域样式 */
.form-textarea {
  width: 100%;
  min-height: 120rpx;
  padding: 20rpx 24rpx;
  font-size: 28rpx;
  color: #333;
  background: #f8f8f8;
  border: 2rpx solid #f0f0f0;
  border-radius: 12rpx;
  resize: none;
  transition: all 0.2s ease;
  line-height: 1.5;
}

.form-textarea:focus {
  background: white;
  border-color: #ff6b9d;
  box-shadow: 0 0 0 4rpx rgba(255, 107, 157, 0.1);
}

.form-textarea.error {
  border-color: #ff3b30;
  background: rgba(255, 59, 48, 0.05);
}

.char-count {
  text-align: right;
  font-size: 22rpx;
  color: #999;
  margin-top: 8rpx;
}

/* 错误提示 */
.error-message {
  display: flex;
  align-items: center;
  gap: 8rpx;
  margin-top: 12rpx;
}

.error-icon {
  font-size: 18rpx;
}

.error-text {
  color: #ff3b30;
  font-size: 22rpx;
  line-height: 1.4;
}

/* 地区选择器 */
.region-selector {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 24rpx;
  background: #f8f8f8;
  border: 2rpx solid #f0f0f0;
  border-radius: 12rpx;
  transition: all 0.2s ease;
}

.region-selector:active {
  background: white;
  border-color: #ff6b9d;
  box-shadow: 0 0 0 4rpx rgba(255, 107, 157, 0.1);
}

.region-selector.error {
  border-color: #ff3b30;
  background: rgba(255, 59, 48, 0.05);
}

.region-content {
  flex: 1;
}

.region-text {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.region-placeholder {
  font-size: 26rpx;
  color: #999;
}

.region-arrow {
  font-size: 24rpx;
  color: #999;
  font-weight: bold;
  margin-left: 12rpx;
}

/* 默认地址设置 */
.default-setting {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8rpx 0;
}

.default-info {
  flex: 1;
}

.default-label {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 6rpx;
}

.default-desc {
  font-size: 22rpx;
  color: #999;
  line-height: 1.4;
}

.switch-container {
  margin-left: 20rpx;
}

/* 提交按钮 */
.submit-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx 30rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
  background: white;
  border-top: 1rpx solid #f0f0f0;
  z-index: 100;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.submit-btn {
  width: 100%;
  background: linear-gradient(135deg, #ff6b9d, #ff8fab);
  color: white;
  border: none;
  border-radius: 25rpx;
  padding: 20rpx;
  font-size: 28rpx;
  font-weight: 500;
  box-shadow: 0 8rpx 20rpx rgba(255, 107, 157, 0.3);
  transition: all 0.2s ease;
}

.submit-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 157, 0.3);
}

.submit-btn.loading {
  opacity: 0.7;
  transform: none;
}

.btn-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
}

.loading-icon {
  font-size: 24rpx;
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.btn-text {
  color: white;
  font-size: 28rpx;
  font-weight: 500;
}

/* 地区选择器弹窗 */
.picker-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.picker-container {
  background: white;
  border-radius: 20rpx 20rpx 0 0;
  animation: slideUp 0.3s ease;
}

@keyframes slideUp {
  from { transform: translateY(100%); }
  to { transform: translateY(0); }
}

.picker-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 25rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.picker-cancel,
.picker-confirm {
  font-size: 28rpx;
  font-weight: 500;
  padding: 12rpx 20rpx;
  border-radius: 20rpx;
  transition: all 0.2s ease;
}

.picker-cancel {
  color: #666;
  background: #f5f5f5;
}

.picker-cancel:active {
  background: #e0e0e0;
}

.picker-confirm {
  color: white;
  background: #ff6b9d;
}

.picker-confirm:active {
  background: #e55a8a;
}

.picker-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
}

.region-picker {
  height: 400rpx;
  background: white;
}

.picker-item {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80rpx;
  font-size: 28rpx;
  color: #333;
}
