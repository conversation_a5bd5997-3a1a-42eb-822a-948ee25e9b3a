// pages/address-edit/address-edit.js
const app = getApp()

Page({
  data: {
    isEdit: false,
    addressId: null,
    formData: {
      recipientName: '',
      recipientPhone: '',
      province: '',
      city: '',
      district: '',
      detailedAddress: '',
      isDefault: 0
    },
    errors: {},
    isSubmitting: false,
    showRegionPicker: false,
    regionArray: [[], [], []],
    regionIndex: [0, 0, 0],
    regionData: {
      provinces: [],
      cities: {},
      districts: {}
    }
  },

  onLoad(options) {
    if (options.id) {
      this.setData({
        isEdit: true,
        addressId: options.id
      })
      wx.setNavigationBarTitle({
        title: '编辑地址'
      })
    } else {
      wx.setNavigationBarTitle({
        title: '添加地址'
      })
    }
    // 先初始化地区数据，然后再加载地址详情
    this.initRegionData().then(() => {
      if (options.id) {
        this.loadAddressDetail(options.id)
      }
    })
  },

  // 初始化地区数据（从数据库API获取完整的中国行政区划数据）
  initRegionData() {
    wx.showLoading({
      title: '加载地区数据...'
    })

    // 从后端API获取完整的省市区数据
    return app.request({
      url: '/flower/region/data',
      method: 'GET'
    }).then(res => {
      const regionData = res.data
      this.setRegionData(regionData.provinces, regionData.cities, regionData.districts)
      console.log('地区数据加载成功:', {
        provinces: regionData.provinces.length,
        cities: Object.keys(regionData.cities).length,
        districts: Object.keys(regionData.districts).length
      })
      return Promise.resolve()
    }).catch(err => {
      console.error('加载地区数据失败:', err)
      wx.showToast({
        title: '地区数据加载失败',
        icon: 'none'
      })
      // 使用备用数据
      this.loadBackupRegionData()
      return Promise.resolve()
    }).finally(() => {
      wx.hideLoading()
    })
  },

  // 处理API返回的地区数据
  processRegionData(chinaData) {
    const provinces = []
    const cities = {}
    const districts = {}

    // 处理省级数据
    chinaData.districts.forEach(province => {
      const provinceName = province.name
      provinces.push(provinceName)
      cities[provinceName] = []

      // 处理市级数据
      if (province.districts && province.districts.length > 0) {
        province.districts.forEach(city => {
          const cityName = city.name
          cities[provinceName].push(cityName)
          districts[cityName] = []

          // 处理区县级数据
          if (city.districts && city.districts.length > 0) {
            city.districts.forEach(district => {
              districts[cityName].push(district.name)
            })
          }
        })
      }
    })

    // 设置数据
    this.setRegionData(provinces, cities, districts)
  },

  // 备用地区数据（当API不可用时使用）
  loadBackupRegionData() {
    console.log('使用备用地区数据')
    // 使用最小化地区数据作为备用方案
    this.loadMinimalRegionData()
  },

  // 处理备用数据
  processBackupData(areas) {
    const provinces = []
    const cities = {}
    const districts = {}

    // 处理省级数据（code以0000结尾）
    const provinceList = areas.filter(item => item.code.endsWith('0000') && item.code !== '000000')

    provinceList.forEach(province => {
      const provinceName = province.name
      provinces.push(provinceName)
      cities[provinceName] = []

      // 查找该省的城市（前两位相同，后四位以00结尾但不全为0）
      const provinceCode = province.code.substring(0, 2)
      const cityList = areas.filter(item =>
        item.code.startsWith(provinceCode) &&
        item.code.endsWith('00') &&
        !item.code.endsWith('0000')
      )

      cityList.forEach(city => {
        const cityName = city.name
        cities[provinceName].push(cityName)
        districts[cityName] = []

        // 查找该市的区县（前四位相同，后两位不为00）
        const cityCode = city.code.substring(0, 4)
        const districtList = areas.filter(item =>
          item.code.startsWith(cityCode) &&
          !item.code.endsWith('00')
        )

        districtList.forEach(district => {
          districts[cityName].push(district.name)
        })
      })
    })

    this.setRegionData(provinces, cities, districts)
  },

  // 最小化地区数据（最后的备用方案）
  loadMinimalRegionData() {
    const provinces = ['北京市', '天津市', '河北省', '山西省', '内蒙古自治区', '辽宁省', '吉林省', '黑龙江省', '上海市', '江苏省', '浙江省', '安徽省', '福建省', '江西省', '山东省', '河南省', '湖北省', '湖南省', '广东省', '广西壮族自治区', '海南省', '重庆市', '四川省', '贵州省', '云南省', '西藏自治区', '陕西省', '甘肃省', '青海省', '宁夏回族自治区', '新疆维吾尔自治区', '台湾省', '香港特别行政区', '澳门特别行政区']

    // 这里只是示例，实际应该包含完整的城市和区县数据
    const cities = {
      '北京市': ['北京市'],
      '上海市': ['上海市'],
      '广东省': ['广州市', '深圳市', '珠海市', '汕头市', '佛山市', '韶关市', '湛江市', '肇庆市', '江门市', '茂名市', '惠州市', '梅州市', '汕尾市', '河源市', '阳江市', '清远市', '东莞市', '中山市', '潮州市', '揭阳市', '云浮市']
      // ... 其他省份的城市数据
    }

    const districts = {
      '北京市': ['东城区', '西城区', '朝阳区', '丰台区', '石景山区', '海淀区', '门头沟区', '房山区', '通州区', '顺义区', '昌平区', '大兴区', '怀柔区', '平谷区', '密云区', '延庆区'],
      '上海市': ['黄浦区', '徐汇区', '长宁区', '静安区', '普陀区', '虹口区', '杨浦区', '闵行区', '宝山区', '嘉定区', '浦东新区', '金山区', '松江区', '青浦区', '奉贤区', '崇明区']
      // ... 其他城市的区县数据
    }

    this.setRegionData(provinces, cities, districts)
  },

  // 设置地区数据的通用方法
  setRegionData(provinces, cities, districts) {
    // 保存地区数据
    this.setData({
      regionData: { provinces, cities, districts }
    })

    // 如果是编辑模式，不设置默认值，等待loadAddressDetail设置真实数据
    if (this.data.isEdit) {
      console.log('编辑模式，等待加载地址详情')
      return
    }

    // 新增模式才设置默认选中的省市区
    const defaultProvince = provinces[0] || ''
    const defaultCities = cities[defaultProvince] || []
    const defaultCity = defaultCities[0] || ''
    const defaultDistricts = districts[defaultCity] || []

    this.setData({
      regionArray: [provinces, defaultCities, defaultDistricts],
      regionIndex: [0, 0, 0],
      'formData.province': defaultProvince,
      'formData.city': defaultCity,
      'formData.district': defaultDistricts[0] || ''
    })

    console.log('地区数据加载完成:', {
      provinces: provinces.length,
      cities: Object.keys(cities).length,
      districts: Object.keys(districts).length
    })
  },

  // 加载地址详情
  loadAddressDetail(id) {
    app.request({
      url: `/address/detail/${id}`,
      method: 'GET'
    }).then(res => {
      const address = res.data
      console.log('原始地址数据:', address)

      // 清理详细地址，移除可能包含的省市区信息
      let cleanDetailedAddress = app.cleanDetailedAddress(
        address.detailedAddress || '',
        address.province || '',
        address.city || '',
        address.district || ''
      )

      this.setData({
        formData: {
          recipientName: address.recipientName || '',
          recipientPhone: address.recipientPhone || '',
          province: address.province || '',
          city: address.city || '',
          district: address.district || '',
          detailedAddress: cleanDetailedAddress,
          isDefault: address.isDefault || 0
        }
      })

      console.log('清理后的地址数据:', {
        province: address.province,
        city: address.city,
        district: address.district,
        detailedAddress: cleanDetailedAddress
      })

      // 更新地区选择器的索引
      this.updateRegionIndex(address.province, address.city, address.district)
    }).catch(err => {
      console.error('加载地址详情失败', err)
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      })
    })
  },



  // 更新地区选择器索引
  updateRegionIndex(province, city, district) {
    const { regionData } = this.data

    // 检查地区数据是否已加载
    if (!regionData || !regionData.provinces) {
      console.log('地区数据未加载，延迟更新')
      setTimeout(() => {
        this.updateRegionIndex(province, city, district)
      }, 500)
      return
    }

    // 找到省份索引
    const provinceIndex = regionData.provinces.indexOf(province)
    if (provinceIndex === -1) {
      console.log('未找到省份:', province)
      return
    }

    // 更新城市和区县数组
    const cities = regionData.cities[province] || []
    const districts = regionData.districts[city] || []

    // 找到城市和区县索引
    const cityIndex = cities.indexOf(city)
    const districtIndex = districts.indexOf(district)

    console.log('更新地区选择器:', {
      province, city, district,
      provinceIndex, cityIndex, districtIndex,
      citiesLength: cities.length,
      districtsLength: districts.length
    })

    this.setData({
      regionIndex: [
        provinceIndex >= 0 ? provinceIndex : 0,
        cityIndex >= 0 ? cityIndex : 0,
        districtIndex >= 0 ? districtIndex : 0
      ],
      regionArray: [
        regionData.provinces,
        cities,
        districts
      ]
    })
  },

  // 输入框变化处理
  onInputChange(e) {
    const { field } = e.currentTarget.dataset
    const { value } = e.detail
    
    this.setData({
      [`formData.${field}`]: value,
      [`errors.${field}`]: '' // 清除错误信息
    })
  },

  // 显示地区选择器
  showRegionPicker() {
    console.log('显示地区选择器，当前数据:', {
      regionArray: this.data.regionArray.map(arr => arr.length),
      regionData: {
        provinces: this.data.regionData.provinces.length,
        cities: Object.keys(this.data.regionData.cities).length,
        districts: Object.keys(this.data.regionData.districts).length
      }
    })

    this.setData({
      showRegionPicker: true
    })
  },

  // 地区选择变化
  onRegionChange(e) {
    const { value } = e.detail
    const { regionArray, regionData, regionIndex } = this.data

    console.log('地区选择变化:', { value, regionIndex })

    // 检查哪一列发生了变化
    let changedColumn = -1
    for (let i = 0; i < value.length; i++) {
      if (value[i] !== regionIndex[i]) {
        changedColumn = i
        break
      }
    }

    console.log('变化的列:', changedColumn)

    // 如果是省份变化，需要更新城市和区县数组
    if (changedColumn === 0) {
      const province = regionArray[0][value[0]]
      const cities = regionData.cities[province] || []
      const districts = regionData.districts[cities[0]] || []

      console.log('省份变化:', { province, cities: cities.length, districts: districts.length })

      const newRegionArray = [...regionArray]
      newRegionArray[1] = cities
      newRegionArray[2] = districts

      const newValue = [value[0], 0, 0] // 重置城市和区县选择

      this.setData({
        regionIndex: newValue,
        regionArray: newRegionArray,
        'formData.province': province,
        'formData.city': cities[0] || '',
        'formData.district': districts[0] || ''
      })
    }
    // 如果是城市变化，需要更新区县数组
    else if (changedColumn === 1) {
      const province = regionArray[0][value[0]]
      const city = regionArray[1][value[1]]
      const districts = regionData.districts[city] || []

      console.log('城市变化:', { city, districts: districts.length })

      const newRegionArray = [...regionArray]
      newRegionArray[2] = districts

      const newValue = [value[0], value[1], 0] // 重置区县选择

      this.setData({
        regionIndex: newValue,
        regionArray: newRegionArray,
        'formData.province': province,
        'formData.city': city,
        'formData.district': districts[0] || ''
      })
    }
    // 如果是区县变化，直接更新
    else {
      const province = regionArray[0][value[0]]
      const city = regionArray[1][value[1]]
      const district = regionArray[2][value[2]]

      console.log('区县变化:', { district })

      this.setData({
        regionIndex: value,
        'formData.province': province,
        'formData.city': city,
        'formData.district': district
      })
    }
  },

  // 确认地区选择
  onRegionConfirm() {
    this.setData({
      showRegionPicker: false,
      'errors.region': '' // 清除地区错误信息
    })
  },



  // 取消地区选择
  onRegionCancel() {
    this.setData({
      showRegionPicker: false
    })
  },

  // 切换默认地址
  toggleDefault() {
    this.setData({
      'formData.isDefault': this.data.formData.isDefault ? 0 : 1
    })
  },

  // 开关变化处理
  onSwitchChange(e) {
    const isDefault = e.detail.value ? 1 : 0
    this.setData({
      'formData.isDefault': isDefault
    })

    // 如果设置为默认地址，给用户提示
    if (isDefault === 1) {
      wx.showToast({
        title: '将设为默认地址',
        icon: 'none',
        duration: 1500
      })
    }
  },

  // 表单验证
  validateForm() {
    const { formData } = this.data
    const errors = {}

    if (!formData.recipientName.trim()) {
      errors.recipientName = '请输入收货人姓名'
    }

    if (!formData.recipientPhone.trim()) {
      errors.recipientPhone = '请输入手机号码'
    } else if (!/^1[3-9]\d{9}$/.test(formData.recipientPhone)) {
      errors.recipientPhone = '请输入正确的手机号码'
    }

    if (!formData.province) {
      errors.region = '请选择所在地区'
    }

    if (!formData.detailedAddress.trim()) {
      errors.detailedAddress = '请输入详细地址'
    }

    this.setData({ errors })
    return Object.keys(errors).length === 0
  },

  // 提交表单
  submitForm() {
    if (!this.validateForm()) {
      wx.showToast({
        title: '请完善地址信息',
        icon: 'none'
      })
      return
    }

    if (this.data.isSubmitting) return

    this.setData({
      isSubmitting: true
    })

    const { formData, isEdit, addressId } = this.data
    const requestData = {
      ...formData,
      userId: app.globalData.userId
    }

    if (isEdit) {
      requestData.id = addressId
    }

    const url = isEdit ? '/address/update' : '/address/add'
    const method = isEdit ? 'PUT' : 'POST'

    app.request({
      url,
      method,
      data: requestData
    }).then(res => {
      wx.showToast({
        title: isEdit ? '更新成功' : '添加成功',
        icon: 'success'
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
    }).catch(err => {
      console.error('保存地址失败', err)
      wx.showToast({
        title: '保存失败',
        icon: 'none'
      })
    }).finally(() => {
      this.setData({
        isSubmitting: false
      })
    })
  }
})
