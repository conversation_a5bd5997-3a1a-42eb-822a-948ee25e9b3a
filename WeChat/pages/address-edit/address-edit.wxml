<!-- pages/address-edit/address-edit.wxml -->
<view class="page-container">
  <!-- 页面头部 -->
  <view class="page-header">
    <text class="header-title">{{isEdit ? '编辑地址' : '添加地址'}}</text>
  </view>

  <scroll-view class="form-container" scroll-y="true">
    <form class="address-form">
      <!-- 收货人信息 -->
      <view class="form-section">
        <view class="section-header">
          <view class="section-icon">👤</view>
          <text class="section-title">收货人信息</text>
        </view>

        <view class="form-item">
          <view class="input-container">
            <view class="input-label">
              <text class="label-text">收货人</text>
              <text class="required">*</text>
            </view>
            <input class="form-input {{errors.recipientName ? 'error' : ''}}"
                   placeholder="请输入收货人姓名"
                   value="{{formData.recipientName}}"
                   data-field="recipientName"
                   bindinput="onInputChange"
                   placeholder-class="input-placeholder" />
            <view class="error-message" wx:if="{{errors.recipientName}}">
              <text class="error-icon">⚠️</text>
              <text class="error-text">{{errors.recipientName}}</text>
            </view>
          </view>
        </view>

        <view class="form-item">
          <view class="input-container">
            <view class="input-label">
              <text class="label-text">手机号码</text>
              <text class="required">*</text>
            </view>
            <input class="form-input {{errors.recipientPhone ? 'error' : ''}}"
                   placeholder="请输入11位手机号码"
                   type="number"
                   maxlength="11"
                   value="{{formData.recipientPhone}}"
                   data-field="recipientPhone"
                   bindinput="onInputChange"
                   placeholder-class="input-placeholder" />
            <view class="error-message" wx:if="{{errors.recipientPhone}}">
              <text class="error-icon">⚠️</text>
              <text class="error-text">{{errors.recipientPhone}}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 收货地址 -->
      <view class="form-section">
        <view class="section-header">
          <view class="section-icon">📍</view>
          <text class="section-title">收货地址</text>
        </view>

        <view class="form-item">
          <view class="input-container">
            <view class="input-label">
              <text class="label-text">所在地区</text>
              <text class="required">*</text>
            </view>
            <view class="region-selector {{errors.region ? 'error' : ''}}" bindtap="showRegionPicker">
              <view class="region-content">
                <text class="region-text" wx:if="{{formData.province && formData.city && formData.district}}">
                  {{formData.province}} {{formData.city}} {{formData.district}}
                </text>
                <text class="region-placeholder" wx:else>请选择省市区</text>
              </view>
              <text class="region-arrow">›</text>
            </view>
            <view class="error-message" wx:if="{{errors.region}}">
              <text class="error-icon">⚠️</text>
              <text class="error-text">{{errors.region}}</text>
            </view>
          </view>
        </view>

        <view class="form-item">
          <view class="input-container">
            <view class="input-label">
              <text class="label-text">详细地址</text>
              <text class="required">*</text>
            </view>
            <textarea class="form-textarea {{errors.detailedAddress ? 'error' : ''}}"
                      placeholder="请输入详细地址，如道路、门牌号、小区、楼栋号、单元室等"
                      value="{{formData.detailedAddress}}"
                      data-field="detailedAddress"
                      bindinput="onInputChange"
                      maxlength="200"
                      auto-height
                      placeholder-class="input-placeholder" />
            <view class="char-count">{{formData.detailedAddress.length}}/200</view>
            <view class="error-message" wx:if="{{errors.detailedAddress}}">
              <text class="error-icon">⚠️</text>
              <text class="error-text">{{errors.detailedAddress}}</text>
            </view>
          </view>
        </view>

      </view>

      <!-- 默认地址设置 -->
      <view class="form-section">
        <view class="section-header">
          <view class="section-icon">⭐</view>
          <text class="section-title">地址设置</text>
        </view>

        <view class="form-item">
          <view class="default-setting">
            <view class="default-info">
              <text class="default-label">设为默认收货地址</text>
              <text class="default-desc">默认地址将优先显示在下单时，方便快速选择</text>
            </view>
            <view class="switch-container">
              <switch checked="{{formData.isDefault}}"
                      color="#ff6b9d"
                      bindchange="onSwitchChange" />
            </view>
          </view>
        </view>
      </view>
    </form>
  </scroll-view>

  <!-- 提交按钮 -->
  <view class="submit-container">
    <button class="submit-btn {{isSubmitting ? 'loading' : ''}}"
            bindtap="submitForm"
            disabled="{{isSubmitting}}">
      <view class="btn-content">
        <text class="loading-icon" wx:if="{{isSubmitting}}">⏳</text>
        <text class="btn-text">{{isSubmitting ? '保存中...' : (isEdit ? '更新地址' : '保存地址')}}</text>
      </view>
    </button>
  </view>

  <!-- 地区选择器遮罩 -->
  <view class="picker-overlay" wx:if="{{showRegionPicker}}" bindtap="onRegionCancel">
    <view class="picker-container" catchtap="">
      <view class="picker-header">
        <text class="picker-cancel" bindtap="onRegionCancel">取消</text>
        <text class="picker-title">选择地区</text>
        <text class="picker-confirm" bindtap="onRegionConfirm">确定</text>
      </view>

      <picker-view class="region-picker"
                   value="{{regionIndex}}"
                   bindchange="onRegionChange">
        <picker-view-column>
          <view wx:for="{{regionArray[0]}}" wx:key="*this" class="picker-item">{{item}}</view>
        </picker-view-column>
        <picker-view-column>
          <view wx:for="{{regionArray[1]}}" wx:key="*this" class="picker-item">{{item}}</view>
        </picker-view-column>
        <picker-view-column>
          <view wx:for="{{regionArray[2]}}" wx:key="*this" class="picker-item">{{item}}</view>
        </picker-view-column>
      </picker-view>
    </view>
  </view>
</view>
