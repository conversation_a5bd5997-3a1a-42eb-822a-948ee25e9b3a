// pages/cart/cart.js
const app = getApp()

Page({
  data: {
    cartItems: [],
    selectAll: false,
    selectedItems: [],
    totalAmount: 0,
    showDeleteModal: false,
    deleteIndex: -1
  },

  onLoad() {
    this.loadCartItems()
  },

  onShow() {
    this.loadCartItems()
    // 清除购物车徽章，表示用户已查看
    app.clearCartBadge()
  },

  onPullDownRefresh() {
    this.loadCartItems()
    wx.stopPullDownRefresh()
  },

  // 加载购物车数据
  loadCartItems() {
    if (!app.globalData.userId) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
      return
    }

    wx.showLoading({
      title: '加载中...'
    })

    app.request({
      url: `/cart/list/${app.globalData.userId}`,
      method: 'GET'
    }).then(res => {
      const cartItems = res.data || []
      
      // 获取花卉详情
      const promises = cartItems.map(item => {
        return app.request({
          url: `/flower/detail/${item.flowerId}`,
          method: 'GET'
        }).then(flowerRes => {
          const flower = flowerRes.data
          flower.tagList = flower.tags ? flower.tags.split(',') : []
          return {
            ...item,
            flower: flower,
            selected: false
          }
        })
      })

      Promise.all(promises).then(items => {
        this.setData({
          cartItems: items
        })
        this.calculateTotal()
        wx.hideLoading()
      }).catch(err => {
        console.error('加载花卉详情失败', err)
        wx.hideLoading()
      })
    }).catch(err => {
      console.error('加载购物车失败', err)
      wx.hideLoading()
    })
  },

  // 切换全选
  toggleSelectAll() {
    const selectAll = !this.data.selectAll
    const cartItems = this.data.cartItems.map(item => ({
      ...item,
      selected: selectAll
    }))

    this.setData({
      selectAll: selectAll,
      cartItems: cartItems
    })
    this.calculateTotal()
  },

  // 切换单个商品选择
  toggleSelect(e) {
    const index = e.currentTarget.dataset.index
    const cartItems = [...this.data.cartItems]
    cartItems[index].selected = !cartItems[index].selected

    const selectAll = cartItems.every(item => item.selected)

    this.setData({
      cartItems: cartItems,
      selectAll: selectAll
    })
    this.calculateTotal()
  },

  // 计算总价
  calculateTotal() {
    const selectedItems = this.data.cartItems.filter(item => item.selected)
    const totalAmount = selectedItems.reduce((total, item) => {
      return total + (parseFloat(item.flower.price) * item.quantity)
    }, 0)

    this.setData({
      selectedItems: selectedItems,
      totalAmount: totalAmount.toFixed(2)
    })
  },

  // 减少数量
  decreaseQuantity(e) {
    const index = e.currentTarget.dataset.index
    const cartItems = [...this.data.cartItems]
    
    if (cartItems[index].quantity > 1) {
      cartItems[index].quantity--
      this.updateCartItem(cartItems[index], index)
    }
  },

  // 增加数量
  increaseQuantity(e) {
    const index = e.currentTarget.dataset.index
    const cartItems = [...this.data.cartItems]
    
    if (cartItems[index].quantity < cartItems[index].flower.stockQuantity) {
      cartItems[index].quantity++
      this.updateCartItem(cartItems[index], index)
    } else {
      wx.showToast({
        title: '库存不足',
        icon: 'none'
      })
    }
  },

  // 更新购物车商品
  updateCartItem(item, index) {
    app.request({
      url: '/cart/update',
      method: 'PUT',
      data: {
        userId: app.globalData.userId,
        flowerId: item.flowerId,
        quantity: item.quantity
      }
    }).then(res => {
      const cartItems = [...this.data.cartItems]
      cartItems[index] = item
      this.setData({
        cartItems: cartItems
      })
      this.calculateTotal()
      // 购物车内的数量更新不需要显示徽章
      app.updateCartCount(false)
    }).catch(err => {
      console.error('更新购物车失败', err)
    })
  },

  // 删除商品
  deleteItem(e) {
    const index = e.currentTarget.dataset.index
    this.setData({
      deleteIndex: index,
      showDeleteModal: true
    })
  },

  // 确认删除
  confirmDelete() {
    const index = this.data.deleteIndex
    const item = this.data.cartItems[index]

    app.request({
      url: '/cart/remove',
      method: 'DELETE',
      data: {
        userId: app.globalData.userId,
        flowerId: item.flowerId
      }
    }).then(res => {
      const cartItems = [...this.data.cartItems]
      cartItems.splice(index, 1)
      
      this.setData({
        cartItems: cartItems,
        showDeleteModal: false,
        deleteIndex: -1
      })
      
      this.calculateTotal()
      // 删除商品不需要显示徽章
      app.updateCartCount(false)
      
      wx.showToast({
        title: '删除成功',
        icon: 'success'
      })
    }).catch(err => {
      console.error('删除商品失败', err)
      this.hideDeleteModal()
    })
  },

  // 隐藏删除弹窗
  hideDeleteModal() {
    this.setData({
      showDeleteModal: false,
      deleteIndex: -1
    })
  },

  // 结算
  checkout() {
    if (this.data.selectedItems.length === 0) {
      wx.showToast({
        title: '请选择要下单的商品',
        icon: 'none'
      })
      return
    }

    // 跳转到订单确认页面
    const orderData = {
      type: 'cart', // 购物车下单
      items: this.data.selectedItems.map(item => ({
        flowerId: item.flowerId,
        quantity: item.quantity,
        flower: item.flower
      }))
    }

    wx.navigateTo({
      url: `/pages/order-confirm/order-confirm?data=${encodeURIComponent(JSON.stringify(orderData))}`
    })
  },

  // 跳转到详情页
  goToDetail(e) {
    const flowerId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/detail/detail?id=${flowerId}`
    })
  },

  // 去购物
  goShopping() {
    wx.switchTab({
      url: '/pages/index/index'
    })
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 空函数，用于阻止事件冒泡
  }
})
