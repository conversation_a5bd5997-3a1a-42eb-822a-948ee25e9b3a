<!-- pages/cart/cart.wxml -->
<view class="container">
  <!-- 购物车为空 -->
  <view class="empty-cart" wx:if="{{cartItems.length === 0}}">
    <view class="empty-icon">🛒</view>
    <text class="empty-text">购物车还是空的</text>
    <text class="empty-desc">快去挑选你喜欢的花卉吧</text>
    <button class="go-shopping-btn btn-primary" bindtap="goShopping">去逛逛</button>
  </view>

  <!-- 购物车列表 -->
  <view class="cart-list" wx:else>
    <!-- 全选 -->
    <view class="select-all card">
      <view class="select-item" bindtap="toggleSelectAll">
        <view class="checkbox {{selectAll ? 'checked' : ''}}">
          <text class="check-icon" wx:if="{{selectAll}}">✓</text>
        </view>
        <text class="select-text">全选</text>
      </view>
      <text class="total-count">共{{cartItems.length}}件商品</text>
    </view>

    <!-- 商品列表 -->
    <view class="cart-item card" wx:for="{{cartItems}}" wx:key="id">
      <view class="item-select" bindtap="toggleSelect" data-index="{{index}}">
        <view class="checkbox {{item.selected ? 'checked' : ''}}">
          <text class="check-icon" wx:if="{{item.selected}}">✓</text>
        </view>
      </view>
      
      <image class="item-image" src="{{item.flower.mainImage}}" mode="aspectFill" bindtap="goToDetail" data-id="{{item.flower.id}}" />
      
      <view class="item-info">
        <text class="item-name" bindtap="goToDetail" data-id="{{item.flower.id}}">{{item.flower.name}}</text>
        <text class="item-desc">{{item.flower.description}}</text>
        <view class="item-tags">
          <text class="tag" wx:for="{{item.flower.tagList}}" wx:key="index" wx:for-item="tag">{{tag}}</text>
        </view>
        
        <view class="item-bottom">
          <view class="item-price">
            <text class="price price-medium">¥{{item.flower.price}}</text>
            <text class="original-price" wx:if="{{item.flower.originalPrice}}">¥{{item.flower.originalPrice}}</text>
          </view>
          
          <view class="quantity-control">
            <view class="quantity-btn {{item.quantity <= 1 ? 'disabled' : ''}}" bindtap="decreaseQuantity" data-index="{{index}}">-</view>
            <text class="quantity-text">{{item.quantity}}</text>
            <view class="quantity-btn {{item.quantity >= item.flower.stockQuantity ? 'disabled' : ''}}" bindtap="increaseQuantity" data-index="{{index}}">+</view>
          </view>
        </view>
      </view>
      
      <view class="item-delete" bindtap="deleteItem" data-index="{{index}}">
        <text class="delete-icon">🗑️</text>
      </view>
    </view>
  </view>
</view>

<!-- 底部结算栏 -->
<view class="bottom-bar" wx:if="{{cartItems.length > 0}}">
  <view class="bar-left">
    <view class="select-item" bindtap="toggleSelectAll">
      <view class="checkbox {{selectAll ? 'checked' : ''}}">
        <text class="check-icon" wx:if="{{selectAll}}">✓</text>
      </view>
      <text class="select-text">全选</text>
    </view>
  </view>
  
  <view class="bar-right">
    <view class="total-info">
      <text class="total-label">合计:</text>
      <text class="total-price">¥{{totalAmount}}</text>
    </view>
    <button class="checkout-btn {{selectedItems.length === 0 ? 'disabled' : ''}}" bindtap="checkout">
      立即下单({{selectedItems.length}})
    </button>
  </view>
</view>

<!-- 删除确认弹窗 -->
<view class="delete-modal {{showDeleteModal ? 'show' : ''}}" bindtap="hideDeleteModal">
  <view class="modal-content" catchtap="stopPropagation">
    <view class="modal-title">确认删除</view>
    <view class="modal-desc">确定要删除这个商品吗？</view>
    <view class="modal-actions">
      <button class="modal-btn cancel-btn" bindtap="hideDeleteModal">取消</button>
      <button class="modal-btn confirm-btn" bindtap="confirmDelete">删除</button>
    </view>
  </view>
</view>
