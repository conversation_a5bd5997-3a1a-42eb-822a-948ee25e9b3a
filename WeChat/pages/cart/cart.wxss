/* pages/cart/cart.wxss */

.container {
  padding-bottom: 120rpx; /* 为底部结算栏留出空间 */
}

/* 空购物车 */
.empty-cart {
  text-align: center;
  padding: 200rpx 40rpx;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
  color: #ddd;
}

.empty-text {
  display: block;
  font-size: 32rpx;
  color: #666;
  margin-bottom: 15rpx;
}

.empty-desc {
  display: block;
  font-size: 26rpx;
  color: #999;
  margin-bottom: 50rpx;
}

.go-shopping-btn {
  width: 300rpx;
  margin: 0 auto;
}

/* 全选区域 */
.select-all {
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.select-item {
  display: flex;
  align-items: center;
}

.checkbox {
  width: 36rpx;
  height: 36rpx;
  border: 2rpx solid #ddd;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.checkbox.checked {
  background: #ff6b9d;
  border-color: #ff6b9d;
}

.check-icon {
  color: white;
  font-size: 20rpx;
  font-weight: bold;
}

.select-text {
  font-size: 28rpx;
  color: #333;
}

.total-count {
  font-size: 24rpx;
  color: #999;
}

/* 购物车商品 */
.cart-item {
  padding: 30rpx;
  margin-bottom: 20rpx;
  display: flex;
  align-items: flex-start;
  gap: 20rpx;
}

.item-select {
  padding-top: 10rpx;
}

.item-image {
  width: 160rpx;
  height: 160rpx;
  border-radius: 12rpx;
  flex-shrink: 0;
}

.item-info {
  flex: 1;
  min-width: 0;
}

.item-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  display: block;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.item-desc {
  font-size: 22rpx;
  color: #999;
  display: block;
  margin-bottom: 12rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.item-tags {
  margin-bottom: 20rpx;
}

.tag {
  display: inline-block;
  padding: 4rpx 12rpx;
  background: rgba(255, 107, 157, 0.1);
  color: #ff6b9d;
  border-radius: 12rpx;
  font-size: 18rpx;
  margin-right: 8rpx;
}

.item-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.item-price {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.quantity-control {
  display: flex;
  align-items: center;
  gap: 15rpx;
}

.quantity-btn {
  width: 50rpx;
  height: 50rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #333;
  background: white;
}

.quantity-btn.disabled {
  color: #ccc;
  border-color: #f0f0f0;
}

.quantity-text {
  font-size: 26rpx;
  color: #333;
  min-width: 40rpx;
  text-align: center;
}

.item-delete {
  padding-top: 10rpx;
}

.delete-icon {
  font-size: 32rpx;
  color: #999;
}

/* 底部结算栏 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 -2rpx 12rpx rgba(0, 0, 0, 0.1);
  z-index: 100;
}

.bar-left {
  display: flex;
  align-items: center;
}

.bar-right {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.total-info {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.total-label {
  font-size: 26rpx;
  color: #333;
}

.total-price {
  font-size: 32rpx;
  color: #ff6b9d;
  font-weight: 600;
}

.checkout-btn {
  background: linear-gradient(135deg, #ff6b9d, #ff8fab);
  color: white;
  border: none;
  border-radius: 50rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
  font-weight: 500;
}

.checkout-btn.disabled {
  background: #f0f0f0;
  color: #ccc;
}

/* 删除确认弹窗 */
.delete-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.delete-modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin: 40rpx;
  max-width: 500rpx;
  width: 100%;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  text-align: center;
  margin-bottom: 20rpx;
}

.modal-desc {
  font-size: 26rpx;
  color: #666;
  text-align: center;
  margin-bottom: 40rpx;
}

.modal-actions {
  display: flex;
  gap: 20rpx;
}

.modal-btn {
  flex: 1;
  height: 70rpx;
  border-radius: 35rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  border: none;
}

.cancel-btn {
  background: #f0f0f0;
  color: #666;
}

.confirm-btn {
  background: linear-gradient(135deg, #ff6b9d, #ff8fab);
  color: white;
}
