/* pages/login/login.wxss */

.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #ff6b9d, #ff8fab);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}

.login-content {
  background: white;
  border-radius: 30rpx;
  padding: 60rpx 40rpx;
  width: 100%;
  max-width: 600rpx;
  text-align: center;
  box-shadow: 0 20rpx 40rpx rgba(0, 0, 0, 0.1);
}

/* Logo区域 */
.logo-section {
  margin-bottom: 60rpx;
}

.logo-icon {
  font-size: 120rpx;
  margin-bottom: 20rpx;
}

.app-name {
  display: block;
  font-size: 48rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 15rpx;
}

.app-desc {
  display: block;
  font-size: 26rpx;
  color: #999;
}

/* 登录信息 */
.login-info {
  margin-bottom: 50rpx;
}

.info-title {
  display: block;
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 15rpx;
}

.info-desc {
  display: block;
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
}

/* 用户信息填写区域 */
.user-info-section {
  margin-bottom: 40rpx;
  padding: 32rpx 28rpx;
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.85));
  border-radius: 24rpx;
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12),
              0 2rpx 8rpx rgba(0, 0, 0, 0.08),
              inset 0 1rpx 0 rgba(255, 255, 255, 0.5);
}

.info-header {
  text-align: center;
  margin-bottom: 32rpx;
}

.header-icon {
  font-size: 48rpx;
  margin-bottom: 12rpx;
  opacity: 0.8;
}

.info-title {
  font-size: 32rpx;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 8rpx;
  display: block;
  letter-spacing: 1rpx;
}

.info-desc {
  font-size: 24rpx;
  color: #7f8c8d;
  display: block;
  font-weight: 400;
}

.info-form {
  display: flex;
  flex-direction: column;
  gap: 28rpx;
}

/* 头像选择区域 */
.avatar-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
}

.avatar-selector {
  background: transparent;
  border: none;
  padding: 0;
}

.avatar-selector::after {
  border: none;
}

.avatar-container {
  position: relative;
  width: 120rpx;
  height: 120rpx;
}

.avatar-preview {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.8);
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}

.avatar-overlay {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 36rpx;
  height: 36rpx;
  background: linear-gradient(135deg, #ff6b9d, #ff8fab);
  border-radius: 18rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 3rpx solid white;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 157, 0.4);
}

.camera-icon {
  font-size: 18rpx;
}

.avatar-selector:active .avatar-preview {
  transform: scale(0.95);
}

.avatar-label {
  font-size: 24rpx;
  color: #7f8c8d;
  font-weight: 500;
}

/* 昵称输入区域 */
.nickname-section {
  width: 100%;
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 16rpx;
  padding: 0 20rpx;
  height: 80rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.input-wrapper:focus-within {
  border-color: #ff6b9d;
  background: rgba(255, 255, 255, 0.9);
  box-shadow: 0 4rpx 20rpx rgba(255, 107, 157, 0.2);
}

.input-icon {
  font-size: 28rpx;
  margin-right: 16rpx;
  opacity: 0.7;
}

.nickname-input {
  flex: 1;
  background: transparent;
  border: none;
  font-size: 28rpx;
  color: #2c3e50;
  height: 100%;
  font-weight: 500;
}

.input-border {
  position: absolute;
  bottom: 0;
  left: 20rpx;
  right: 20rpx;
  height: 2rpx;
  background: linear-gradient(90deg, #ff6b9d, #ff8fab);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.input-wrapper:focus-within .input-border {
  transform: scaleX(1);
}

/* 登录按钮 */
.login-btn {
  position: relative;
  width: 100%;
  height: 96rpx;
  background: linear-gradient(135deg, #ff6b9d 0%, #ff8fab 50%, #ffa8c5 100%);
  color: white;
  border: none;
  border-radius: 48rpx;
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 40rpx;
  box-shadow: 0 12rpx 32rpx rgba(255, 107, 157, 0.4),
              0 4rpx 16rpx rgba(255, 107, 157, 0.2);
  overflow: hidden;
  transition: all 0.3s ease;
}

.login-btn::after {
  border: none;
}

.btn-content {
  position: relative;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  width: 100%;
  height: 100%;
}

.btn-icon {
  font-size: 32rpx;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.btn-text {
  font-weight: 600;
  letter-spacing: 2rpx;
}

.btn-shine {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent);
  animation: shine 3s infinite;
}

@keyframes shine {
  0% { left: -100%; }
  50% { left: 100%; }
  100% { left: 100%; }
}

.login-btn:active:not(:disabled) {
  transform: scale(0.98);
  box-shadow: 0 8rpx 24rpx rgba(255, 107, 157, 0.3),
              0 2rpx 8rpx rgba(255, 107, 157, 0.2);
}

.login-btn:disabled {
  background: linear-gradient(135deg, #d1d5db, #e5e7eb);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  color: #9ca3af;
}

.login-btn:disabled .btn-icon {
  animation: none;
}

.login-btn:disabled .btn-shine {
  display: none;
}



.btn-icon {
  margin-right: 15rpx;
  font-size: 28rpx;
}



/* 服务条款 */
.terms-section {
  font-size: 22rpx;
  color: #999;
  line-height: 1.5;
}

.terms-link {
  color: #ff6b9d;
  text-decoration: underline;
}

/* 手机号授权弹框 - 完全模仿微信原生UI */
.phone-auth-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.phone-auth-content {
  width: 640rpx;
  background: #ffffff;
  border-radius: 24rpx;
  padding: 0 0 30rpx 0;
  text-align: center;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  overflow: hidden;
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(100rpx);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* 手机图标 */
.phone-icon {
  font-size: 96rpx;
  margin: 60rpx 0 40rpx 0;
  line-height: 1;
}

/* 标题 */
.auth-title {
  font-size: 34rpx;
  font-weight: 500;
  color: #000000;
  margin-bottom: 16rpx;
  line-height: 1.4;
  padding: 0 40rpx;
}

/* 描述文字 */
.auth-desc {
  font-size: 28rpx;
  color: #888888;
  margin-bottom: 20rpx;
  line-height: 1.4;
  padding: 0 40rpx;
}

/* 开发环境提示 */
.dev-notice {
  margin-bottom: 40rpx;
  padding: 0 40rpx;
}

.dev-text {
  font-size: 24rpx;
  color: #ff9500;
  background: #fff7e6;
  padding: 16rpx 20rpx;
  border-radius: 8rpx;
  border-left: 6rpx solid #ff9500;
  display: block;
  line-height: 1.4;
}

/* 授权说明区域 */
.auth-notice {
  margin-bottom: 60rpx;
  padding: 0 40rpx;
  text-align: left;
}

.notice-text {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.5;
  display: block;
  margin-bottom: 20rpx;
}

.notice-list {
  margin-left: 20rpx;
}

.notice-item {
  font-size: 26rpx;
  color: #888888;
  line-height: 1.8;
  display: block;
}

/* 授权手机号按钮 */
.auth-phone-btn {
  width: calc(100% - 80rpx);
  height: 88rpx;
  background: #007aff;
  color: #ffffff;
  border: none;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: 500;
  margin: 0 40rpx 20rpx 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
}

.auth-phone-btn::after {
  border: none;
}

.auth-phone-btn:active {
  background: #0056cc;
  transform: scale(0.98);
}

/* 拒绝授权按钮 */
.deny-phone-btn {
  width: calc(100% - 80rpx);
  height: 88rpx;
  background: #f5f5f5;
  color: #666666;
  border: none;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: 400;
  margin: 0 40rpx 0 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
}

.deny-phone-btn::after {
  border: none;
}

.deny-phone-btn:active {
  background: #e0e0e0;
  transform: scale(0.98);
}

.btn-text {
  font-size: 32rpx;
}


