<!-- pages/login/login.wxml -->
<view class="container">
  <view class="login-content">
    <!-- Logo区域 -->
    <view class="logo-section">
      <view class="logo-icon">🌸</view>
      <text class="app-name">花语小铺</text>
      <text class="app-desc">发现生活中的美好</text>
    </view>

    <!-- 登录说明 -->
    <view class="login-info">
      <text class="info-title">欢迎使用花语小铺</text>
      <text class="info-desc">点击下方按钮即可快速登录，开始您的购花之旅</text>
    </view>

    <!-- 用户信息填写区域 -->
    <view class="user-info-section">
      <view class="info-header">
        <view class="header-icon">👤</view>
        <text class="info-title">个人信息</text>
        <text class="info-desc">设置您的专属形象</text>
      </view>

      <view class="info-form">
        <!-- 头像选择 -->
        <view class="avatar-section">
          <button class="avatar-selector" open-type="chooseAvatar" bindchooseavatar="onChooseAvatar">
            <view class="avatar-container">
              <image class="avatar-preview" src="{{currentAvatarUrl}}" mode="aspectFill"></image>
              <view class="avatar-overlay">
                <text class="camera-icon">📷</text>
              </view>
            </view>
          </button>
          <text class="avatar-label">{{avatarLabel}}</text>
        </view>

        <!-- 昵称输入 -->
        <view class="nickname-section">
          <view class="input-wrapper">
            <text class="input-icon">✏️</text>
            <input class="nickname-input"
                   type="nickname"
                   placeholder="请输入您的昵称"
                   value="{{nickname}}"
                   bindinput="onNicknameInput"
                   maxlength="20" />
            <view class="input-border"></view>
          </view>
        </view>
      </view>
    </view>

    <!-- 登录按钮 -->
    <button class="login-btn"
            bindtap="doLogin"
            loading="{{isLoading}}"
            disabled="{{isLoading || (!nickname && !avatarUrl)}}">
      <view class="btn-content">
        <text class="btn-icon" wx:if="{{!isLoading}}">🌸</text>
        <text class="btn-text">{{isLoading ? '登录中...' : '立即登录'}}</text>
      </view>
      <view class="btn-shine" wx:if="{{!isLoading}}"></view>
    </button>

    <!-- 服务条款 -->
    <view class="terms-section">
      <text class="terms-text">登录即表示同意</text>
      <text class="terms-link">《用户服务协议》</text>
      <text class="terms-text">和</text>
      <text class="terms-link">《隐私政策》</text>
    </view>
  </view>

  <!-- 手机号授权弹框 - 完全模仿微信原生UI -->
  <view class="phone-auth-modal" wx:if="{{showPhoneAuth}}">
    <view class="phone-auth-content">
      <!-- 手机图标 -->
      <view class="phone-icon">📱</view>

      <!-- 标题 -->
      <view class="auth-title">申请获取并验证你的手机号</view>

      <!-- 描述 -->
      <view class="auth-desc">保证发布动态、评论等功能正常使用</view>

      <!-- 开发环境提示 -->
      <view class="dev-notice">
        <text class="dev-text">💡 开发环境提示：如遇权限问题将自动使用模拟数据</text>
      </view>

      <!-- 授权说明 -->
      <view class="auth-notice">
        <text class="notice-text">为了更好地为您提供服务，需要获取您的手机号用于：</text>
        <view class="notice-list">
          <text class="notice-item">• 订单状态通知</text>
          <text class="notice-item">• 物流配送信息</text>
          <text class="notice-item">• 重要活动推送</text>
        </view>
      </view>

      <!-- 授权按钮 - 使用微信原生授权 -->
      <button
        class="auth-phone-btn"
        open-type="getPhoneNumber"
        bindgetphonenumber="getPhoneNumber">
        <text class="btn-text">授权手机号</text>
      </button>

      <!-- 拒绝授权按钮 -->
      <button
        class="deny-phone-btn"
        bindtap="skipPhoneAuth">
        <text class="btn-text">暂不授权</text>
      </button>
    </view>
  </view>
</view>
