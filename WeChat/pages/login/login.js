// pages/login/login.js
const app = getApp()

Page({
  data: {
    isLoading: false,
    showPhoneAuth: false,
    avatarUrl: '',
    nickname: '',
    currentAvatarUrl: '/images/default-avatar.png',
    avatarLabel: '点击选择头像'
  },

  onLoad() {
    // 检查是否已经登录
    if (app.globalData.userInfo) {
      this.goBack()
    }
  },

  // 一键登录
  doLogin() {
    // 检查是否已填写基本信息
    const { nickname, avatarUrl } = this.data
    if (!nickname && !avatarUrl) {
      wx.showToast({
        title: '请先完善个人信息',
        icon: 'none'
      })
      return
    }

    this.setData({
      isLoading: true
    })

    // 如果用户填写了信息，先保存到临时变量
    if (nickname || avatarUrl) {
      const userInfo = {
        nickName: nickname || '微信用户',
        avatarUrl: avatarUrl || '/images/default-avatar.png',
        gender: 0,
        city: '',
        province: '',
        country: ''
      }
      app.globalData.tempUserInfo = userInfo
    }

    // 进行登录
    this.performLogin()
  },

  // 选择头像
  onChooseAvatar(e) {
    console.log('选择头像:', e.detail.avatarUrl)
    const newAvatarUrl = e.detail.avatarUrl
    this.setData({
      avatarUrl: newAvatarUrl,
      currentAvatarUrl: newAvatarUrl,
      avatarLabel: '点击更换头像'
    })

    wx.showToast({
      title: '头像选择成功',
      icon: 'success',
      duration: 1500
    })
  },

  // 输入昵称
  onNicknameInput(e) {
    console.log('输入昵称:', e.detail.value)
    this.setData({
      nickname: e.detail.value
    })
  },



  // 执行登录
  performLogin() {
    wx.login({
      success: (res) => {
        if (res.code) {
          console.log('获取到登录凭证:', res.code)
          // 先进行基础登录
          app.request({
            url: '/user/login',
            method: 'POST',
            data: {
              code: res.code
            }
          }).then(response => {
            console.log('基础登录成功:', response)
            const user = response.data

            // 保存基础用户信息
            app.globalData.userInfo = user
            app.globalData.openid = user.openid
            app.globalData.userId = user.id

            // 基础登录成功后，检查是否已获取用户信息
            if (app.globalData.tempUserInfo) {
              // 如果已经获取了用户信息，直接使用
              this.updateUserProfileWithAvatar(app.globalData.userId, app.globalData.tempUserInfo)
            } else {
              // 如果没有获取用户信息，调用获取方法
              this.getUserProfileForLogin()
            }
          }).catch(err => {
            console.error('登录失败', err)
            wx.showToast({
              title: '登录失败，请重试',
              icon: 'none'
            })
            this.setData({
              isLoading: false
            })
          })
        } else {
          wx.showToast({
            title: '获取登录凭证失败',
            icon: 'none'
          })
          this.setData({
            isLoading: false
          })
        }
      },
      fail: (err) => {
        console.error('wx.login失败', err)
        wx.showToast({
          title: '登录失败',
          icon: 'none'
        })
        this.setData({
          isLoading: false
        })
      }
    })
  },

  // 登录流程中获取用户信息授权
  getUserProfileForLogin() {
    wx.getUserProfile({
      desc: '用于完善用户资料',
      success: (res) => {
        console.log('登录流程中获取用户信息成功:', res)
        const userInfo = res.userInfo

        // 更新用户信息到服务器
        this.updateUserInfo(app.globalData.userId, userInfo)

        // 获取用户信息成功后，跳转到新的手机号授权页面
        wx.navigateTo({
          url: '/pages/phone-auth/phone-auth?from=login'
        })
        this.setData({
          isLoading: false
        })
      },
      fail: (err) => {
        console.log('用户拒绝授权用户信息:', err)
        // 用户拒绝授权，直接跳转到手机号授权页面
        wx.navigateTo({
          url: '/pages/phone-auth/phone-auth?from=login'
        })
        this.setData({
          isLoading: false
        })
      }
    })
  },

  // 获取手机号授权（通过button的bindgetphonenumber事件）
  getPhoneNumber(e) {
    console.log('获取手机号事件:', e)

    if (e.detail.errMsg === 'getPhoneNumber:ok') {
      console.log('用户同意授权手机号')

      // 显示加载状态
      wx.showLoading({
        title: '正在获取手机号...',
        mask: true
      })

      // 使用新版API的code参数发送到服务器
      app.request({
        url: '/user/phone',
        method: 'POST',
        data: {
          userId: app.globalData.userId,
          code: e.detail.code  // 新版API使用code参数
        }
      }).then(response => {
        wx.hideLoading()
        console.log('手机号获取响应:', response)

        if (response.code === 200 && response.data) {
          // 更新用户信息
          const phoneNumber = response.data.purePhoneNumber || response.data.phoneNumber
          const updatedUser = { ...app.globalData.userInfo, phone: phoneNumber }
          app.globalData.userInfo = updatedUser

          wx.showToast({
            title: '手机号授权成功',
            icon: 'success',
            duration: 2000
          })

          console.log('手机号获取成功:', phoneNumber)

          this.setData({
            showPhoneAuth: false
          })

          // 延迟一下再跳转，让用户看到成功提示
          setTimeout(() => {
            this.loginSuccess(updatedUser)
          }, 1500)
        } else {
          throw new Error(response.message || '手机号获取失败')
        }
      }).catch(err => {
        wx.hideLoading()
        console.error('手机号获取失败:', err)

        wx.showModal({
          title: '手机号获取失败',
          content: '获取手机号失败，您可以通过其他方式绑定手机号',
          showCancel: true,
          cancelText: '跳过',
          confirmText: '去绑定',
          success: (res) => {
            if (res.confirm) {
              // 跳转到手机号授权页面
              wx.navigateTo({
                url: '/pages/phone-auth/phone-auth?from=login'
              })
            } else {
              // 跳过手机号绑定，完成登录
              this.setData({
                showPhoneAuth: false
              })
              this.loginSuccess(app.globalData.userInfo)
            }
          }
        })
      })
    } else if (e.detail.errMsg === 'getPhoneNumber:fail user deny') {
      console.log('用户拒绝授权手机号')
      // 用户主动拒绝授权
      this.handlePhoneAuthDeny()
    } else if (e.detail.errMsg === 'getPhoneNumber:fail no permission') {
      console.log('小程序没有获取手机号权限')
      // 小程序没有权限，使用模拟授权流程
      this.handleNoPermission()
    } else {
      console.log('获取手机号失败:', e.detail.errMsg)
      // 其他错误情况
      wx.showModal({
        title: '获取手机号失败',
        content: `错误信息: ${e.detail.errMsg}`,
        showCancel: false,
        confirmText: '继续使用',
        success: () => {
          this.setData({
            showPhoneAuth: false
          })
          this.loginSuccess(app.globalData.userInfo)
        }
      })
    }
  },

  // 处理用户拒绝手机号授权
  handlePhoneAuthDeny() {
    wx.showModal({
      title: '提示',
      content: '您已拒绝手机号授权，部分功能可能受限。您可以稍后在个人中心重新授权。',
      showCancel: true,
      cancelText: '重新授权',
      confirmText: '继续使用',
      success: (res) => {
        if (res.confirm) {
          // 用户选择继续使用
          this.setData({
            showPhoneAuth: false
          })
          this.loginSuccess(app.globalData.userInfo)
        } else if (res.cancel) {
          // 用户选择重新授权，保持弹框显示
          console.log('用户选择重新授权')
        }
      }
    })
  },

  // 处理小程序无权限获取手机号的情况
  handleNoPermission() {
    console.log('小程序没有获取手机号权限，跳转到手机号授权页面')

    wx.showModal({
      title: '权限提示',
      content: '当前小程序暂无手机号快速获取权限，您可以通过手动输入的方式绑定手机号',
      showCancel: true,
      cancelText: '跳过',
      confirmText: '去绑定',
      success: (res) => {
        if (res.confirm) {
          // 跳转到手机号授权页面
          wx.navigateTo({
            url: '/pages/phone-auth/phone-auth?from=login'
          })
        } else {
          // 跳过授权
          this.setData({
            showPhoneAuth: false
          })
          this.loginSuccess(app.globalData.userInfo)
        }
      }
    })
  },

  // 模拟手机号授权
  simulatePhoneAuth() {
    console.log('开始模拟手机号授权')

    wx.showLoading({
      title: '正在获取手机号...',
      mask: true
    })

    // 模拟网络请求延迟
    setTimeout(() => {
      // 直接调用后端接口，不传加密数据（后端会返回模拟数据）
      app.request({
        url: '/user/phone',
        method: 'POST',
        data: {
          userId: app.globalData.userId,
          encryptedData: 'mock_encrypted_data',
          iv: 'mock_iv'
        }
      }).then(response => {
        wx.hideLoading()
        console.log('模拟手机号授权响应:', response)

        if (response.code === 200 && response.data) {
          const phoneNumber = response.data.purePhoneNumber || response.data.phoneNumber
          const updatedUser = { ...app.globalData.userInfo, phone: phoneNumber }
          app.globalData.userInfo = updatedUser

          wx.showToast({
            title: '手机号授权成功',
            icon: 'success',
            duration: 2000
          })

          console.log('模拟手机号获取成功:', phoneNumber)

          this.setData({
            showPhoneAuth: false
          })

          setTimeout(() => {
            this.loginSuccess(updatedUser)
          }, 1500)
        } else {
          throw new Error(response.message || '模拟授权失败')
        }
      }).catch(err => {
        wx.hideLoading()
        console.error('模拟手机号授权失败:', err)

        wx.showToast({
          title: '模拟授权失败',
          icon: 'none'
        })

        this.setData({
          showPhoneAuth: false
        })
        this.loginSuccess(app.globalData.userInfo)
      })
    }, 1500)
  },

  // 跳过手机号授权
  skipPhoneAuth() {
    console.log('用户选择跳过手机号授权')

    wx.showModal({
      title: '确认跳过',
      content: '跳过手机号授权后，您将无法接收订单通知和物流信息。确定要跳过吗？',
      showCancel: true,
      cancelText: '重新考虑',
      confirmText: '确定跳过',
      success: (res) => {
        if (res.confirm) {
          // 用户确认跳过
          this.setData({
            showPhoneAuth: false
          })

          wx.showToast({
            title: '已跳过手机号授权',
            icon: 'none',
            duration: 2000
          })

          setTimeout(() => {
            this.loginSuccess(app.globalData.userInfo)
          }, 1500)
        }
        // 如果用户取消，则保持弹框显示，不做任何操作
      }
    })
  },



  // 更新用户信息
  updateUserInfo(userId, userInfo) {
    console.log('更新用户信息:', userInfo)

    app.request({
      url: '/user/update',
      method: 'PUT',
      data: {
        id: userId,
        nickname: userInfo.nickName,
        avatarUrl: userInfo.avatarUrl,
        gender: userInfo.gender,
        city: userInfo.city,
        province: userInfo.province,
        country: userInfo.country
      }
    }).then(response => {
      console.log('用户信息更新成功:', response)
      // 更新全局用户信息
      app.globalData.userInfo = { ...app.globalData.userInfo, ...response.data }
      // 不在这里调用loginSuccess，继续后续的手机号授权流程
    }).catch(err => {
      console.error('更新用户信息失败', err)
      // 即使更新失败也继续流程
    })
  },

  // 更新用户信息并上传头像
  updateUserProfileWithAvatar(userId, userInfo) {
    console.log('更新用户信息并上传头像:', userInfo)

    // 将头像转换为Base64格式
    this.convertAvatarToBase64(userInfo.avatarUrl).then(avatarBase64 => {
      return app.request({
        url: '/user/update-profile',
        method: 'POST',
        data: {
          userId: userId,
          nickname: userInfo.nickName,
          avatarBase64: avatarBase64,
          phone: null // 登录时不更新手机号
        }
      })
    }).then(response => {
      console.log('用户信息和头像更新成功:', response)
      // 更新全局用户信息
      app.globalData.userInfo = { ...app.globalData.userInfo, ...response.data }

      this.setData({
        showPhoneAuth: true,
        isLoading: false
      })
    }).catch(err => {
      console.error('更新用户信息失败', err)
      // 即使更新失败也继续流程
      this.setData({
        showPhoneAuth: true,
        isLoading: false
      })
    })
  },

  // 将头像转换为Base64格式
  convertAvatarToBase64(avatarUrl) {
    return new Promise((resolve, reject) => {
      if (!avatarUrl) {
        resolve(null)
        return
      }

      // 如果是本地临时文件路径，需要转换为Base64
      if (avatarUrl.startsWith('http://tmp/') || avatarUrl.startsWith('wxfile://')) {
        wx.getFileSystemManager().readFile({
          filePath: avatarUrl,
          encoding: 'base64',
          success: (res) => {
            resolve('data:image/jpeg;base64,' + res.data)
          },
          fail: (err) => {
            console.error('读取头像文件失败:', err)
            resolve(null)
          }
        })
      } else {
        // 如果是网络图片，下载后转换
        wx.downloadFile({
          url: avatarUrl,
          success: (res) => {
            if (res.statusCode === 200) {
              wx.getFileSystemManager().readFile({
                filePath: res.tempFilePath,
                encoding: 'base64',
                success: (fileRes) => {
                  resolve('data:image/jpeg;base64,' + fileRes.data)
                },
                fail: (err) => {
                  console.error('读取下载的头像文件失败:', err)
                  resolve(null)
                }
              })
            } else {
              resolve(null)
            }
          },
          fail: (err) => {
            console.error('下载头像失败:', err)
            resolve(null)
          }
        })
      }
    })
  },

  // 登录成功
  loginSuccess(user) {
    console.log('登录成功，用户信息:', user)

    // 保存用户信息
    app.globalData.userInfo = user
    app.globalData.openid = user.openid
    app.globalData.userId = user.id

    wx.setStorageSync('userInfo', user)
    wx.setStorageSync('openid', user.openid)

    // 更新购物车数量
    app.updateCartCount()

    this.setData({
      isLoading: false
    })

    // 直接完成登录，不再检查用户信息
    wx.showToast({
      title: '登录成功',
      icon: 'success'
    })
    this.goBack()
  },



  // 返回上一页
  goBack() {
    setTimeout(() => {
      const pages = getCurrentPages()
      if (pages.length > 1) {
        wx.navigateBack()
      } else {
        wx.switchTab({
          url: '/pages/index/index'
        })
      }
    }, 1000)
  }
})
