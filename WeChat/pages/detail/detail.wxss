/* pages/detail/detail.wxss */

.container {
  padding-bottom: 120rpx; /* 为底部操作栏留出空间 */
}

/* 图片区域 */
.image-section {
  position: relative;
  margin-bottom: 20rpx;
}

.image-swiper {
  height: 600rpx;
}

.flower-image {
  width: 100%;
  height: 100%;
}

.favorite-btn {
  position: absolute;
  top: 30rpx;
  right: 30rpx;
  width: 80rpx;
  height: 80rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.favorite-btn.active {
  background: rgba(255, 107, 157, 0.1);
}

.favorite-icon {
  font-size: 36rpx;
}

/* 基本信息 */
.info-section {
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.flower-header {
  margin-bottom: 20rpx;
}

.flower-name {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 15rpx;
}

.flower-price {
  display: flex;
  align-items: center;
  gap: 15rpx;
}

.flower-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  display: block;
  margin-bottom: 20rpx;
}

.flower-tags {
  margin-bottom: 30rpx;
}

.flower-stats {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.stat-label {
  font-size: 26rpx;
  color: #999;
}

.stat-value {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

/* 详细信息区块 */
.meaning-section,
.occasion-section,
.care-section,
.quantity-section {
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.meaning-text,
.occasion-text,
.care-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
}

/* 数量选择 */
.quantity-selector {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.quantity-btn {
  width: 60rpx;
  height: 60rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #333;
  background: white;
}

.quantity-btn.disabled {
  color: #ccc;
  border-color: #f0f0f0;
}

.quantity-input {
  width: 120rpx;
  height: 60rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  text-align: center;
  font-size: 28rpx;
}

/* 底部操作栏 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 20rpx;
  display: flex;
  gap: 20rpx;
  box-shadow: 0 -2rpx 12rpx rgba(0, 0, 0, 0.1);
  z-index: 100;
}

.action-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: 500;
}

.cart-btn {
  background: rgba(255, 107, 157, 0.1);
  color: #ff6b9d;
  border: 2rpx solid #ff6b9d;
}

.buy-btn {
  background: linear-gradient(135deg, #ff6b9d, #ff8fab);
  color: white;
}

.btn-icon {
  margin-right: 10rpx;
  font-size: 24rpx;
}

/* 登录弹窗 */
.login-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.login-modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin: 40rpx;
  max-width: 500rpx;
  width: 100%;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  text-align: center;
  margin-bottom: 20rpx;
}

.modal-desc {
  font-size: 26rpx;
  color: #666;
  text-align: center;
  margin-bottom: 40rpx;
}

.modal-actions {
  display: flex;
  gap: 20rpx;
}

.modal-btn {
  flex: 1;
  height: 70rpx;
  border-radius: 35rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  border: none;
}

.cancel-btn {
  background: #f0f0f0;
  color: #666;
}

.confirm-btn {
  background: linear-gradient(135deg, #ff6b9d, #ff8fab);
  color: white;
}
