<!-- pages/detail/detail.wxml -->
<view class="container">
  <!-- 图片轮播 -->
  <view class="image-section">
    <swiper class="image-swiper" indicator-dots="true" indicator-color="rgba(255,255,255,0.5)" indicator-active-color="white">
      <swiper-item>
        <image class="flower-image" src="{{flower.mainImage}}" mode="aspectFill" />
      </swiper-item>
      <swiper-item wx:for="{{flower.imageList}}" wx:key="index">
        <image class="flower-image" src="{{item}}" mode="aspectFill" />
      </swiper-item>
    </swiper>
    
    <!-- 收藏按钮 -->
    <view class="favorite-btn {{isFavorite ? 'active' : ''}}" bindtap="toggleFavorite">
      <text class="favorite-icon">{{isFavorite ? '❤️' : '🤍'}}</text>
    </view>
  </view>

  <!-- 基本信息 -->
  <view class="info-section card">
    <view class="flower-header">
      <text class="flower-name">{{flower.name}}</text>
      <view class="flower-price">
        <text class="price price-large">¥{{flower.price}}</text>
        <text class="original-price" wx:if="{{flower.originalPrice}}">¥{{flower.originalPrice}}</text>
      </view>
    </view>
    
    <text class="flower-desc">{{flower.description}}</text>
    
    <view class="flower-tags">
      <text class="tag tag-primary" wx:for="{{flower.tagList}}" wx:key="index" wx:for-item="tag">{{tag}}</text>
    </view>
    
    <view class="flower-stats">
      <view class="stat-item">
        <text class="stat-label">销量</text>
        <text class="stat-value">{{flower.salesCount}}</text>
      </view>
      <view class="stat-item">
        <text class="stat-label">库存</text>
        <text class="stat-value">{{flower.stockQuantity}}</text>
      </view>
      <view class="stat-item">
        <text class="stat-label">颜色</text>
        <text class="stat-value">{{flower.color}}</text>
      </view>
      <view class="stat-item">
        <text class="stat-label">规格</text>
        <text class="stat-value">{{flower.size}}</text>
      </view>
    </view>
  </view>

  <!-- 花语寓意 -->
  <view class="meaning-section card" wx:if="{{flower.flowerLanguage}}">
    <view class="section-title">花语寓意</view>
    <text class="meaning-text">{{flower.flowerLanguage}}</text>
  </view>

  <!-- 适用场合 -->
  <view class="occasion-section card" wx:if="{{flower.occasion}}">
    <view class="section-title">适用场合</view>
    <text class="occasion-text">{{flower.occasion}}</text>
  </view>

  <!-- 养护说明 -->
  <view class="care-section card" wx:if="{{flower.careInstructions}}">
    <view class="section-title">养护说明</view>
    <text class="care-text">{{flower.careInstructions}}</text>
  </view>

  <!-- 数量选择 -->
  <view class="quantity-section card">
    <view class="section-title">购买数量</view>
    <view class="quantity-selector">
      <view class="quantity-btn {{quantity <= 1 ? 'disabled' : ''}}" bindtap="decreaseQuantity">-</view>
      <input class="quantity-input" type="number" value="{{quantity}}" bindinput="onQuantityInput" />
      <view class="quantity-btn {{quantity >= flower.stockQuantity ? 'disabled' : ''}}" bindtap="increaseQuantity">+</view>
    </view>
  </view>
</view>

<!-- 底部操作栏 -->
<view class="bottom-actions">
  <view class="action-btn cart-btn" bindtap="addToCart">
    <text class="btn-icon">🛒</text>
    <text class="btn-text">加入购物车</text>
  </view>
  <view class="action-btn buy-btn" bindtap="buyNow">
    <text class="btn-text">立即下单</text>
  </view>
</view>

<!-- 登录提示弹窗 -->
<view class="login-modal {{showLoginModal ? 'show' : ''}}" bindtap="hideLoginModal">
  <view class="modal-content" catchtap="stopPropagation">
    <view class="modal-title">需要登录</view>
    <view class="modal-desc">请先登录后再进行操作</view>
    <view class="modal-actions">
      <button class="modal-btn cancel-btn" bindtap="hideLoginModal">取消</button>
      <button class="modal-btn confirm-btn" bindtap="goToLogin">去登录</button>
    </view>
  </view>
</view>
