// pages/detail/detail.js
const app = getApp()

Page({
  data: {
    flower: {},
    quantity: 1,
    isFavorite: false,
    showLoginModal: false
  },

  onLoad(options) {
    const flowerId = options.id
    if (flowerId) {
      this.loadFlowerDetail(flowerId)
      this.checkFavoriteStatus(flowerId)
    }
  },

  onShow() {
    // 检查登录状态
    if (app.globalData.userId) {
      this.setData({
        showLoginModal: false
      })
    }
  },

  // 加载花卉详情
  loadFlowerDetail(flowerId) {
    wx.showLoading({
      title: '加载中...'
    })

    app.request({
      url: `/flower/detail/${flowerId}`,
      method: 'GET'
    }).then(res => {
      const flower = res.data
      flower.imageList = flower.images ? JSON.parse(flower.images) : []
      flower.tagList = flower.tags ? flower.tags.split(',') : []
      
      this.setData({
        flower: flower
      })
      
      // 设置页面标题
      wx.setNavigationBarTitle({
        title: flower.name
      })
      
      wx.hideLoading()
    }).catch(err => {
      console.error('加载花卉详情失败', err)
      wx.hideLoading()
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      })
    })
  },

  // 检查收藏状态
  checkFavoriteStatus(flowerId) {
    if (!app.globalData.userId) return

    app.request({
      url: '/favorite/check',
      method: 'GET',
      data: {
        userId: app.globalData.userId,
        flowerId: flowerId
      }
    }).then(res => {
      this.setData({
        isFavorite: res.data
      })
    }).catch(err => {
      console.error('检查收藏状态失败', err)
    })
  },

  // 切换收藏状态
  toggleFavorite() {
    if (!app.globalData.userId) {
      this.setData({
        showLoginModal: true
      })
      return
    }

    const { isFavorite, flower } = this.data
    const url = isFavorite ? '/favorite/remove' : '/favorite/add'
    const method = isFavorite ? 'DELETE' : 'POST'

    app.request({
      url: url,
      method: method,
      data: {
        userId: app.globalData.userId,
        flowerId: flower.id
      }
    }).then(res => {
      this.setData({
        isFavorite: !isFavorite
      })
      wx.showToast({
        title: isFavorite ? '已取消收藏' : '已添加收藏',
        icon: 'success'
      })
    }).catch(err => {
      console.error('操作收藏失败', err)
      wx.showToast({
        title: '操作失败，请重试',
        icon: 'none'
      })
    })
  },

  // 减少数量
  decreaseQuantity() {
    if (this.data.quantity > 1) {
      this.setData({
        quantity: this.data.quantity - 1
      })
    }
  },

  // 增加数量
  increaseQuantity() {
    if (this.data.quantity < this.data.flower.stockQuantity) {
      this.setData({
        quantity: this.data.quantity + 1
      })
    }
  },

  // 输入数量
  onQuantityInput(e) {
    const value = parseInt(e.detail.value) || 1
    const maxQuantity = this.data.flower.stockQuantity
    const quantity = Math.max(1, Math.min(value, maxQuantity))
    
    this.setData({
      quantity: quantity
    })
  },

  // 添加到购物车
  addToCart() {
    if (!app.globalData.userId) {
      this.setData({
        showLoginModal: true
      })
      return
    }

    if (this.data.flower.stockQuantity <= 0) {
      wx.showToast({
        title: '商品已售罄',
        icon: 'none'
      })
      return
    }

    wx.showLoading({
      title: '添加中...'
    })

    app.request({
      url: '/cart/add',
      method: 'POST',
      data: {
        userId: app.globalData.userId,
        flowerId: this.data.flower.id,
        quantity: this.data.quantity
      }
    }).then(res => {
      wx.hideLoading()
      wx.showToast({
        title: '已添加到购物车',
        icon: 'success'
      })
      // 使用新的方法更新购物车徽章
      app.addToCartWithBadge()
    }).catch(err => {
      wx.hideLoading()
      console.error('添加购物车失败', err)
      wx.showToast({
        title: '添加失败，请重试',
        icon: 'none'
      })
    })
  },

  // 立即购买
  buyNow() {
    if (!app.globalData.userId) {
      this.setData({
        showLoginModal: true
      })
      return
    }

    if (this.data.flower.stockQuantity <= 0) {
      wx.showToast({
        title: '商品已售罄',
        icon: 'none'
      })
      return
    }

    // 跳转到订单确认页面
    const orderData = {
      type: 'direct', // 直接下单
      items: [{
        flowerId: this.data.flower.id,
        quantity: this.data.quantity,
        flower: this.data.flower
      }]
    }

    wx.navigateTo({
      url: `/pages/order-confirm/order-confirm?data=${encodeURIComponent(JSON.stringify(orderData))}`
    })
  },

  // 显示登录弹窗
  showLoginModal() {
    this.setData({
      showLoginModal: true
    })
  },

  // 隐藏登录弹窗
  hideLoginModal() {
    this.setData({
      showLoginModal: false
    })
  },

  // 跳转到登录页面
  goToLogin() {
    this.hideLoginModal()
    wx.navigateTo({
      url: '/pages/login/login'
    })
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 空函数，用于阻止事件冒泡
  }
})
