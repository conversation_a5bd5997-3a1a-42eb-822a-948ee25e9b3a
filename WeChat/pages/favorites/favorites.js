// pages/favorites/favorites.js
const app = getApp()

Page({
  data: {
    favoriteFlowers: [],
    isLoading: false,
    isEmpty: false,
    showDeleteModal: false,
    deleteFlowerId: null,
    deleteType: 'single', // 'single' 或 'batch'
    isEditMode: false,
    selectedItems: [],
    isAllSelected: false
  },

  onLoad() {
    this.loadFavorites()
  },

  onShow() {
    this.loadFavorites()
  },

  onPullDownRefresh() {
    this.loadFavorites()
    wx.stopPullDownRefresh()
  },

  // 加载收藏列表
  loadFavorites() {
    if (!app.globalData.userId) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
      setTimeout(() => {
        wx.navigateTo({
          url: '/pages/login/login'
        })
      }, 1500)
      return
    }

    this.setData({
      isLoading: true,
      isEditMode: false,
      selectedItems: [],
      isAllSelected: false
    })

    app.request({
      url: `/favorite/flowers/${app.globalData.userId}`,
      method: 'GET'
    }).then(res => {
      const flowers = res.data.map(item => ({
        ...item,
        tagList: item.tags ? item.tags.split(',') : [],
        isNew: this.isNewProduct(item.createdAt) // 判断是否为新品
      }))

      this.setData({
        favoriteFlowers: flowers,
        isEmpty: flowers.length === 0,
        isLoading: false
      })
    }).catch(err => {
      console.error('加载收藏列表失败', err)
      this.setData({
        isLoading: false,
        isEmpty: true
      })
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      })
    })
  },

  // 判断是否为新品（7天内上架的商品）
  isNewProduct(createdAt) {
    if (!createdAt) return false
    const now = new Date()
    const createTime = new Date(createdAt)
    const diffTime = now - createTime
    const diffDays = diffTime / (1000 * 60 * 60 * 24)
    return diffDays <= 7
  },

  // 切换编辑模式
  toggleEditMode() {
    const newEditMode = !this.data.isEditMode
    this.setData({
      isEditMode: newEditMode,
      selectedItems: [],
      isAllSelected: false
    })
  },

  // 切换选中状态
  toggleSelect(e) {
    const id = e.currentTarget.dataset.id
    const { selectedItems } = this.data
    const index = selectedItems.indexOf(id)

    if (index > -1) {
      selectedItems.splice(index, 1)
    } else {
      selectedItems.push(id)
    }

    this.setData({
      selectedItems,
      isAllSelected: selectedItems.length === this.data.favoriteFlowers.length
    })
  },

  // 全选/取消全选
  toggleSelectAll() {
    const { isAllSelected, favoriteFlowers } = this.data
    if (isAllSelected) {
      this.setData({
        selectedItems: [],
        isAllSelected: false
      })
    } else {
      this.setData({
        selectedItems: favoriteFlowers.map(item => item.id),
        isAllSelected: true
      })
    }
  },

  // 跳转到详情页
  goToDetail(e) {
    if (this.data.isEditMode) return // 编辑模式下不跳转
    const flowerId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/detail/detail?id=${flowerId}`
    })
  },

  // 显示删除确认弹窗
  showDeleteConfirm(e) {
    const flowerId = e.currentTarget.dataset.id
    this.setData({
      showDeleteModal: true,
      deleteFlowerId: flowerId,
      deleteType: 'single'
    })
  },

  // 批量删除
  batchDelete() {
    if (this.data.selectedItems.length === 0) return

    this.setData({
      showDeleteModal: true,
      deleteType: 'batch'
    })
  },

  // 隐藏删除弹窗
  hideDeleteModal() {
    this.setData({
      showDeleteModal: false,
      deleteFlowerId: null,
      deleteType: 'single'
    })
  },

  // 确认删除收藏
  confirmDelete() {
    const { deleteType, deleteFlowerId, selectedItems } = this.data

    if (deleteType === 'single') {
      if (!deleteFlowerId) return
      this.removeSingleFavorite(deleteFlowerId)
    } else if (deleteType === 'batch') {
      if (selectedItems.length === 0) return
      this.removeBatchFavorites(selectedItems)
    }
  },

  // 删除单个收藏
  removeSingleFavorite(flowerId) {
    app.request({
      url: '/favorite/remove',
      method: 'DELETE',
      data: {
        userId: app.globalData.userId,
        flowerId: flowerId
      }
    }).then(res => {
      wx.showToast({
        title: '已取消收藏',
        icon: 'success'
      })
      this.hideDeleteModal()
      this.loadFavorites()
    }).catch(err => {
      console.error('取消收藏失败', err)
      wx.showToast({
        title: '操作失败',
        icon: 'none'
      })
      this.hideDeleteModal()
    })
  },

  // 批量删除收藏
  removeBatchFavorites(flowerIds) {
    const promises = flowerIds.map(flowerId => {
      return app.request({
        url: '/favorite/remove',
        method: 'DELETE',
        data: {
          userId: app.globalData.userId,
          flowerId: flowerId
        }
      })
    })

    Promise.all(promises).then(() => {
      wx.showToast({
        title: `已删除${flowerIds.length}个收藏`,
        icon: 'success'
      })
      this.hideDeleteModal()
      this.setData({
        isEditMode: false,
        selectedItems: [],
        isAllSelected: false
      })
      this.loadFavorites()
    }).catch(err => {
      console.error('批量删除失败', err)
      wx.showToast({
        title: '删除失败，请重试',
        icon: 'none'
      })
      this.hideDeleteModal()
    })
  },

  // 添加到购物车
  addToCart(e) {
    const flowerId = e.currentTarget.dataset.id

    if (!app.globalData.userId) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
      return
    }

    // 显示加载提示
    wx.showLoading({
      title: '添加中...'
    })

    app.request({
      url: '/cart/add',
      method: 'POST',
      data: {
        userId: app.globalData.userId,
        flowerId: flowerId,
        quantity: 1
      }
    }).then(res => {
      wx.hideLoading()
      wx.showToast({
        title: '已添加到购物车',
        icon: 'success'
      })
      // 更新购物车徽章
      if (app.addToCartWithBadge) {
        app.addToCartWithBadge()
      }
    }).catch(err => {
      wx.hideLoading()
      console.error('添加购物车失败', err)

      // 根据错误信息显示不同提示
      let errorMsg = '添加失败'
      if (err.message && err.message.includes('库存不足')) {
        errorMsg = '商品库存不足'
      } else if (err.message && err.message.includes('已下架')) {
        errorMsg = '商品已下架'
      }

      wx.showToast({
        title: errorMsg,
        icon: 'none'
      })
    })
  },

  // 去购物
  goShopping() {
    wx.switchTab({
      url: '/pages/index/index'
    })
  }
})