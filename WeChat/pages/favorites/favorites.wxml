<!-- pages/favorites/favorites.wxml -->
<view class="page-container">
  <!-- 页面头部 -->
  <view class="page-header">
    <view class="header-content">
      <view class="header-title">
        <text class="title-text">我的收藏</text>
        <text class="title-count" wx:if="{{!isEmpty && !isLoading}}">{{favoriteFlowers.length}}件商品</text>
      </view>
      <view class="header-actions" wx:if="{{!isEmpty && !isLoading}}">
        <view class="edit-mode-btn" bindtap="toggleEditMode">
          <text class="edit-text">{{isEditMode ? '完成' : '编辑'}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-state" wx:if="{{isLoading}}">
    <view class="loading-container">
      <view class="loading-spinner"></view>
      <text class="loading-text">正在加载收藏...</text>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:elif="{{isEmpty}}">
    <view class="empty-container">
      <view class="empty-icon">
        <image class="empty-image" src="/images/empty-favorite.png" mode="aspectFit" />
      </view>
      <text class="empty-title">还没有收藏任何商品</text>
      <text class="empty-desc">发现心仪的花卉，点击爱心收藏吧</text>
      <button class="go-shopping-btn" bindtap="goShopping">
        <text class="btn-text">去逛逛</text>
      </button>
    </view>
  </view>

  <!-- 收藏列表 -->
  <view class="favorites-container" wx:else>
    <view class="favorites-list">
      <view class="favorite-item {{isEditMode ? 'edit-mode' : ''}}"
            wx:for="{{favoriteFlowers}}"
            wx:key="id"
            bindtap="{{isEditMode ? '' : 'goToDetail'}}"
            data-id="{{item.id}}">

        <!-- 编辑模式选择框 -->
        <view class="select-checkbox" wx:if="{{isEditMode}}"
              bindtap="toggleSelect"
              data-id="{{item.id}}"
              catchtap="">
          <view class="checkbox {{selectedItems.indexOf(item.id) !== -1 ? 'checked' : ''}}">
            <text class="check-icon" wx:if="{{selectedItems.indexOf(item.id) !== -1}}">✓</text>
          </view>
        </view>

        <!-- 商品图片 -->
        <view class="item-image-container">
          <image class="item-image" src="{{item.mainImage}}" mode="aspectFill" />
          <view class="image-overlay" wx:if="{{item.status === 0}}">
            <text class="overlay-text">已下架</text>
          </view>
        </view>

        <!-- 商品信息 -->
        <view class="item-info">
          <view class="item-header">
            <text class="item-name">{{item.name}}</text>
            <view class="item-badge" wx:if="{{item.isNew}}">
              <text class="badge-text">新品</text>
            </view>
          </view>

          <text class="item-desc">{{item.description}}</text>

          <view class="item-tags" wx:if="{{item.tagList && item.tagList.length > 0}}">
            <text class="item-tag" wx:for="{{item.tagList}}" wx:key="index" wx:for-item="tag">{{tag}}</text>
          </view>

          <view class="item-price-row">
            <view class="price-container">
              <text class="current-price">¥{{item.price}}</text>
              <text class="original-price" wx:if="{{item.originalPrice && item.originalPrice > item.price}}">¥{{item.originalPrice}}</text>
            </view>
            <text class="sales-info">已售{{item.salesCount || 0}}件</text>
          </view>

          <view class="item-actions" wx:if="{{!isEditMode}}">
            <view class="action-btn remove-btn" catchtap="showDeleteConfirm" data-id="{{item.id}}">
              <text class="action-icon">♡</text>
              <text class="action-text">取消收藏</text>
            </view>
            <view class="action-btn cart-btn" catchtap="addToCart" data-id="{{item.id}}">
              <text class="action-icon">🛒</text>
              <text class="action-text">加入购物车</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 编辑模式底部操作栏 -->
    <view class="edit-bottom-bar" wx:if="{{isEditMode}}">
      <view class="select-all-container">
        <view class="select-all-btn" bindtap="toggleSelectAll">
          <view class="checkbox {{isAllSelected ? 'checked' : ''}}">
            <text class="check-icon" wx:if="{{isAllSelected}}">✓</text>
          </view>
          <text class="select-all-text">全选</text>
        </view>
      </view>
      <view class="edit-actions">
        <button class="batch-delete-btn {{selectedItems.length === 0 ? 'disabled' : ''}}"
                bindtap="batchDelete"
                disabled="{{selectedItems.length === 0}}">
          删除选中 ({{selectedItems.length}})
        </button>
      </view>
    </view>
  </view>

  <!-- 删除确认弹窗 -->
  <view class="modal-overlay" wx:if="{{showDeleteModal}}" bindtap="hideDeleteModal">
    <view class="modal-container" catchtap="">
      <view class="modal-header">
        <text class="modal-title">{{deleteType === 'single' ? '取消收藏' : '批量删除'}}</text>
      </view>
      <view class="modal-body">
        <text class="modal-text">
          {{deleteType === 'single' ? '确定要取消收藏这个商品吗？' : '确定要删除选中的' + selectedItems.length + '个商品吗？'}}
        </text>
      </view>
      <view class="modal-footer">
        <button class="modal-btn cancel-btn" bindtap="hideDeleteModal">取消</button>
        <button class="modal-btn confirm-btn" bindtap="confirmDelete">确定</button>
      </view>
    </view>
  </view>
</view>