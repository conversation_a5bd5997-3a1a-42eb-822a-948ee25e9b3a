/* pages/favorites/favorites.wxss */

/* 页面容器 */
.page-container {
  min-height: 100vh;
  background: linear-gradient(180deg, #fef7f0 0%, #f8f8f8 100%);
}

/* 页面头部 */
.page-header {
  background: white;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-title {
  display: flex;
  align-items: baseline;
  gap: 15rpx;
}

.title-text {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.title-count {
  font-size: 24rpx;
  color: #999;
  background: #f5f5f5;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

.header-actions {
  display: flex;
  align-items: center;
}

.edit-mode-btn {
  padding: 12rpx 24rpx;
  background: rgba(255, 107, 157, 0.1);
  border-radius: 20rpx;
  transition: all 0.2s ease;
}

.edit-mode-btn:active {
  background: rgba(255, 107, 157, 0.2);
  transform: scale(0.95);
}

.edit-text {
  font-size: 26rpx;
  color: #ff6b9d;
  font-weight: 500;
}

/* 加载状态 */
.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
}

.loading-container {
  text-align: center;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f0f0f0;
  border-top: 4rpx solid #ff6b9d;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 30rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* 空状态 */
.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
}

.empty-container {
  text-align: center;
  padding: 0 40rpx;
}

.empty-icon {
  margin-bottom: 40rpx;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  opacity: 0.6;
}

.empty-title {
  display: block;
  font-size: 32rpx;
  color: #666;
  font-weight: 500;
  margin-bottom: 15rpx;
}

.empty-desc {
  display: block;
  font-size: 26rpx;
  color: #999;
  margin-bottom: 50rpx;
  line-height: 1.4;
}

.go-shopping-btn {
  background: linear-gradient(135deg, #ff6b9d, #ff8fab);
  color: white;
  border: none;
  border-radius: 50rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
  font-weight: 500;
  box-shadow: 0 8rpx 20rpx rgba(255, 107, 157, 0.3);
  transition: all 0.2s ease;
}

.go-shopping-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 157, 0.3);
}

.btn-text {
  color: white;
}

/* 收藏容器 */
.favorites-container {
  flex: 1;
}

.favorites-list {
  padding: 20rpx;
  padding-bottom: 120rpx; /* 为底部操作栏留空间 */
}

.favorite-item {
  display: flex;
  background: white;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  position: relative;
}

.favorite-item:active {
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.12);
}

.favorite-item.edit-mode {
  padding-left: 80rpx;
}

/* 选择框 */
.select-checkbox {
  position: absolute;
  left: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  z-index: 10;
}

.checkbox {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #ddd;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  transition: all 0.2s ease;
}

.checkbox.checked {
  background: #ff6b9d;
  border-color: #ff6b9d;
}

.check-icon {
  color: white;
  font-size: 24rpx;
  font-weight: bold;
}

/* 商品图片 */
.item-image-container {
  position: relative;
  width: 220rpx;
  height: 220rpx;
  flex-shrink: 0;
}

.item-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
}

.overlay-text {
  color: white;
  font-size: 24rpx;
  font-weight: 500;
}

/* 商品信息 */
.item-info {
  flex: 1;
  padding: 25rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.item-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 12rpx;
}

.item-name {
  font-size: 30rpx;
  color: #333;
  font-weight: 600;
  line-height: 1.3;
  flex: 1;
  margin-right: 10rpx;
}

.item-badge {
  background: linear-gradient(135deg, #ff6b9d, #ff8fab);
  color: white;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  font-weight: 500;
  flex-shrink: 0;
}

.badge-text {
  color: white;
}

.item-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
  margin-bottom: 15rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.item-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
  margin-bottom: 15rpx;
}

.item-tag {
  background: rgba(255, 107, 157, 0.1);
  color: #ff6b9d;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  font-weight: 500;
}

.item-price-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.price-container {
  display: flex;
  align-items: baseline;
  gap: 10rpx;
}

.current-price {
  font-size: 32rpx;
  color: #ff6b9d;
  font-weight: 600;
}

.original-price {
  font-size: 24rpx;
  color: #999;
  text-decoration: line-through;
}

.sales-info {
  font-size: 22rpx;
  color: #999;
}

.item-actions {
  display: flex;
  gap: 15rpx;
}

.action-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  padding: 16rpx;
  border-radius: 12rpx;
  font-size: 24rpx;
  font-weight: 500;
  transition: all 0.2s ease;
}

.remove-btn {
  background: rgba(255, 59, 48, 0.1);
  color: #ff3b30;
}

.cart-btn {
  background: rgba(255, 107, 157, 0.1);
  color: #ff6b9d;
}

.action-btn:active {
  transform: scale(0.95);
}

.action-icon {
  font-size: 20rpx;
}

.action-text {
  font-size: 22rpx;
}

/* 编辑模式底部操作栏 */
.edit-bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 20rpx 30rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
  border-top: 1rpx solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 100;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.select-all-container {
  display: flex;
  align-items: center;
}

.select-all-btn {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 12rpx;
  transition: all 0.2s ease;
}

.select-all-btn:active {
  transform: scale(0.95);
}

.select-all-text {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.edit-actions {
  display: flex;
  gap: 15rpx;
}

.batch-delete-btn {
  background: #ff3b30;
  color: white;
  border: none;
  border-radius: 25rpx;
  padding: 16rpx 30rpx;
  font-size: 26rpx;
  font-weight: 500;
  transition: all 0.2s ease;
}

.batch-delete-btn.disabled {
  background: #ddd;
  color: #999;
}

.batch-delete-btn:not(.disabled):active {
  background: #e6342a;
  transform: scale(0.95);
}

/* 删除确认弹窗 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.modal-container {
  background: white;
  border-radius: 24rpx;
  width: 600rpx;
  max-width: 85%;
  overflow: hidden;
  animation: slideUp 0.3s ease;
}

@keyframes slideUp {
  from {
    transform: translateY(50rpx);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.modal-header {
  padding: 40rpx 30rpx 20rpx;
  text-align: center;
}

.modal-title {
  font-size: 34rpx;
  font-weight: 600;
  color: #333;
}

.modal-body {
  padding: 20rpx 30rpx 40rpx;
  text-align: center;
}

.modal-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

.modal-footer {
  display: flex;
  border-top: 1rpx solid #f0f0f0;
}

.modal-btn {
  flex: 1;
  padding: 32rpx;
  font-size: 28rpx;
  border: none;
  background: white;
  border-radius: 0;
  font-weight: 500;
  transition: all 0.2s ease;
}

.cancel-btn {
  color: #666;
  border-right: 1rpx solid #f0f0f0;
}

.confirm-btn {
  color: #ff3b30;
  font-weight: 600;
}

.modal-btn:active {
  background: #f8f8f8;
}
