/* pages/profile/profile.wxss */

/* 页面容器 */
.container {
  min-height: 100vh;
  background: linear-gradient(180deg, #ff6b9d 0%, #ff8fab 30%, #f8f8f8 100%);
  padding-top: 40rpx;
}

/* 用户信息区域 */
.user-section {
  margin: 0 30rpx 30rpx;
  background: white;
  border-radius: 24rpx;
  padding: 40rpx 30rpx;
  box-shadow: 0 8rpx 30rpx rgba(255, 107, 157, 0.15);
  position: relative;
  overflow: hidden;
}

.user-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6rpx;
  background: linear-gradient(90deg, #ff6b9d, #ff8fab, #ffa8c5);
}

.user-info,
.login-prompt {
  display: flex;
  align-items: center;
  gap: 25rpx;
}

.avatar-container {
  position: relative;
  width: 140rpx;
  height: 140rpx;
}

.user-avatar,
.default-avatar {
  width: 140rpx;
  height: 140rpx;
  border-radius: 50%;
  border: 6rpx solid rgba(255, 107, 157, 0.1);
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.avatar-edit-icon {
  position: absolute;
  bottom: 8rpx;
  right: 8rpx;
  width: 36rpx;
  height: 36rpx;
  background: linear-gradient(135deg, #ff6b9d, #ff8fab);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18rpx;
  color: white;
  border: 3rpx solid white;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 157, 0.4);
}

.user-details,
.login-info {
  flex: 1;
}

.user-name,
.login-text {
  font-size: 34rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 12rpx;
}

.user-phone,
.login-desc {
  font-size: 26rpx;
  color: #666;
  background: rgba(255, 107, 157, 0.1);
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  display: inline-block;
}

.arrow-icon {
  font-size: 24rpx;
  color: #ff6b9d;
  font-weight: bold;
}

/* 订单管理 */
.order-section {
  margin: 0 30rpx 30rpx;
  background: white;
  border-radius: 24rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.section-title::before {
  content: '📋';
  font-size: 24rpx;
}

.section-more {
  font-size: 26rpx;
  color: #ff6b9d;
  font-weight: 500;
  padding: 8rpx 16rpx;
  background: rgba(255, 107, 157, 0.1);
  border-radius: 16rpx;
  transition: all 0.2s ease;
}

.section-more:active {
  background: rgba(255, 107, 157, 0.2);
  transform: scale(0.95);
}

.order-types {
  display: flex;
  justify-content: space-around;
}

.order-type {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  padding: 20rpx 15rpx;
  border-radius: 16rpx;
  transition: all 0.2s ease;
  min-width: 120rpx;
}

.order-type:active {
  background: rgba(255, 107, 157, 0.1);
  transform: scale(0.95);
}

.type-icon {
  font-size: 48rpx;
  margin-bottom: 12rpx;
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.1));
}

.type-name {
  font-size: 24rpx;
  color: #666;
  font-weight: 500;
}

.type-badge {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  background: linear-gradient(135deg, #ff3b30, #ff6b59);
  color: white;
  border-radius: 50%;
  width: 36rpx;
  height: 36rpx;
  font-size: 20rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 36rpx;
  box-shadow: 0 2rpx 8rpx rgba(255, 59, 48, 0.3);
  border: 2rpx solid white;
}

/* 功能菜单 */
.menu-section,
.settings-section {
  margin: 0 30rpx 30rpx;
  background: white;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 35rpx 30rpx;
  border-bottom: 1rpx solid #f8f8f8;
  gap: 25rpx;
  transition: all 0.2s ease;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item:active {
  background: rgba(255, 107, 157, 0.05);
}

.menu-icon {
  width: 60rpx;
  height: 60rpx;
  background: rgba(255, 107, 157, 0.1);
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  flex-shrink: 0;
}

.menu-name {
  flex: 1;
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
}

.cache-size {
  font-size: 24rpx;
  color: #999;
  background: #f5f5f5;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
}

.phone-status {
  font-size: 24rpx;
  color: #999;
  background: #f5f5f5;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
}

/* 版本信息 */
.version-info {
  text-align: center;
  padding: 40rpx;
  margin-bottom: 40rpx;
}

.version-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  background: rgba(255, 255, 255, 0.1);
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  display: inline-block;
  backdrop-filter: blur(10rpx);
}

/* 缓存管理弹窗 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.cache-modal {
  background: white;
  border-radius: 20rpx;
  width: 650rpx;
  max-width: 90%;
  max-height: 80%;
  overflow: hidden;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.close-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  color: #999;
  border-radius: 50%;
  background: #f8f8f8;
}

.cache-info {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 28rpx;
  color: #666;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.cache-actions {
  padding: 20rpx;
}

.action-item {
  display: flex;
  align-items: center;
  padding: 25rpx;
  border-radius: 12rpx;
  margin-bottom: 15rpx;
  transition: all 0.2s ease;
}

.action-item:last-child {
  margin-bottom: 0;
}

.action-item:active {
  background: #f8f8f8;
}

.action-item.danger:active {
  background: rgba(255, 59, 48, 0.1);
}

.action-icon {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  background: #f8f8f8;
  border-radius: 50%;
  margin-right: 20rpx;
}

.action-item.danger .action-icon {
  background: rgba(255, 59, 48, 0.1);
}

.action-content {
  flex: 1;
}

.action-title {
  display: block;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 5rpx;
}

.action-item.danger .action-title {
  color: #ff3b30;
}

.action-desc {
  display: block;
  font-size: 22rpx;
  color: #999;
}
