// pages/category-products/category-products.js
const app = getApp()

Page({
  data: {
    categoryId: null,
    categoryName: '',
    featuredFlowers: [], // 精选商品
    flowers: [], // 普通商品
    currentPage: 1,
    pageSize: 10,
    hasMore: true,
    isLoading: false,
    featuredLoaded: false, // 精选商品是否已加载
    priceCategories: [], // 价格分类
    selectedPriceCategory: null, // 选中的价格分类
    showPriceFilter: false // 是否显示价格筛选
  },

  onLoad(options) {
    if (options.categoryId && options.categoryName) {
      this.setData({
        categoryId: parseInt(options.categoryId),
        categoryName: decodeURIComponent(options.categoryName)
      })
      
      // 设置页面标题
      wx.setNavigationBarTitle({
        title: this.data.categoryName
      })
      
      // 加载价格分类和商品数据
      this.loadPriceCategories()
    }
  },

  onShow() {
    app.updateCartCount()
  },

  onPullDownRefresh() {
    this.refreshData()
    wx.stopPullDownRefresh()
  },

  onReachBottom() {
    this.loadMoreFlowers()
  },

  // 加载价格分类
  loadPriceCategories() {
    app.request({
      url: '/flower/price-categories',
      method: 'GET'
    }).then(res => {
      this.setData({
        priceCategories: res.data || []
      })
      // 价格分类加载完成后，加载商品数据
      this.loadFeaturedFlowers()
    }).catch(err => {
      console.error('加载价格分类失败', err)
      // 即使价格分类加载失败，也要加载商品数据
      this.loadFeaturedFlowers()
    })
  },

  // 加载精选花卉
  loadFeaturedFlowers() {
    if (this.data.isLoading) return

    this.setData({
      isLoading: true
    })

    // 构建请求参数
    const requestData = {
      categoryId: this.data.categoryId,
      limit: 6 // 限制精选商品数量
    }

    // 添加价格筛选参数
    if (this.data.selectedPriceCategory) {
      requestData.minPrice = this.data.selectedPriceCategory.minPrice
      requestData.maxPrice = this.data.selectedPriceCategory.maxPrice
    }

    app.request({
      url: '/flower/featured',
      method: 'GET',
      data: requestData
    }).then(res => {
      const featuredFlowers = res.data.map(item => ({
        ...item,
        tagList: item.tags ? item.tags.split(',') : [],
        isFeatured: true // 标记为精选商品
      }))
      
      this.setData({
        featuredFlowers: featuredFlowers,
        featuredLoaded: true,
        isLoading: false
      })
      
      // 精选商品加载完成后，加载普通商品
      this.loadFlowers()
    }).catch(err => {
      console.error('加载精选花卉失败', err)
      this.setData({
        featuredLoaded: true,
        isLoading: false
      })
      // 即使精选商品加载失败，也要加载普通商品
      this.loadFlowers()
    })
  },

  // 加载花卉列表
  loadFlowers() {
    if (this.data.isLoading) return

    this.setData({
      isLoading: true
    })

    // 构建请求参数
    const requestData = {
      current: this.data.currentPage,
      size: this.data.pageSize,
      categoryId: this.data.categoryId,
      excludeFeatured: true // 排除精选商品，避免重复显示
    }

    // 添加价格筛选参数
    if (this.data.selectedPriceCategory) {
      requestData.minPrice = this.data.selectedPriceCategory.minPrice
      requestData.maxPrice = this.data.selectedPriceCategory.maxPrice
    }

    app.request({
      url: '/flower/list',
      method: 'GET',
      data: requestData
    }).then(res => {
      const flowers = res.data.records.map(item => ({
        ...item,
        tagList: item.tags ? item.tags.split(',') : []
      }))
      
      if (this.data.currentPage === 1) {
        this.setData({
          flowers: flowers
        })
      } else {
        this.setData({
          flowers: [...this.data.flowers, ...flowers]
        })
      }
      
      this.setData({
        hasMore: this.data.currentPage < res.data.pages,
        isLoading: false
      })
    }).catch(err => {
      console.error('加载花卉列表失败', err)
      this.setData({
        isLoading: false
      })
    })
  },

  // 加载更多
  loadMoreFlowers() {
    if (!this.data.hasMore || this.data.isLoading) return
    
    this.setData({
      currentPage: this.data.currentPage + 1
    })
    this.loadFlowers()
  },

  // 刷新数据
  refreshData() {
    this.setData({
      currentPage: 1,
      hasMore: true,
      flowers: [],
      featuredFlowers: [],
      featuredLoaded: false
    })
    this.loadFeaturedFlowers()
  },

  // 切换价格筛选显示
  togglePriceFilter() {
    this.setData({
      showPriceFilter: !this.data.showPriceFilter
    })
  },

  // 选择价格分类
  selectPriceCategory(e) {
    const categoryId = e.currentTarget.dataset.id
    let selectedCategory = null

    if (categoryId && categoryId !== 'all') {
      selectedCategory = this.data.priceCategories.find(cat => cat.id == categoryId)
    }

    this.setData({
      selectedPriceCategory: selectedCategory,
      showPriceFilter: false,
      currentPage: 1,
      hasMore: true,
      flowers: [],
      featuredFlowers: [],
      featuredLoaded: false
    })

    // 重新加载数据
    this.loadFeaturedFlowers()
  },

  // 跳转到商品详情
  goToDetail(e) {
    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/detail/detail?id=${id}`
    })
  },

  // 添加到购物车
  addToCart(e) {
    const id = e.currentTarget.dataset.id
    
    if (!app.globalData.userId) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
      return
    }

    wx.showLoading({
      title: '添加中...'
    })

    app.request({
      url: '/cart/add',
      method: 'POST',
      data: {
        userId: app.globalData.userId,
        flowerId: id,
        quantity: 1
      }
    }).then(res => {
      wx.hideLoading()
      wx.showToast({
        title: '添加成功',
        icon: 'success'
      })
      app.updateCartCount()
    }).catch(err => {
      wx.hideLoading()
      console.error('添加到购物车失败', err)
      wx.showToast({
        title: '添加失败',
        icon: 'none'
      })
    })
  }
})
