// pages/user-edit/user-edit.js
const app = getApp()

Page({
  data: {
    userInfo: null,
    avatarUrl: '',
    nickname: '',
    currentNickname: '',
    currentAvatarUrl: '/images/default-avatar.png',
    phone: '',
    currentPhone: '',
    phoneStatus: '登录时自动绑定',
    hasChanges: false,
    isSaving: false,
    phoneError: '',
    currentNicknameLength: 0,
    currentPhoneLength: 0,
    saveButtonDisabled: true,
    saveButtonText: '保存修改'
  },

  onLoad() {
    this.loadUserInfo()
  },

  // 加载用户信息
  loadUserInfo() {
    const userInfo = app.globalData.userInfo
    if (!userInfo) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
      wx.navigateBack()
      return
    }

    const nickname = userInfo.nickname || ''
    const avatarUrl = userInfo.avatarUrl || ''
    const phone = userInfo.phone || ''

    this.setData({
      userInfo: userInfo,
      avatarUrl: avatarUrl,
      nickname: nickname,
      currentNickname: nickname,
      currentAvatarUrl: avatarUrl || '/images/default-avatar.png',
      phone: phone,
      currentPhone: phone || '',
      phoneStatus: phone ? '已绑定' : '登录时自动绑定',
      currentNicknameLength: nickname.length,
      currentPhoneLength: (phone || '').length,
      hasChanges: false,
      saveButtonDisabled: true,
      saveButtonText: '保存修改',
      phoneError: ''
    })
  },

  // 选择头像
  onChooseAvatar(e) {
    console.log('选择头像:', e.detail.avatarUrl)
    const newAvatarUrl = e.detail.avatarUrl
    const hasChanges = true
    this.setData({
      avatarUrl: newAvatarUrl,
      currentAvatarUrl: newAvatarUrl,
      hasChanges: hasChanges,
      saveButtonDisabled: !hasChanges || this.data.isSaving
    })

    wx.showToast({
      title: '头像选择成功',
      icon: 'success',
      duration: 1500
    })
  },

  // 输入昵称
  onNicknameInput(e) {
    const nickname = e.detail.value
    const hasChanges = this.checkHasChanges(this.data.avatarUrl, nickname, this.data.phone)
    this.setData({
      nickname: nickname,
      currentNickname: nickname,
      currentNicknameLength: nickname.length,
      hasChanges: hasChanges,
      saveButtonDisabled: !hasChanges || this.data.isSaving
    })
  },

  // 输入手机号
  onPhoneInput(e) {
    const phone = e.detail.value
    const isValid = this.validatePhone(phone)
    const hasChanges = this.checkHasChanges(this.data.avatarUrl, this.data.nickname, phone)

    this.setData({
      phone: phone,
      currentPhone: phone,
      currentPhoneLength: phone.length,
      phoneError: phone && !isValid ? '请输入正确的11位手机号' : '',
      hasChanges: hasChanges,
      saveButtonDisabled: !hasChanges || this.data.isSaving || (phone && !isValid)
    })
  },

  // 手机号格式校验
  validatePhone(phone) {
    if (!phone) return true // 允许空值
    const phoneRegex = /^1[3-9]\d{9}$/
    return phoneRegex.test(phone)
  },

  // 检查是否有修改
  checkHasChanges(avatarUrl, nickname, phone) {
    const { userInfo } = this.data
    return avatarUrl !== (userInfo.avatarUrl || '') ||
           nickname !== (userInfo.nickname || '') ||
           phone !== (userInfo.phone || '')
  },

  // 保存用户信息
  saveUserInfo() {
    if (!this.data.hasChanges) {
      wx.showToast({
        title: '没有修改内容',
        icon: 'none'
      })
      return
    }

    const { nickname, avatarUrl, phone } = this.data

    if (!nickname.trim()) {
      wx.showToast({
        title: '请输入昵称',
        icon: 'none'
      })
      return
    }

    // 验证手机号格式（如果有输入）
    if (phone && !this.validatePhone(phone)) {
      wx.showToast({
        title: '请输入正确的11位手机号',
        icon: 'none'
      })
      return
    }

    this.setData({
      isSaving: true,
      saveButtonDisabled: true,
      saveButtonText: '保存中...'
    })

    // 转换头像为Base64并保存
    this.convertAvatarToBase64(avatarUrl).then(avatarBase64 => {
      return app.request({
        url: '/user/update-profile',
        method: 'POST',
        data: {
          userId: app.globalData.userId,
          nickname: nickname.trim(),
          avatarBase64: avatarBase64,
          phone: phone || null
        }
      })
    }).then(response => {
      console.log('用户信息更新成功:', response)
      
      // 更新全局用户信息
      app.globalData.userInfo = { ...app.globalData.userInfo, ...response.data }
      
      wx.showToast({
        title: '保存成功',
        icon: 'success',
        duration: 2000
      })

      this.setData({
        isSaving: false,
        hasChanges: false,
        userInfo: response.data,
        currentAvatarUrl: response.data?.avatarUrl || '/images/default-avatar.png',
        currentNickname: response.data?.nickname || '微信用户',
        phone: response.data?.phone || '',
        currentPhone: response.data?.phone || '',
        currentPhoneLength: (response.data?.phone || '').length,
        phoneStatus: response.data?.phone ? '已绑定' : '登录时自动绑定',
        phoneError: '',
        saveButtonDisabled: true,
        saveButtonText: '保存修改'
      })

      // 延迟返回上一页
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)

    }).catch(err => {
      console.error('保存用户信息失败', err)
      wx.showToast({
        title: '保存失败，请重试',
        icon: 'none'
      })
      this.setData({
        isSaving: false,
        saveButtonDisabled: !this.data.hasChanges,
        saveButtonText: '保存修改'
      })
    })
  },

  // 将头像转换为Base64格式
  convertAvatarToBase64(avatarUrl) {
    return new Promise((resolve, reject) => {
      if (!avatarUrl || avatarUrl === this.data.userInfo.avatarUrl) {
        resolve(null)
        return
      }

      // 如果是本地临时文件路径，需要转换为Base64
      if (avatarUrl.startsWith('http://tmp/') || avatarUrl.startsWith('wxfile://')) {
        wx.getFileSystemManager().readFile({
          filePath: avatarUrl,
          encoding: 'base64',
          success: (res) => {
            resolve('data:image/jpeg;base64,' + res.data)
          },
          fail: (err) => {
            console.error('读取头像文件失败:', err)
            resolve(null)
          }
        })
      } else {
        // 如果是网络图片，下载后转换
        wx.downloadFile({
          url: avatarUrl,
          success: (res) => {
            if (res.statusCode === 200) {
              wx.getFileSystemManager().readFile({
                filePath: res.tempFilePath,
                encoding: 'base64',
                success: (fileRes) => {
                  resolve('data:image/jpeg;base64,' + fileRes.data)
                },
                fail: (err) => {
                  console.error('读取下载的头像文件失败:', err)
                  resolve(null)
                }
              })
            } else {
              resolve(null)
            }
          },
          fail: (err) => {
            console.error('下载头像失败:', err)
            resolve(null)
          }
        })
      }
    })
  },

  // 取消编辑
  cancelEdit() {
    if (this.data.hasChanges) {
      wx.showModal({
        title: '提示',
        content: '您有未保存的修改，确定要离开吗？',
        success: (res) => {
          if (res.confirm) {
            wx.navigateBack()
          }
        }
      })
    } else {
      wx.navigateBack()
    }
  },


})
