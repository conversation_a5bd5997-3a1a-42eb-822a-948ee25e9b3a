<!-- pages/user-edit/user-edit.wxml -->
<view class="container">
  <!-- 页面标题 -->
  <view class="page-header">
    <text class="page-title">编辑个人信息</text>
    <text class="page-desc">修改您的头像和昵称</text>
  </view>

  <!-- 编辑表单 -->
  <view class="edit-form">
    <!-- 头像编辑 -->
    <view class="form-section avatar-section">
      <view class="form-row">
        <text class="section-title">头像</text>
        <view class="avatar-edit-section">
          <button class="avatar-edit-btn" open-type="chooseAvatar" bindchooseavatar="onChooseAvatar">
            <view class="avatar-container">
              <image class="avatar-preview" src="{{currentAvatarUrl}}" mode="aspectFill"></image>
              <view class="avatar-overlay">
                <text class="camera-icon">📷</text>
                <text class="edit-text">更换头像</text>
              </view>
            </view>
          </button>
        </view>
      </view>
    </view>

    <!-- 昵称编辑 -->
    <view class="form-section">
      <view class="form-row">
        <text class="section-title">昵称</text>
        <view class="nickname-edit-section">
          <view class="input-wrapper">
            <input class="nickname-input"
                   type="nickname"
                   placeholder="请输入昵称"
                   value="{{currentNickname}}"
                   bindinput="onNicknameInput"
                   maxlength="20" />
            <view class="input-border"></view>
          </view>
          <text class="input-tip">{{currentNicknameLength}}/20</text>
        </view>
      </view>
    </view>

    <!-- 手机号编辑 -->
    <view class="form-section">
      <view class="form-row">
        <text class="section-title">手机号</text>
        <view class="phone-edit-section">
          <view class="input-wrapper">
            <input class="phone-input"
                   type="number"
                   placeholder="请输入11位手机号"
                   value="{{currentPhone}}"
                   bindinput="onPhoneInput"
                   maxlength="11" />
            <view class="input-border"></view>
          </view>
          <view class="phone-info">
            <text class="input-tip">{{currentPhoneLength}}/11</text>
            <text class="phone-status" wx:if="{{phoneStatus}}">{{phoneStatus}}</text>
          </view>
          <text class="phone-error" wx:if="{{phoneError}}">{{phoneError}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="action-buttons">
    <button class="save-btn"
            bindtap="saveUserInfo"
            disabled="{{saveButtonDisabled}}">
      <text class="btn-text">{{saveButtonText}}</text>
    </button>
    
    <button class="cancel-btn" bindtap="cancelEdit">
      <text class="btn-text">取消</text>
    </button>
  </view>
</view>
