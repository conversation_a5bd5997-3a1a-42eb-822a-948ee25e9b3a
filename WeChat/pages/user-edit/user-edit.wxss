/* pages/user-edit/user-edit.wxss */

.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #ff6b9d, #ff8fab);
  padding: 40rpx 30rpx;
}

/* 页面标题 */
.page-header {
  text-align: center;
  margin-bottom: 60rpx;
}

.page-title {
  display: block;
  font-size: 48rpx;
  font-weight: 700;
  color: white;
  margin-bottom: 16rpx;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.page-desc {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* 编辑表单 */
.edit-form {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  padding: 40rpx 32rpx;
  backdrop-filter: blur(20rpx);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
  margin-bottom: 40rpx;
}

.form-section {
  margin-bottom: 40rpx;
  padding: 24rpx 0;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
}

.form-section:last-child {
  margin-bottom: 0;
  border-bottom: none;
}

.form-row {
  display: flex;
  align-items: flex-start;
  gap: 32rpx;
}

.avatar-section .form-row {
  align-items: center;
}

.section-title {
  flex-shrink: 0;
  width: 120rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: #2c3e50;
  line-height: 88rpx;
}

/* 头像编辑 */
.avatar-edit-section {
  flex: 1;
}

.avatar-edit-btn {
  background: transparent;
  border: none;
  padding: 0;
}

.avatar-edit-btn::after {
  border: none;
}

.avatar-container {
  position: relative;
  width: 160rpx;
  height: 160rpx;
}

.avatar-preview {
  width: 160rpx;
  height: 160rpx;
  border-radius: 80rpx;
  border: 6rpx solid rgba(255, 255, 255, 0.8);
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
}

.avatar-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 80rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.avatar-edit-btn:active .avatar-overlay {
  opacity: 1;
}

.camera-icon {
  font-size: 48rpx;
  color: white;
  margin-bottom: 8rpx;
}

.edit-text {
  font-size: 24rpx;
  color: white;
  font-weight: 500;
}

/* 昵称编辑 */
.nickname-edit-section {
  position: relative;
  flex: 1;
}

.input-wrapper {
  position: relative;
  background: rgba(248, 249, 250, 0.8);
  border-radius: 16rpx;
  padding: 0 24rpx;
  height: 88rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
}

.input-wrapper:focus-within {
  border-color: #ff6b9d;
  background: rgba(255, 255, 255, 0.9);
  box-shadow: 0 4rpx 20rpx rgba(255, 107, 157, 0.2);
}

.nickname-input {
  width: 100%;
  height: 100%;
  background: transparent;
  border: none;
  font-size: 32rpx;
  color: #2c3e50;
  font-weight: 500;
}

.input-border {
  position: absolute;
  bottom: 0;
  left: 24rpx;
  right: 24rpx;
  height: 2rpx;
  background: linear-gradient(90deg, #ff6b9d, #ff8fab);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.input-wrapper:focus-within .input-border {
  transform: scaleX(1);
}

.input-tip {
  position: absolute;
  right: 0;
  top: 100rpx;
  font-size: 24rpx;
  color: #999;
}

/* 手机号编辑 */
.phone-edit-section {
  position: relative;
  flex: 1;
}

.phone-edit-section .input-wrapper {
  position: relative;
  background: rgba(248, 249, 250, 0.8);
  border-radius: 16rpx;
  padding: 0 24rpx;
  height: 88rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
}

.phone-edit-section .input-wrapper:focus-within {
  border-color: #ff6b9d;
  background: rgba(255, 255, 255, 0.9);
  box-shadow: 0 4rpx 20rpx rgba(255, 107, 157, 0.2);
}

.phone-input {
  width: 100%;
  height: 100%;
  background: transparent;
  border: none;
  font-size: 32rpx;
  color: #2c3e50;
  font-weight: 500;
}

.phone-edit-section .input-border {
  position: absolute;
  bottom: 0;
  left: 24rpx;
  right: 24rpx;
  height: 2rpx;
  background: linear-gradient(90deg, #ff6b9d, #ff8fab);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.phone-edit-section .input-wrapper:focus-within .input-border {
  transform: scaleX(1);
}

.phone-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 12rpx;
}

.phone-info .input-tip {
  font-size: 24rpx;
  color: #999;
}

.phone-status {
  font-size: 24rpx;
  color: #52c41a;
  background: rgba(82, 196, 26, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

.phone-error {
  font-size: 24rpx;
  color: #ff4d4f;
  margin-top: 8rpx;
  padding-left: 24rpx;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.save-btn {
  width: 100%;
  height: 96rpx;
  background: linear-gradient(135deg, #ff6b9d, #ff8fab);
  color: white;
  border: none;
  border-radius: 48rpx;
  font-size: 32rpx;
  font-weight: 600;
  box-shadow: 0 8rpx 24rpx rgba(255, 107, 157, 0.4);
  transition: all 0.3s ease;
}

.save-btn::after {
  border: none;
}

.save-btn:disabled {
  background: linear-gradient(135deg, #d1d5db, #e5e7eb);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  color: #9ca3af;
}

.save-btn:active:not(:disabled) {
  transform: scale(0.98);
  box-shadow: 0 4rpx 16rpx rgba(255, 107, 157, 0.3);
}

.cancel-btn {
  width: 100%;
  height: 88rpx;
  background: transparent;
  color: white;
  border: 2rpx solid rgba(255, 255, 255, 0.6);
  border-radius: 44rpx;
  font-size: 30rpx;
  font-weight: 500;
}

.cancel-btn::after {
  border: none;
}

.cancel-btn:active {
  background: rgba(255, 255, 255, 0.1);
}

.btn-text {
  font-weight: inherit;
}
