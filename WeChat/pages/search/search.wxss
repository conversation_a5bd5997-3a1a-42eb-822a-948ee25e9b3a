/* pages/search/search.wxss */

/* 页面容器 */
.search-container {
  min-height: 100vh;
  background: #f8f8f8;
}

/* 搜索头部 */
.search-header {
  background: white;
  padding: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.search-bar {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.back-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: #f5f5f5;
}

.back-icon {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
}

.search-input-wrapper {
  flex: 1;
  position: relative;
  background: #f5f5f5;
  border-radius: 50rpx;
  padding: 0 30rpx;
  display: flex;
  align-items: center;
}

.search-input {
  flex: 1;
  height: 70rpx;
  font-size: 28rpx;
  color: #333;
}

.clear-btn {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: #ccc;
}

.clear-icon {
  font-size: 24rpx;
  color: white;
  font-weight: bold;
}

.search-btn {
  padding: 20rpx 30rpx;
  background: linear-gradient(135deg, #ff6b9d, #ff8fab);
  border-radius: 50rpx;
}

.search-text {
  font-size: 28rpx;
  color: white;
  font-weight: 500;
}

/* 搜索建议区域 */
.search-suggestions {
  padding: 30rpx;
}

.suggestion-section {
  margin-bottom: 40rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
}

.clear-history-btn {
  padding: 10rpx 20rpx;
  background: #f5f5f5;
  border-radius: 20rpx;
}

.clear-text {
  font-size: 24rpx;
  color: #666;
}

/* 热门搜索 */
.hot-keywords {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.keyword-tag {
  padding: 15rpx 25rpx;
  border-radius: 50rpx;
  font-size: 26rpx;
  transition: all 0.2s ease;
}

.hot-tag {
  background: linear-gradient(135deg, #ff6b9d, #ff8fab);
  color: white;
}

.hot-tag:active {
  transform: scale(0.95);
}

.suggestion-tag {
  background: #f0f0f0;
  color: #666;
}

.keyword-text {
  font-size: 26rpx;
}

/* 搜索历史 */
.history-list {
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
}

.history-item {
  display: flex;
  align-items: center;
  padding: 25rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.history-item:last-child {
  border-bottom: none;
}

.history-content {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.history-icon {
  font-size: 28rpx;
  color: #999;
}

.history-text {
  font-size: 28rpx;
  color: #333;
}

.delete-history-btn {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: #f5f5f5;
}

.delete-icon {
  font-size: 24rpx;
  color: #999;
  font-weight: bold;
}

/* 搜索中状态 */
.searching-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
}

.searching-container {
  text-align: center;
}

.searching-text {
  display: block;
  font-size: 28rpx;
  color: #666;
  margin-top: 20rpx;
}

/* 搜索结果 */
.search-results {
  padding: 0 20rpx;
}

.results-header {
  padding: 30rpx 10rpx 20rpx;
}

.results-count {
  font-size: 26rpx;
  color: #666;
}

/* 商品列表 */
.products-list {
  margin-bottom: 30rpx;
}

.product-item {
  background: white;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  padding: 20rpx;
  display: flex;
  gap: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.product-item:active {
  transform: scale(0.98);
}

.product-image {
  width: 160rpx;
  height: 160rpx;
  border-radius: 12rpx;
  flex-shrink: 0;
}

.product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.product-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.product-desc {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 15rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  line-height: 1.4;
}

.product-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx;
  margin-bottom: 15rpx;
}

.product-tag {
  padding: 6rpx 12rpx;
  background: #f0f0f0;
  border-radius: 12rpx;
  font-size: 20rpx;
  color: #666;
}

.product-footer {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
}

.price-section {
  display: flex;
  align-items: baseline;
  gap: 10rpx;
}

.current-price {
  font-size: 32rpx;
  font-weight: 600;
  color: #ff6b9d;
}

.original-price {
  font-size: 24rpx;
  color: #999;
  text-decoration: line-through;
}

.product-actions {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.sales-count {
  font-size: 22rpx;
  color: #999;
}

.add-cart-btn {
  width: 60rpx;
  height: 60rpx;
  background: linear-gradient(135deg, #ff6b9d, #ff8fab);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 157, 0.3);
}

.cart-icon {
  font-size: 24rpx;
  color: white;
}

/* 加载状态 */
.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #f0f0f0;
  border-top: 4rpx solid #ff6b9d;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.load-more {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx;
}

.loading-text {
  font-size: 26rpx;
  color: #666;
  margin-top: 20rpx;
}

.no-more {
  text-align: center;
  padding: 40rpx;
}

.no-more-text {
  font-size: 26rpx;
  color: #999;
}

/* 空状态 */
.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
}

.empty-container {
  text-align: center;
  padding: 0 40rpx;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 40rpx;
  opacity: 0.6;
}

.empty-title {
  display: block;
  font-size: 32rpx;
  color: #666;
  font-weight: 500;
  margin-bottom: 15rpx;
}

.empty-desc {
  display: block;
  font-size: 26rpx;
  color: #999;
  margin-bottom: 40rpx;
}

.empty-suggestions {
  text-align: center;
}

.suggestion-label {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 20rpx;
  display: block;
}

.suggestion-keywords {
  display: flex;
  justify-content: center;
  gap: 15rpx;
  flex-wrap: wrap;
}
