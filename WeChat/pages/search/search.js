// pages/search/search.js
const app = getApp()

Page({
  data: {
    keyword: '', // 搜索关键词
    searchHistory: [], // 搜索历史
    hotKeywords: [], // 热门搜索
    searchResults: [], // 搜索结果
    isSearching: false, // 是否正在搜索
    hasSearched: false, // 是否已经搜索过
    isEmpty: false, // 搜索结果是否为空
    currentPage: 1, // 当前页码
    pageSize: 10, // 每页大小
    hasMore: true, // 是否还有更多数据
    isLoading: false, // 是否正在加载
    showHistory: true, // 是否显示搜索历史
    searchTimer: null // 搜索防抖定时器
  },

  onLoad(options) {
    // 如果从其他页面传入了搜索关键词
    if (options.keyword) {
      this.setData({
        keyword: decodeURIComponent(options.keyword)
      })
      this.performSearch()
    }
    this.loadSearchHistory()
    this.loadHotKeywords()
  },

  onShow() {
    // 每次显示页面时重新加载搜索历史
    this.loadSearchHistory()
  },

  onPullDownRefresh() {
    if (this.data.hasSearched) {
      this.setData({
        currentPage: 1,
        hasMore: true
      })
      this.performSearch()
    }
    wx.stopPullDownRefresh()
  },

  onReachBottom() {
    if (this.data.hasMore && !this.data.isLoading && this.data.hasSearched) {
      this.loadMoreResults()
    }
  },

  // 输入框输入事件
  onInput(e) {
    const keyword = e.detail.value
    this.setData({
      keyword: keyword,
      showHistory: keyword.length === 0
    })

    // 清除之前的定时器
    if (this.data.searchTimer) {
      clearTimeout(this.data.searchTimer)
    }

    // 如果输入为空，清空搜索结果
    if (keyword.length === 0) {
      this.setData({
        searchResults: [],
        hasSearched: false,
        isEmpty: false
      })
      return
    }

    // 设置防抖搜索
    const timer = setTimeout(() => {
      this.performSearch()
    }, 500)

    this.setData({
      searchTimer: timer
    })
  },

  // 搜索框确认事件
  onConfirm(e) {
    const keyword = e.detail.value.trim()
    if (keyword) {
      this.setData({
        keyword: keyword
      })
      this.performSearch()
    }
  },

  // 执行搜索
  performSearch() {
    const keyword = this.data.keyword.trim()
    if (!keyword) {
      wx.showToast({
        title: '请输入搜索关键词',
        icon: 'none'
      })
      return
    }

    this.setData({
      isSearching: true,
      showHistory: false,
      currentPage: 1,
      hasMore: true
    })

    // 添加到搜索历史
    this.addToSearchHistory(keyword)

    // 调用搜索API
    app.request({
      url: '/flower/search',
      method: 'GET',
      data: {
        keyword: keyword,
        current: 1,
        size: this.data.pageSize
      }
    }).then(res => {
      const results = res.data.records.map(item => ({
        ...item,
        tagList: item.tags ? item.tags.split(',') : []
      }))

      this.setData({
        searchResults: results,
        hasSearched: true,
        isEmpty: results.length === 0,
        isSearching: false,
        hasMore: this.data.currentPage < res.data.pages
      })
    }).catch(err => {
      console.error('搜索失败', err)
      this.setData({
        isSearching: false,
        isEmpty: true,
        hasSearched: true
      })
    })
  },

  // 加载更多搜索结果
  loadMoreResults() {
    if (this.data.isLoading) return

    this.setData({
      isLoading: true,
      currentPage: this.data.currentPage + 1
    })

    app.request({
      url: '/flower/search',
      method: 'GET',
      data: {
        keyword: this.data.keyword,
        current: this.data.currentPage,
        size: this.data.pageSize
      }
    }).then(res => {
      const newResults = res.data.records.map(item => ({
        ...item,
        tagList: item.tags ? item.tags.split(',') : []
      }))

      this.setData({
        searchResults: [...this.data.searchResults, ...newResults],
        hasMore: this.data.currentPage < res.data.pages,
        isLoading: false
      })
    }).catch(err => {
      console.error('加载更多失败', err)
      this.setData({
        isLoading: false,
        currentPage: this.data.currentPage - 1
      })
    })
  },

  // 加载搜索历史
  loadSearchHistory() {
    try {
      const history = wx.getStorageSync('searchHistory') || []
      this.setData({
        searchHistory: history.slice(0, 10) // 最多显示10条历史记录
      })
    } catch (e) {
      console.error('加载搜索历史失败', e)
    }
  },

  // 加载热门关键词
  loadHotKeywords() {
    app.request({
      url: '/flower/hot-keywords',
      method: 'GET'
    }).then(res => {
      this.setData({
        hotKeywords: res.data || ['玫瑰', '百合', '康乃馨', '向日葵', '郁金香', '满天星']
      })
    }).catch(err => {
      console.error('加载热门关键词失败', err)
      // 使用默认的热门关键词
      this.setData({
        hotKeywords: ['玫瑰', '百合', '康乃馨', '向日葵', '郁金香', '满天星']
      })
    })
  },

  // 添加到搜索历史
  addToSearchHistory(keyword) {
    try {
      let history = wx.getStorageSync('searchHistory') || []

      // 移除重复的关键词
      history = history.filter(item => item !== keyword)

      // 添加到开头
      history.unshift(keyword)

      // 限制历史记录数量
      if (history.length > 20) {
        history = history.slice(0, 20)
      }

      wx.setStorageSync('searchHistory', history)
      this.setData({
        searchHistory: history.slice(0, 10)
      })
    } catch (e) {
      console.error('保存搜索历史失败', e)
    }
  },

  // 点击搜索历史
  onHistoryTap(e) {
    const keyword = e.currentTarget.dataset.keyword
    this.setData({
      keyword: keyword
    })
    this.performSearch()
  },

  // 点击热门搜索
  onHotKeywordTap(e) {
    const keyword = e.currentTarget.dataset.keyword
    this.setData({
      keyword: keyword
    })
    this.performSearch()
  },

  // 清空搜索历史
  clearSearchHistory() {
    wx.showModal({
      title: '提示',
      content: '确定要清空搜索历史吗？',
      success: (res) => {
        if (res.confirm) {
          try {
            wx.removeStorageSync('searchHistory')
            this.setData({
              searchHistory: []
            })
            wx.showToast({
              title: '已清空',
              icon: 'success'
            })
          } catch (e) {
            wx.showToast({
              title: '清空失败',
              icon: 'none'
            })
          }
        }
      }
    })
  },

  // 删除单个搜索历史
  deleteHistoryItem(e) {
    const keyword = e.currentTarget.dataset.keyword
    try {
      let history = wx.getStorageSync('searchHistory') || []
      history = history.filter(item => item !== keyword)
      wx.setStorageSync('searchHistory', history)
      this.setData({
        searchHistory: history.slice(0, 10)
      })
    } catch (e) {
      console.error('删除搜索历史失败', e)
    }
  },

  // 跳转到商品详情
  goToDetail(e) {
    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/detail/detail?id=${id}`
    })
  },

  // 添加到购物车
  addToCart(e) {
    const id = e.currentTarget.dataset.id

    if (!app.globalData.userId) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
      return
    }

    wx.showLoading({
      title: '添加中...'
    })

    app.request({
      url: '/cart/add',
      method: 'POST',
      data: {
        userId: app.globalData.userId,
        flowerId: id,
        quantity: 1
      }
    }).then(res => {
      wx.hideLoading()
      wx.showToast({
        title: '已添加到购物车',
        icon: 'success'
      })
      app.addToCartWithBadge()
    }).catch(err => {
      wx.hideLoading()
      console.error('添加到购物车失败', err)
    })
  },

  // 返回上一页
  goBack() {
    wx.navigateBack()
  },

  // 清空搜索框
  clearInput() {
    this.setData({
      keyword: '',
      searchResults: [],
      hasSearched: false,
      isEmpty: false,
      showHistory: true
    })
  }
})