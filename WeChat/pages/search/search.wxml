<!-- pages/search/search.wxml -->
<view class="search-container">
  <!-- 搜索头部 -->
  <view class="search-header">
    <view class="search-bar">
      <view class="back-btn" bindtap="goBack">
        <text class="back-icon">←</text>
      </view>
      <view class="search-input-wrapper">
        <input
          class="search-input"
          placeholder="搜索你喜欢的花卉"
          value="{{keyword}}"
          bindinput="onInput"
          bindconfirm="onConfirm"
          focus="{{!hasSearched}}"
          confirm-type="search"
        />
        <view class="clear-btn" wx:if="{{keyword}}" bindtap="clearInput">
          <text class="clear-icon">×</text>
        </view>
      </view>
      <view class="search-btn" bindtap="performSearch">
        <text class="search-text">搜索</text>
      </view>
    </view>
  </view>

  <!-- 搜索建议区域 -->
  <view class="search-suggestions" wx:if="{{showHistory}}">
    <!-- 热门搜索 -->
    <view class="suggestion-section">
      <view class="section-header">
        <text class="section-title">🔥 热门搜索</text>
      </view>
      <view class="hot-keywords">
        <view class="keyword-tag hot-tag"
              wx:for="{{hotKeywords}}"
              wx:key="index"
              bindtap="onHotKeywordTap"
              data-keyword="{{item}}">
          <text class="keyword-text">{{item}}</text>
        </view>
      </view>
    </view>

    <!-- 搜索历史 -->
    <view class="suggestion-section" wx:if="{{searchHistory.length > 0}}">
      <view class="section-header">
        <text class="section-title">🕒 搜索历史</text>
        <view class="clear-history-btn" bindtap="clearSearchHistory">
          <text class="clear-text">清空</text>
        </view>
      </view>
      <view class="history-list">
        <view class="history-item"
              wx:for="{{searchHistory}}"
              wx:key="index">
          <view class="history-content"
                bindtap="onHistoryTap"
                data-keyword="{{item}}">
            <text class="history-icon">🕒</text>
            <text class="history-text">{{item}}</text>
          </view>
          <view class="delete-history-btn"
                bindtap="deleteHistoryItem"
                data-keyword="{{item}}">
            <text class="delete-icon">×</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 搜索中状态 -->
  <view class="searching-state" wx:if="{{isSearching}}">
    <view class="searching-container">
      <view class="loading-spinner"></view>
      <text class="searching-text">正在搜索...</text>
    </view>
  </view>

  <!-- 搜索结果 -->
  <view class="search-results" wx:elif="{{hasSearched}}">
    <!-- 搜索结果头部 -->
    <view class="results-header" wx:if="{{!isEmpty}}">
      <text class="results-count">找到 {{searchResults.length}} 个相关商品</text>
    </view>

    <!-- 商品列表 -->
    <view class="products-list" wx:if="{{!isEmpty}}">
      <view class="product-item"
            wx:for="{{searchResults}}"
            wx:key="id"
            bindtap="goToDetail"
            data-id="{{item.id}}">
        <image class="product-image" src="{{item.mainImage}}" mode="aspectFill" />
        <view class="product-info">
          <text class="product-name">{{item.name}}</text>
          <text class="product-desc">{{item.description}}</text>
          <view class="product-tags" wx:if="{{item.tagList.length > 0}}">
            <text class="product-tag" wx:for="{{item.tagList}}" wx:key="index" wx:for-item="tag">{{tag}}</text>
          </view>
          <view class="product-footer">
            <view class="price-section">
              <text class="current-price">¥{{item.price}}</text>
              <text class="original-price" wx:if="{{item.originalPrice}}">¥{{item.originalPrice}}</text>
            </view>
            <view class="product-actions">
              <text class="sales-count">已售{{item.salesCount}}件</text>
              <view class="add-cart-btn" bindtap="addToCart" data-id="{{item.id}}" catchtap="true">
                <text class="cart-icon">🛒</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 加载更多 -->
    <view class="load-more" wx:if="{{isLoading}}">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 没有更多 -->
    <view class="no-more" wx:if="{{!hasMore && searchResults.length > 0}}">
      <text class="no-more-text">没有更多商品了</text>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{isEmpty}}">
      <view class="empty-container">
        <view class="empty-icon">🔍</view>
        <text class="empty-title">没有找到相关商品</text>
        <text class="empty-desc">试试其他关键词吧</text>
        <view class="empty-suggestions">
          <text class="suggestion-label">推荐搜索：</text>
          <view class="suggestion-keywords">
            <view class="keyword-tag suggestion-tag"
                  wx:for="{{hotKeywords.slice(0, 3)}}"
                  wx:key="index"
                  bindtap="onHotKeywordTap"
                  data-keyword="{{item}}">
              <text class="keyword-text">{{item}}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>