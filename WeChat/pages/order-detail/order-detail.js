// pages/order-detail/order-detail.js
const app = getApp()

Page({
  data: {
    orderId: null,
    order: {},
    orderItems: [],
    statusText: {
      1: '待付款',
      2: '已下单',
      3: '配送中/取货中',
      4: '已完成',
      5: '已取消'
    },
    deliveryTypeText: {
      1: '外卖配送',
      2: '到店自取'
    },
    // 时间标签文本
    timeLabels: {
      delivery: '收货日期',
      pickup: '取货日期',
      actualDelivery: '实际收货时间',
      actualPickup: '实际取货时间'
    }
  },

  onLoad(options) {
    if (options.id) {
      this.setData({
        orderId: options.id
      })
      this.loadOrderDetail()
    }
  },

  onShow() {
    // 每次显示页面时刷新订单状态
    if (this.data.orderId) {
      this.loadOrderDetail()
    }
  },

  onPullDownRefresh() {
    this.loadOrderDetail()
    wx.stopPullDownRefresh()
  },

  // 加载订单详情
  loadOrderDetail() {
    wx.showLoading({
      title: '加载中...'
    })

    Promise.all([
      this.getOrderDetail(),
      this.getOrderItems()
    ]).then(() => {
      wx.hideLoading()
    }).catch(err => {
      wx.hideLoading()
      console.error('加载订单详情失败', err)
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      })
    })
  },

  // 获取订单详情
  getOrderDetail() {
    return app.request({
      url: `/order/detail/${this.data.orderId}`,
      method: 'GET'
    }).then(res => {
      const order = res.data

      // 格式化时间显示
      if (order.createdAt) {
        order.createdAt = this.formatDateTime(order.createdAt)
      }
      if (order.paidAt) {
        order.paidAt = this.formatDateTime(order.paidAt)
      }
      if (order.deliveredAt) {
        order.deliveredAt = this.formatDateTime(order.deliveredAt)
      }
      if (order.shippedAt) {
        order.shippedAt = this.formatDateTime(order.shippedAt)
      }

      // deliveryTime和pickupTime是用户选择的时间，通常已经是格式化的字符串，不需要再次格式化

      // 计算完成时间的标签文本
      const completionTimeLabel = order.deliveryType === 1 ? '实际收货时间：' : '实际取货时间：'

      this.setData({
        order: order,
        completionTimeLabel: completionTimeLabel
      })

      // 设置页面标题
      wx.setNavigationBarTitle({
        title: `订单详情 - ${this.data.statusText[order.status]}`
      })
    })
  },

  // 格式化日期时间
  formatDateTime(dateTimeStr) {
    if (!dateTimeStr) return ''

    try {
      const date = new Date(dateTimeStr)
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')
      const seconds = String(date.getSeconds()).padStart(2, '0')

      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
    } catch (error) {
      console.error('时间格式化失败:', error)
      return dateTimeStr
    }
  },

  // 获取完成时间的标签文本
  getCompletionTimeLabel() {
    const order = this.data.order
    if (!order || !order.deliveredAt) return ''

    return order.deliveryType === 1 ? '收货时间：' : '取货时间：'
  },

  // 获取订单商品
  getOrderItems() {
    return app.request({
      url: `/order/items/${this.data.orderId}`,
      method: 'GET'
    }).then(res => {
      this.setData({
        orderItems: res.data || []
      })
    })
  },

  // 取消订单
  cancelOrder() {
    wx.showModal({
      title: '确认取消',
      content: '确定要取消这个订单吗？',
      success: (res) => {
        if (res.confirm) {
          wx.showLoading({
            title: '取消中...'
          })

          app.request({
            url: '/order/cancel',
            method: 'PUT',
            data: {
              orderId: this.data.orderId,
              userId: app.globalData.userId
            }
          }).then(res => {
            wx.hideLoading()
            wx.showToast({
              title: '订单已取消',
              icon: 'success'
            })
            this.loadOrderDetail()
          }).catch(err => {
            wx.hideLoading()
            console.error('取消订单失败', err)
            wx.showToast({
              title: '取消失败',
              icon: 'none'
            })
          })
        }
      }
    })
  },

  // 确认收货
  confirmReceive() {
    wx.showModal({
      title: '确认收货',
      content: '确认已收到商品吗？',
      success: (res) => {
        if (res.confirm) {
          wx.showLoading({
            title: '确认中...'
          })

          app.request({
            url: '/order/status',
            method: 'PUT',
            data: {
              orderId: this.data.orderId,
              status: 4 // 已完成
            }
          }).then(res => {
            wx.hideLoading()
            wx.showToast({
              title: '确认收货成功',
              icon: 'success'
            })
            this.loadOrderDetail()
          }).catch(err => {
            wx.hideLoading()
            console.error('确认收货失败', err)
            wx.showToast({
              title: '操作失败',
              icon: 'none'
            })
          })
        }
      }
    })
  },

  // 去评价
  goToReview() {
    wx.navigateTo({
      url: `/pages/review/review?orderId=${this.data.orderId}`
    })
  },

  // 再次购买
  buyAgain() {
    const orderData = {
      type: 'cart',
      items: this.data.orderItems.map(item => ({
        flowerId: item.flowerId,
        quantity: item.quantity,
        flower: {
          id: item.flowerId,
          name: item.flowerName,
          price: item.price,
          mainImage: item.flowerImage
        }
      }))
    }

    wx.navigateTo({
      url: `/pages/order-confirm/order-confirm?data=${encodeURIComponent(JSON.stringify(orderData))}`
    })
  },

  // 联系客服
  contactService() {
    wx.showModal({
      title: '联系客服',
      content: '客服电话：400-123-4567\n工作时间：9:00-18:00',
      showCancel: false,
      confirmText: '知道了'
    })
  },

  // 复制订单号
  copyOrderNo() {
    wx.setClipboardData({
      data: this.data.order.orderNo,
      success: () => {
        wx.showToast({
          title: '订单号已复制',
          icon: 'success'
        })
      }
    })
  },

  // 返回上一页
  goBack() {
    wx.navigateBack()
  }
})