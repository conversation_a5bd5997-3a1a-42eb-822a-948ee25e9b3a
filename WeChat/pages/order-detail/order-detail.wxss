/* pages/order-detail/order-detail.wxss */

/* 页面容器 */
.container {
  min-height: 100vh;
  background: #f8f8f8;
  display: flex;
  flex-direction: column;
}

/* 头部 */
.header {
  background: white;
  padding: 20rpx;
  display: flex;
  align-items: center;
  border-bottom: 1rpx solid #f0f0f0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.back-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: #f5f5f5;
  margin-right: 20rpx;
}

.back-icon {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
}

.header-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

/* 内容区域 */
.content {
  flex: 1;
  padding-bottom: 120rpx;
}

/* 订单状态 */
.status-section {
  background: linear-gradient(135deg, #ff6b9d, #ff8fab);
  padding: 40rpx 30rpx;
  display: flex;
  align-items: center;
  color: white;
}

.status-icon {
  width: 80rpx;
  height: 80rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 30rpx;
}

.status-emoji {
  font-size: 40rpx;
}

.status-info {
  flex: 1;
}

.status-text {
  font-size: 32rpx;
  font-weight: 600;
  display: block;
  margin-bottom: 10rpx;
}

.status-desc {
  font-size: 26rpx;
  opacity: 0.9;
  display: block;
}

/* 通用区块 */
.section {
  background: white;
  margin-bottom: 20rpx;
  padding: 30rpx;
}

.section-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
  padding-bottom: 15rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

/* 配送信息 */
.delivery-info,
.pickup-info {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.info-row {
  display: flex;
  align-items: flex-start;
}

.info-label {
  font-size: 26rpx;
  color: #666;
  width: 140rpx;
  flex-shrink: 0;
}

.info-value {
  font-size: 26rpx;
  color: #333;
  flex: 1;
  line-height: 1.4;
}

.store-address {
  margin-top: 20rpx;
  padding: 20rpx;
  background: #f8f8f8;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
}

.store-label {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.store-value {
  font-size: 26rpx;
  color: #333;
  line-height: 1.4;
}

/* 商品列表 */
.product-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.product-item {
  display: flex;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.product-item:last-child {
  border-bottom: none;
}

.product-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.product-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
}

.product-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.product-price {
  font-size: 28rpx;
  font-weight: 600;
  color: #ff6b9d;
}

.product-quantity {
  font-size: 24rpx;
  color: #666;
}

/* 订单信息 */
.order-info {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.order-no {
  font-family: monospace;
  color: #ff6b9d !important;
}

.copy-hint {
  font-size: 22rpx;
  color: #999;
  margin-left: 20rpx;
}

/* 金额信息 */
.amount-info {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.amount-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.amount-row.total {
  padding-top: 15rpx;
  margin-bottom: 30rpx;
  border-top: 1rpx solid #f0f0f0;
}

.amount-label {
  font-size: 26rpx;
  color: #666;
}

.amount-row.total .amount-label {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.amount-value {
  font-size: 26rpx;
  color: #333;
}

.amount-value.discount {
  color: #ff6b9d;
}

.total-price {
  font-size: 32rpx;
  font-weight: 600;
  color: #ff6b9d;
}

/* 底部操作按钮 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 20rpx 30rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
  border-top: 1rpx solid #f0f0f0;
  display: flex;
  gap: 20rpx;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.action-btn {
  flex: 1;
  padding: 25rpx 30rpx;
  border-radius: 50rpx;
  text-align: center;
  font-size: 28rpx;
  font-weight: 600;
  transition: all 0.2s ease;
}

.action-btn.primary {
  background: linear-gradient(135deg, #ff6b9d, #ff8fab);
  color: white;
}

.action-btn.secondary {
  background: #f5f5f5;
  color: #666;
  border: 1rpx solid #e0e0e0;
}

.action-btn:active {
  transform: scale(0.95);
}

.btn-text {
  font-size: 28rpx;
}
