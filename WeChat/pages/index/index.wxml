<!-- pages/index/index.wxml -->
<view class="container">
  <!-- 搜索栏 -->
  <view class="search-bar" bindtap="goToSearch">
    <view class="search-input">
      <text class="search-icon">🔍</text>
      <text class="search-placeholder">搜索你喜欢的花卉</text>
    </view>
  </view>

  <!-- 轮播图 -->
  <view class="banner-section" wx:if="{{banners.length > 0}}">
    <swiper
      class="banner-swiper"
      indicator-dots="true"
      autoplay="true"
      interval="3000"
      duration="500"
      circular="true"
      current="{{swiperCurrent}}"
      bindchange="onSwiperChange"
    >
      <swiper-item wx:for="{{banners}}" wx:key="id" data-index="{{index}}" bindtap="onBannerTap">
        <image class="banner-image" src="{{item.image}}" mode="aspectFill" />
        <view class="banner-title" wx:if="{{item.title}}">{{item.title}}</view>
      </swiper-item>
    </swiper>
  </view>

  <!-- 分类导航 -->
  <view class="category-section card">
    <view class="section-title">花卉分类</view>
    <view class="category-grid">
      <view class="category-item" wx:for="{{categories}}" wx:key="id" bindtap="goToCategory" data-id="{{item.id}}">
        <image class="category-icon" src="{{item.imageUrl}}" mode="aspectFill" />
        <text class="category-name">{{item.name}}</text>
      </view>
    </view>
  </view>

  <!-- 精选推荐 -->
  <view class="featured-section">
    <view class="section-header">
      <view class="section-title-wrapper">
        <text class="section-title">✨ 精选推荐</text>
        <text class="section-subtitle">为你精心挑选的优质花卉</text>
      </view>
      <text class="section-more" bindtap="goToCategory">更多 ></text>
    </view>
    <view class="featured-grid">
      <view class="featured-item card" wx:for="{{featuredFlowers}}" wx:key="id" bindtap="goToDetail" data-id="{{item.id}}">
        <view class="featured-image-wrapper">
          <image class="featured-image" src="{{item.mainImage}}" mode="aspectFill" />
          <view class="featured-badge">精选</view>
        </view>
        <view class="featured-info">
          <text class="featured-name">{{item.name}}</text>
          <text class="featured-desc">{{item.description}}</text>
          <view class="featured-price">
            <text class="price price-medium">¥{{item.price}}</text>
            <text class="original-price" wx:if="{{item.originalPrice}}">¥{{item.originalPrice}}</text>
          </view>
          <view class="featured-tags">
            <text class="tag tag-primary" wx:for="{{item.tagList}}" wx:key="index" wx:for-item="tag">{{tag}}</text>
          </view>
          <view class="featured-actions">
            <text class="sales-count">已售{{item.salesCount}}件</text>
            <view class="add-cart-btn" bindtap="addToCart" data-id="{{item.id}}" catchtap="true">
              <text class="cart-icon">🛒</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 热销商品 -->
  <view class="hot-section">
    <view class="section-header">
      <text class="section-title">热销商品</text>
      <text class="section-more" bindtap="goToCategory">更多 ></text>
    </view>
    <view class="flower-grid">
      <view class="flower-item card" wx:for="{{hotFlowers}}" wx:key="id" bindtap="goToDetail" data-id="{{item.id}}">
        <image class="flower-image" src="{{item.mainImage}}" mode="aspectFill" />
        <view class="flower-info">
          <text class="flower-name">{{item.name}}</text>
          <text class="flower-desc">{{item.description}}</text>
          <view class="flower-price">
            <text class="price price-medium">¥{{item.price}}</text>
            <text class="original-price" wx:if="{{item.originalPrice}}">¥{{item.originalPrice}}</text>
          </view>
          <view class="flower-tags">
            <text class="tag tag-primary" wx:for="{{item.tagList}}" wx:key="index" wx:for-item="tag">{{tag}}</text>
          </view>
          <view class="flower-actions">
            <text class="sales-count">已售{{item.salesCount}}件</text>
            <view class="add-cart-btn" bindtap="addToCart" data-id="{{item.id}}" catchtap="true">
              <text class="cart-icon">🛒</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 加载更多 -->
  <view class="load-more" wx:if="{{hasMore}}">
    <text class="loading-text">加载更多...</text>
  </view>
</view>
