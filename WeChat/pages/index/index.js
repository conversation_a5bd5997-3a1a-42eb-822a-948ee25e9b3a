// pages/index/index.js
const app = getApp()

Page({
  data: {
    banners: [],
    categories: [],
    featuredFlowers: [],
    hotFlowers: [],
    hasMore: true,
    currentPage: 1,
    pageSize: 10,
    swiperCurrent: 0
  },

  onLoad() {
    this.loadData()
  },

  onShow() {
    // 更新购物车数量
    app.updateCartCount()
  },

  onPullDownRefresh() {
    this.setData({
      currentPage: 1,
      hasMore: true
    })
    this.loadData()
    wx.stopPullDownRefresh()
  },

  onReachBottom() {
    if (this.data.hasMore) {
      this.loadMoreFlowers()
    }
  },

  // 加载数据
  loadData() {
    this.loadBanners()
    this.loadCategories()
    this.loadFeaturedFlowers()
    this.loadHotFlowers()
  },

  // 加载轮播图
  loadBanners() {
    app.request({
      url: '/swiper/active',
      method: 'GET'
    }).then(res => {
      console.log('轮播图数据:', res.data)
      this.setData({
        banners: res.data.map(item => ({
          id: item.id,
          image: item.imageUrl,
          title: item.title,
          linkUrl: item.linkUrl
        }))
      })
    }).catch(err => {
      console.error('加载轮播图失败', err)
      // 如果加载失败，使用默认轮播图
      this.setData({
        banners: [
          {
            id: 1,
            image: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=800&h=400&fit=crop',
            title: '精美花卉',
            linkUrl: ''
          },
          {
            id: 2,
            image: 'https://images.unsplash.com/photo-1563241527-3004b7be0ffd?w=800&h=400&fit=crop',
            title: '鲜花配送',
            linkUrl: ''
          },
          {
            id: 3,
            image: 'https://images.unsplash.com/photo-1490750967868-88aa4486c946?w=800&h=400&fit=crop',
            title: '花卉养护',
            linkUrl: ''
          }
        ]
      })
    })
  },

  // 加载分类
  loadCategories() {
    app.request({
      url: '/flower/categories',
      method: 'GET'
    }).then(res => {
      this.setData({
        categories: res.data.slice(0, 8) // 只显示前8个分类
      })
    }).catch(err => {
      console.error('加载分类失败', err)
    })
  },

  // 加载精选花卉
  loadFeaturedFlowers() {
    app.request({
      url: '/flower/featured',
      method: 'GET',
      data: {
        limit: 10
        // 首页不需要价格筛选，所以不传递价格参数
      }
    }).then(res => {
      const flowers = res.data.map(item => ({
        ...item,
        tagList: item.tags ? item.tags.split(',') : []
      }))
      this.setData({
        featuredFlowers: flowers
      })
    }).catch(err => {
      console.error('加载精选花卉失败', err)
    })
  },

  // 加载热销花卉
  loadHotFlowers() {
    app.request({
      url: '/flower/list',
      method: 'GET',
      data: {
        current: this.data.currentPage,
        size: this.data.pageSize
      }
    }).then(res => {
      const flowers = res.data.records.map(item => ({
        ...item,
        tagList: item.tags ? item.tags.split(',') : []
      }))
      
      if (this.data.currentPage === 1) {
        this.setData({
          hotFlowers: flowers
        })
      } else {
        this.setData({
          hotFlowers: [...this.data.hotFlowers, ...flowers]
        })
      }
      
      this.setData({
        hasMore: this.data.currentPage < res.data.pages
      })
    }).catch(err => {
      console.error('加载热销花卉失败', err)
    })
  },

  // 加载更多花卉
  loadMoreFlowers() {
    this.setData({
      currentPage: this.data.currentPage + 1
    })
    this.loadHotFlowers()
  },

  // 轮播图滑动事件
  onSwiperChange(e) {
    this.setData({
      swiperCurrent: e.detail.current
    })
  },

  // 轮播图点击事件
  onBannerTap(e) {
    const index = e.currentTarget.dataset.index
    const banner = this.data.banners[index]

    if (banner && banner.linkUrl) {
      // 如果有跳转链接，可以根据需要处理跳转逻辑
      console.log('轮播图点击:', banner.title, banner.linkUrl)
      // 这里可以添加具体的跳转逻辑，比如跳转到商品详情页等
    }
  },

  // 跳转到搜索页面
  goToSearch() {
    wx.navigateTo({
      url: '/pages/search/search'
    })
  },

  // 跳转到分类页面
  goToCategory(e) {
    const categoryId = e.currentTarget.dataset.id

    if (categoryId) {
      // 找到对应的分类信息
      const category = this.data.categories.find(cat => cat.id === categoryId)
      if (category) {
        wx.navigateTo({
          url: `/pages/category-products/category-products?categoryId=${categoryId}&categoryName=${encodeURIComponent(category.name)}`
        })
      }
    } else {
      wx.switchTab({
        url: '/pages/category/category'
      })
    }
  },

  // 跳转到详情页面
  goToDetail(e) {
    const flowerId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/detail/detail?id=${flowerId}`
    })
  },

  // 添加到购物车
  addToCart(e) {
    const flowerId = e.currentTarget.dataset.id
    
    // 检查登录状态
    if (!app.globalData.userId) {
      this.showLoginModal()
      return
    }

    app.request({
      url: '/cart/add',
      method: 'POST',
      data: {
        userId: app.globalData.userId,
        flowerId: flowerId,
        quantity: 1
      }
    }).then(res => {
      wx.showToast({
        title: '已添加到购物车',
        icon: 'success'
      })
      // 使用新的方法更新购物车徽章
      app.addToCartWithBadge()
    }).catch(err => {
      console.error('添加购物车失败', err)
    })
  },

  // 切换收藏状态
  toggleFavorite(e) {
    const flowerId = e.currentTarget.dataset.id

    // 检查登录状态
    if (!app.globalData.userId) {
      this.showLoginPrompt()
      return
    }

    // 调用收藏/取消收藏的API
    app.request({
      url: '/favorite/toggle',
      method: 'POST',
      data: {
        userId: app.globalData.userId,
        flowerId: flowerId
      }
    }).then(res => {
      wx.showToast({
        title: res.data ? '已添加到收藏' : '已取消收藏',
        icon: 'success'
      })
    }).catch(err => {
      console.error('收藏操作失败', err)
      wx.showToast({
        title: '操作失败，请重试',
        icon: 'none'
      })
    })
  },

  // 显示登录提示
  showLoginPrompt() {
    wx.showModal({
      title: '提示',
      content: '请先登录后再进行操作',
      confirmText: '去登录',
      success: (res) => {
        if (res.confirm) {
          wx.navigateTo({
            url: '/pages/login/login'
          })
        }
      }
    })
  },

  // 显示登录弹窗
  showLoginModal() {
    wx.showModal({
      title: '提示',
      content: '请先登录后再进行操作',
      confirmText: '去登录',
      success: (res) => {
        if (res.confirm) {
          wx.navigateTo({
            url: '/pages/login/login'
          })
        }
      }
    })
  }
})
