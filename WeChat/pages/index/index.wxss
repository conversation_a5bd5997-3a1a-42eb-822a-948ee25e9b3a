/* pages/index/index.wxss */

/* 搜索栏 */
.search-bar {
  margin-bottom: 20rpx;
}

.search-input {
  background: white;
  border-radius: 50rpx;
  padding: 20rpx 30rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.search-icon {
  margin-right: 20rpx;
  font-size: 28rpx;
  color: #999;
}

.search-placeholder {
  color: #999;
  font-size: 28rpx;
}

/* 轮播图 */
.banner-section {
  margin-bottom: 30rpx;
}

.banner-swiper {
  height: 300rpx;
  border-radius: 16rpx;
  overflow: hidden;
}

.banner-swiper swiper-item {
  position: relative;
}

.banner-image {
  width: 100%;
  height: 100%;
}

.banner-title {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.6));
  color: white;
  padding: 20rpx 30rpx;
  font-size: 28rpx;
  font-weight: 500;
}

/* 分类导航 */
.category-section {
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 30rpx;
}

.category-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 30rpx;
}

.category-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.category-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-bottom: 15rpx;
}

.category-name {
  font-size: 24rpx;
  color: #666;
}

/* 区块头部 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  padding: 0 20rpx;
  margin-bottom: 25rpx;
}

.section-title-wrapper {
  display: flex;
  flex-direction: column;
}

.section-subtitle {
  font-size: 22rpx;
  color: #999;
  margin-top: 5rpx;
  font-weight: 400;
}

.section-more {
  font-size: 24rpx;
  color: #ff6b9d;
  font-weight: 500;
}

/* 精选推荐 */
.featured-section {
  margin-bottom: 30rpx;
}

.featured-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
  padding: 0 20rpx;
}

.featured-item {
  padding: 0;
  overflow: hidden;
  position: relative;
}

.featured-image-wrapper {
  position: relative;
  overflow: hidden;
}

.featured-image {
  width: 100%;
  height: 200rpx;
}

.featured-badge {
  position: absolute;
  top: 12rpx;
  left: 12rpx;
  background: linear-gradient(135deg, #ff6b9d, #ff8fab);
  color: white;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 18rpx;
  font-weight: 500;
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 157, 0.3);
  z-index: 2;
}

.featured-info {
  padding: 20rpx;
}

.featured-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  display: block;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.featured-desc {
  font-size: 22rpx;
  color: #999;
  display: block;
  margin-bottom: 12rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.featured-price {
  display: flex;
  align-items: center;
  gap: 10rpx;
  margin-bottom: 12rpx;
}

.featured-tags {
  margin-bottom: 15rpx;
  min-height: 40rpx;
}

.featured-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.featured-section .sales-count {
  font-size: 20rpx;
  color: #999;
}

.featured-section .add-cart-btn {
  width: 60rpx;
  height: 60rpx;
  background: linear-gradient(135deg, #ff6b9d, #ff8fab);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 157, 0.3);
}

.featured-section .cart-icon {
  color: white;
  font-size: 24rpx;
}

/* 热销商品 */
.hot-section {
  margin-bottom: 30rpx;
}

.flower-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
  padding: 0 20rpx;
}

.flower-item {
  padding: 0;
  overflow: hidden;
}

.flower-image {
  width: 100%;
  height: 200rpx;
}

.flower-info {
  padding: 20rpx;
}

.flower-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  display: block;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.flower-desc {
  font-size: 22rpx;
  color: #999;
  display: block;
  margin-bottom: 12rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.flower-price {
  display: flex;
  align-items: center;
  gap: 10rpx;
  margin-bottom: 12rpx;
}

.flower-tags {
  margin-bottom: 15rpx;
  min-height: 40rpx;
}

.flower-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.sales-count {
  font-size: 20rpx;
  color: #999;
}

.add-cart-btn {
  width: 60rpx;
  height: 60rpx;
  background: linear-gradient(135deg, #ff6b9d, #ff8fab);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 157, 0.3);
}

.cart-icon {
  color: white;
  font-size: 24rpx;
}

/* 加载更多 */
.load-more {
  text-align: center;
  padding: 40rpx;
}

.loading-text {
  color: #999;
  font-size: 24rpx;
}
