/* pages/category/category.wxss */

/* 搜索栏 */
.search-bar {
  padding: 20rpx;
  background: white;
}

.search-input {
  background: #f8f8f8;
  border-radius: 50rpx;
  padding: 20rpx 30rpx;
  display: flex;
  align-items: center;
}

.search-icon {
  margin-right: 20rpx;
  font-size: 28rpx;
  color: #999;
}

.search-placeholder {
  color: #999;
  font-size: 28rpx;
}

/* 主要内容 */
.content {
  display: flex;
  height: calc(100vh - 140rpx); /* 减去搜索栏和tabbar高度 */
}

/* 分类侧边栏 */
.category-sidebar {
  width: 180rpx;
  background: linear-gradient(180deg, #fafbfc 0%, #f5f6f7 100%);
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  border-right: 1rpx solid rgba(255, 107, 157, 0.1);
  box-shadow: 2rpx 0 8rpx rgba(0, 0, 0, 0.05);
  position: relative;
  z-index: 10;
}

/* 模式切换按钮 */
.mode-switch {
  display: flex;
  background: #f8f9fa;
  margin: 15rpx 10rpx;
  border-radius: 20rpx;
  padding: 6rpx;
  position: relative;
  box-shadow: 0 2rpx 12rpx rgba(255, 107, 157, 0.15);
  border: 1rpx solid rgba(255, 107, 157, 0.1);
}

.switch-item {
  flex: 1;
  padding: 20rpx 12rpx;
  text-align: center;
  border-radius: 16rpx;
  transition: background 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  z-index: 2;
  /* 固定高度防止抖动 */
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.switch-item.active {
  background: linear-gradient(135deg, #e8548fff, #eb4d82ff);
  color: white !important;
  box-shadow: 0 4rpx 12rpx rgba(225, 74, 135, 0.5);
  transform: translateY(-1rpx);
}

.switch-text {
  font-size: 24rpx;
  font-weight: 600;
  transition: color 0.3s ease, text-shadow 0.3s ease;
  line-height: 1.3;
  /* 固定字体大小和粗细防止抖动 */
}

.switch-item:not(.active) .switch-text {
  color: #666;
  /* 保持相同的字体粗细 */
  font-weight: 600;
}

.switch-item.active .switch-text {
  color: #ffffff !important;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
  /* 保持相同的字体粗细和大小 */
  font-weight: 600;
  font-size: 24rpx;
}

.category-item {
  padding: 28rpx 20rpx;
  text-align: center;
  position: relative;
  margin: 8rpx 12rpx;
  border-radius: 16rpx;
  transition: background 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
}

.category-item:hover {
  background: rgba(76, 175, 80, 0.05);
  /* 移除 transform 防止抖动 */
}

.category-item.active {
  background: linear-gradient(135deg, #ff6b9d, #eb5279ff);
  color: white !important;
  box-shadow: 0 6rpx 20rpx rgba(76, 175, 80, 0.4);
  /* 移除 transform 防止抖动 */
}

.category-item.active::before {
  content: '';
  position: absolute;
  left: -12rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 6rpx;
  height: 40rpx;
  background: linear-gradient(135deg, #c2185b, #e91e63);
  border-radius: 3rpx;
  box-shadow: 0 2rpx 8rpx rgba(194, 24, 91, 0.5);
}

.category-name {
  font-size: 24rpx;
  color: #333;
  font-weight: 600;
  line-height: 1.4;
  transition: all 0.3s ease;
}

.category-item.active .category-name {
  color: white !important;
  /* 保持相同的字体粗细和大小防止抖动 */
  font-weight: 600;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
  font-size: 24rpx;
}

/* 商品内容区 */
.product-content {
  flex: 1;
  background: white;
  overflow-y: auto;
}

/* 分类头部 */
.category-header {
  padding: 25rpx;
  display: flex;
  align-items: center;
  gap: 15rpx;
  border-bottom: 1rpx solid #f0f0f0;
  min-height: 130rpx;
  box-sizing: border-box;
}

.category-image {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
}

.category-info {
  flex: 1;
}

.category-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.category-desc {
  font-size: 22rpx;
  color: #999;
  line-height: 1.4;
}

/* 区域标题 */
.section-header {
  padding: 25rpx 20rpx 15rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 5rpx;
}

.section-subtitle {
  font-size: 22rpx;
  color: #999;
}

/* 精选商品区域 */
.featured-section {
  margin-bottom: 20rpx;
}

.featured-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15rpx;
  padding: 15rpx;
}

.featured-item {
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(255, 107, 157, 0.15);
  border: 2rpx solid rgba(255, 107, 157, 0.1);
  position: relative;
}

.featured-image {
  width: 100%;
  height: 200rpx;
}

.featured-badge {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  background: linear-gradient(135deg, #ff6b9d, #ff8fab);
  color: white;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  font-size: 18rpx;
  font-weight: 500;
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 157, 0.3);
}

.featured-info {
  padding: 20rpx;
}

.featured-name {
  font-size: 26rpx;
  color: #333;
  font-weight: 600;
  display: block;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.featured-desc {
  font-size: 20rpx;
  color: #999;
  display: block;
  margin-bottom: 12rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.featured-price {
  display: flex;
  align-items: center;
  gap: 8rpx;
  margin-bottom: 12rpx;
}

.featured-tags {
  margin-bottom: 15rpx;
  min-height: 30rpx;
}

.tag-featured {
  background: rgba(255, 107, 157, 0.15);
  color: #ff6b9d;
  font-weight: 500;
}

.featured-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 全部商品区域 */
.all-products-section {
  background: #fafafa;
  border-radius: 20rpx 20rpx 0 0;
  margin-top: 10rpx;
}

.all-products-section .section-header {
  background: white;
  border-radius: 20rpx 20rpx 0 0;
}

/* 商品网格 */
.product-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15rpx;
  padding: 15rpx;
  background: #fafafa;
}

.product-item {
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.product-image {
  width: 100%;
  height: 200rpx;
}

.product-info {
  padding: 20rpx;
}

.product-name {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
  display: block;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-desc {
  font-size: 20rpx;
  color: #999;
  display: block;
  margin-bottom: 12rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-price {
  display: flex;
  align-items: center;
  gap: 8rpx;
  margin-bottom: 12rpx;
}

.product-tags {
  margin-bottom: 15rpx;
  min-height: 30rpx;
}

.tag {
  display: inline-block;
  padding: 4rpx 8rpx;
  background: rgba(255, 107, 157, 0.1);
  color: #ff6b9d;
  border-radius: 8rpx;
  font-size: 16rpx;
  margin-right: 6rpx;
  margin-bottom: 6rpx;
}

.product-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.sales-count {
  font-size: 18rpx;
  color: #999;
}

.add-cart-btn {
  width: 50rpx;
  height: 50rpx;
  background: linear-gradient(135deg, #ff6b9d, #ff8fab);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 157, 0.3);
}

.cart-icon {
  color: white;
  font-size: 20rpx;
}

/* 加载状态 */
.loading-more {
  text-align: center;
  padding: 40rpx;
}

.loading-text {
  color: #999;
  font-size: 24rpx;
}

.no-more {
  text-align: center;
  padding: 40rpx;
}

.no-more-text {
  color: #999;
  font-size: 24rpx;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 100rpx 40rpx;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 20rpx;
  color: #ddd;
}

.empty-text {
  display: block;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.empty-desc {
  display: block;
  font-size: 24rpx;
  color: #999;
}

/* 分类列表 */
.category-list {
  flex: 1;
  overflow-y: auto;
  padding: 10rpx 0;
  -webkit-overflow-scrolling: touch;
}

.category-list::-webkit-scrollbar {
  width: 4rpx;
}

.category-list::-webkit-scrollbar-track {
  background: transparent;
}

.category-list::-webkit-scrollbar-thumb {
  background: rgba(255, 107, 157, 0.3);
  border-radius: 2rpx;
}

/* 价格分类列表 */
.price-category-list {
  flex: 1;
  overflow-y: auto;
  padding: 10rpx 0;
  -webkit-overflow-scrolling: touch;
}

.price-category-list::-webkit-scrollbar {
  width: 4rpx;
}

.price-category-list::-webkit-scrollbar-track {
  background: transparent;
}

.price-category-list::-webkit-scrollbar-thumb {
  background: rgba(255, 107, 157, 0.3);
  border-radius: 2rpx;
}

.price-category-item {
  padding: 24rpx 20rpx;
  position: relative;
  margin: 8rpx 12rpx;
  border-radius: 16rpx;
  transition: background 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  text-align: center;
}

.price-category-item:hover {
  background: rgba(76, 175, 80, 0.05);
  /* 移除 transform 防止抖动 */
}

.price-category-item.active {
  background: linear-gradient(135deg, #f2679eff, #f5608dff);
  color: white !important;
  box-shadow: 0 6rpx 20rpx rgba(76, 175, 80, 0.4);
  /* 移除 transform 防止抖动 */
}

.price-category-item.active::before {
  content: '';
  position: absolute;
  left: -12rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 6rpx;
  height: 40rpx;
  background: linear-gradient(135deg, #c2185b, #e91e63);
  border-radius: 3rpx;
  box-shadow: 0 2rpx 8rpx rgba(194, 24, 91, 0.5);
}

.price-category-name {
  font-size: 26rpx;
  display: block;
  margin-bottom: 6rpx;
  font-weight: 600;
  color: #333;
  transition: color 0.3s ease, text-shadow 0.3s ease;
  line-height: 1.3;
}

.price-category-item.active .price-category-name {
  color: #ffffff !important;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
  /* 保持相同的字体粗细和大小防止抖动 */
  font-weight: 600;
  font-size: 26rpx;
}

.price-range {
  font-size: 24rpx;
  color: #666;
  display: block;
  font-weight: 600;
  transition: all 0.3s ease;
  line-height: 1.2;
}

.price-category-item.active .price-range {
  color: #ffffff !important;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
  /* 保持相同的字体大小防止抖动 */
  font-size: 24rpx;
}

/* 价格分类信息头部 */
.price-header {
  padding: 25rpx;
  background: linear-gradient(135deg, #ff6b9d 0%, #ff8fab 50%, #ffa8c5 100%);
  color: white;
  margin-bottom: 20rpx;
  position: relative;
  overflow: hidden;
  min-height: 130rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.price-header::before {
  content: '';
  position: absolute;
  top: -30rpx;
  right: -15rpx;
  width: 100rpx;
  height: 100rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  transform: rotate(45deg);
}

.price-header::after {
  content: '';
  position: absolute;
  bottom: -20rpx;
  left: -20rpx;
  width: 80rpx;
  height: 80rpx;
  background: rgba(255, 255, 255, 0.08);
  border-radius: 50%;
}

.price-info {
  text-align: center;
  position: relative;
  z-index: 2;
}

.price-title {
  font-size: 28rpx;
  font-weight: 700;
  display: block;
  margin-bottom: 6rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
  letter-spacing: 1rpx;
}

.price-desc {
  font-size: 22rpx;
  opacity: 0.95;
  font-weight: 500;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}
