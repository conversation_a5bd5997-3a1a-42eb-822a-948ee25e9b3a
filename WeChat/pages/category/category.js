// pages/category/category.js
const app = getApp()

Page({
  data: {
    categories: [],
    priceCategories: [], // 价格分类
    currentCategoryId: null,
    currentCategory: null,
    selectedPriceCategory: null, // 选中的价格分类
    featuredFlowers: [], // 精选商品
    flowers: [], // 普通商品
    currentPage: 1,
    pageSize: 10,
    hasMore: true,
    isLoading: false,
    featuredLoaded: false, // 精选商品是否已加载
    viewMode: 'category', // 'category' 或 'price' - 当前查看模式
    categoryModeData: {
      categories: [],
      currentCategoryId: null,
      currentCategory: null
    },
    priceModeData: {
      selectedPriceCategory: null
    }
  },

  onLoad(options) {
    this.loadCategories()
    this.loadPriceCategories()

    // 获取传递的分类ID（从URL参数或全局数据）
    let categoryId = null
    if (options.categoryId) {
      categoryId = parseInt(options.categoryId)
    } else if (getApp().globalData.selectedCategoryId) {
      categoryId = getApp().globalData.selectedCategoryId
      // 使用后清除全局数据
      getApp().globalData.selectedCategoryId = null
    }

    if (categoryId) {
      this.setData({
        currentCategoryId: categoryId,
        'categoryModeData.currentCategoryId': categoryId
      })
    }
  },

  onShow() {
    app.updateCartCount()

    // 检查是否有从首页传递的分类ID
    if (getApp().globalData.selectedCategoryId) {
      const categoryId = getApp().globalData.selectedCategoryId
      getApp().globalData.selectedCategoryId = null // 使用后清除

      // 如果当前没有选中分类，或者选中的分类不同，则切换分类
      if (!this.data.currentCategoryId || this.data.currentCategoryId !== categoryId) {
        this.setData({
          currentCategoryId: categoryId
        })

        // 等待分类数据加载完成后再选择分类
        if (this.data.categories.length > 0) {
          this.selectCategoryById(categoryId)
        } else {
          // 如果分类数据还没加载完成，设置一个标记
          this.pendingCategoryId = categoryId
        }
      }
    }
  },

  onPullDownRefresh() {
    this.refreshData()
    wx.stopPullDownRefresh()
  },

  onReachBottom() {
    if (this.data.hasMore && !this.data.isLoading) {
      this.loadMoreFlowers()
    }
  },

  // 加载分类列表
  loadCategories() {
    app.request({
      url: '/flower/categories',
      method: 'GET'
    }).then(res => {
      const categories = res.data || []
      this.setData({
        categories: categories,
        'categoryModeData.categories': categories
      })

      // 如果没有指定分类，选择第一个分类
      if (!this.data.currentCategoryId && categories.length > 0) {
        this.selectCategory({
          currentTarget: {
            dataset: {
              id: categories[0].id
            }
          }
        })
      } else if (this.data.currentCategoryId) {
        // 找到对应的分类并加载商品
        const category = categories.find(cat => cat.id === this.data.currentCategoryId)
        if (category) {
          this.setData({
            currentCategory: category,
            'categoryModeData.currentCategory': category
          })
          this.loadFeaturedFlowers()
        }

        // 检查是否有待处理的分类ID
        if (this.pendingCategoryId) {
          this.selectCategoryById(this.pendingCategoryId)
          this.pendingCategoryId = null
        }
      }
    }).catch(err => {
      console.error('加载分类失败', err)
    })
  },

  // 加载价格分类
  loadPriceCategories() {
    return app.request({
      url: '/flower/price-categories',
      method: 'GET'
    }).then(res => {
      this.setData({
        priceCategories: res.data || []
      })
      return res.data || []
    }).catch(err => {
      console.error('加载价格分类失败', err)
      return []
    })
  },

  // 切换查看模式
  switchViewMode(e) {
    const mode = e.currentTarget.dataset.mode

    if (mode === this.data.viewMode) return

    this.setData({
      viewMode: mode,
      featuredFlowers: [],
      flowers: [],
      featuredLoaded: false,
      currentPage: 1,
      hasMore: true
    })

    if (mode === 'category') {
      // 切换到分类模式
      this.setData({
        currentCategoryId: this.data.categoryModeData.currentCategoryId,
        currentCategory: this.data.categoryModeData.currentCategory,
        selectedPriceCategory: null
      })

      // 如果有选中的分类，加载商品
      if (this.data.currentCategoryId) {
        this.loadFeaturedFlowers()
      } else if (this.data.categories.length > 0) {
        // 如果没有选中分类，选择第一个
        this.selectCategoryById(this.data.categories[0].id)
      }
    } else if (mode === 'price') {
      // 切换到价格模式
      this.setData({
        currentCategoryId: null,
        currentCategory: null,
        selectedPriceCategory: this.data.priceModeData.selectedPriceCategory
      })

      // 总是加载商品，不管是否有选中的价格分类
      // 如果没有选中价格分类，会显示所有商品
      this.loadFeaturedFlowers()
    }
  },

  // 选择分类
  selectCategory(e) {
    const categoryId = e.currentTarget.dataset.id
    this.selectCategoryById(categoryId)
  },

  // 根据ID选择分类
  selectCategoryById(categoryId) {
    const category = this.data.categories.find(cat => cat.id === categoryId)

    if (category) {
      this.setData({
        currentCategoryId: categoryId,
        currentCategory: category,
        currentPage: 1,
        hasMore: true,
        flowers: [],
        featuredFlowers: [],
        featuredLoaded: false,
        selectedPriceCategory: null,
        'categoryModeData.currentCategoryId': categoryId,
        'categoryModeData.currentCategory': category
      })

      // 先加载精选商品，再加载普通商品
      this.loadFeaturedFlowers()
    }
  },

  // 选择价格分类
  selectPriceCategory(e) {
    const categoryId = e.currentTarget.dataset.id
    let selectedCategory = null

    if (categoryId && categoryId !== 'all') {
      selectedCategory = this.data.priceCategories.find(cat => cat.id == categoryId)
    }

    this.setData({
      selectedPriceCategory: selectedCategory,
      currentCategoryId: null,
      currentCategory: null,
      currentPage: 1,
      hasMore: true,
      flowers: [],
      featuredFlowers: [],
      featuredLoaded: false,
      'priceModeData.selectedPriceCategory': selectedCategory
    })

    // 重新加载数据
    this.loadFeaturedFlowers()
  },

  // 加载精选花卉
  loadFeaturedFlowers() {
    if (this.data.isLoading) return

    this.setData({
      isLoading: true
    })

    // 构建请求参数
    const requestData = {
      limit: 6 // 限制精选商品数量
    }

    // 根据当前模式添加筛选参数
    if (this.data.viewMode === 'category' && this.data.currentCategoryId) {
      requestData.categoryId = this.data.currentCategoryId
    } else if (this.data.viewMode === 'price' && this.data.selectedPriceCategory) {
      requestData.minPrice = this.data.selectedPriceCategory.minPrice
      requestData.maxPrice = this.data.selectedPriceCategory.maxPrice
    }

    app.request({
      url: '/flower/featured',
      method: 'GET',
      data: requestData
    }).then(res => {
      const featuredFlowers = res.data.map(item => ({
        ...item,
        tagList: item.tags ? item.tags.split(',') : [],
        isFeatured: true // 标记为精选商品
      }))

      this.setData({
        featuredFlowers: featuredFlowers,
        featuredLoaded: true,
        isLoading: false
      })

      // 精选商品加载完成后，加载普通商品
      this.loadFlowers()
    }).catch(err => {
      console.error('加载精选花卉失败', err)
      this.setData({
        featuredLoaded: true,
        isLoading: false
      })
      // 即使精选商品加载失败，也要加载普通商品
      this.loadFlowers()
    })
  },

  // 加载花卉列表
  loadFlowers() {
    if (this.data.isLoading) return

    this.setData({
      isLoading: true
    })

    // 构建请求参数
    const requestData = {
      current: this.data.currentPage,
      size: this.data.pageSize,
      excludeFeatured: true // 排除精选商品，避免重复显示
    }

    // 根据当前模式添加筛选参数
    if (this.data.viewMode === 'category' && this.data.currentCategoryId) {
      requestData.categoryId = this.data.currentCategoryId
    } else if (this.data.viewMode === 'price' && this.data.selectedPriceCategory) {
      requestData.minPrice = this.data.selectedPriceCategory.minPrice
      requestData.maxPrice = this.data.selectedPriceCategory.maxPrice
    }

    app.request({
      url: '/flower/list',
      method: 'GET',
      data: requestData
    }).then(res => {
      const flowers = res.data.records.map(item => ({
        ...item,
        tagList: item.tags ? item.tags.split(',') : []
      }))

      if (this.data.currentPage === 1) {
        this.setData({
          flowers: flowers
        })
      } else {
        this.setData({
          flowers: [...this.data.flowers, ...flowers]
        })
      }

      this.setData({
        hasMore: this.data.currentPage < res.data.pages,
        isLoading: false
      })
    }).catch(err => {
      console.error('加载花卉列表失败', err)
      this.setData({
        isLoading: false
      })
    })
  },

  // 加载更多花卉
  loadMoreFlowers() {
    this.setData({
      currentPage: this.data.currentPage + 1
    })
    this.loadFlowers()
  },

  // 刷新数据
  refreshData() {
    this.setData({
      currentPage: 1,
      hasMore: true,
      flowers: [],
      featuredFlowers: [],
      featuredLoaded: false
    })
    this.loadFeaturedFlowers()
  },

  // 跳转到搜索页面
  goToSearch() {
    wx.navigateTo({
      url: '/pages/search/search'
    })
  },

  // 跳转到详情页面
  goToDetail(e) {
    const flowerId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/detail/detail?id=${flowerId}`
    })
  },

  // 添加到购物车
  addToCart(e) {
    const flowerId = e.currentTarget.dataset.id
    
    // 检查登录状态
    if (!app.globalData.userId) {
      this.showLoginModal()
      return
    }

    app.request({
      url: '/cart/add',
      method: 'POST',
      data: {
        userId: app.globalData.userId,
        flowerId: flowerId,
        quantity: 1
      }
    }).then(res => {
      wx.showToast({
        title: '已添加到购物车',
        icon: 'success'
      })
      // 使用新的方法更新购物车徽章
      app.addToCartWithBadge()
    }).catch(err => {
      console.error('添加购物车失败', err)
    })
  },

  // 切换收藏状态
  toggleFavorite(e) {
    const flowerId = e.currentTarget.dataset.id

    if (!app.globalData.userId) {
      this.showLoginModal()
      return
    }

    app.request({
      url: '/favorite/toggle',
      method: 'POST',
      data: {
        userId: app.globalData.userId,
        flowerId: flowerId
      }
    }).then(res => {
      wx.showToast({
        title: res.data ? '已添加到收藏' : '已取消收藏',
        icon: 'success'
      })
    }).catch(err => {
      console.error('收藏操作失败', err)
      wx.showToast({
        title: '操作失败，请重试',
        icon: 'none'
      })
    })
  },

  // 显示登录提示
  showLoginModal() {
    wx.showModal({
      title: '提示',
      content: '请先登录后再进行操作',
      confirmText: '去登录',
      success: (res) => {
        if (res.confirm) {
          wx.navigateTo({
            url: '/pages/login/login'
          })
        }
      }
    })
  }
})
