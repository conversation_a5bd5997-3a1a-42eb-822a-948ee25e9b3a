<!-- pages/address-list/address-list.wxml -->
<view class="page-container">
  <!-- 页面头部 -->
  <view class="page-header">
    <view class="header-content">
      <text class="header-title">收货地址</text>
      <text class="address-count" wx:if="{{!isEmpty && !isLoading}}">{{addresses.length}}个地址</text>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-state" wx:if="{{isLoading}}">
    <view class="loading-container">
      <view class="loading-spinner"></view>
      <text class="loading-text">正在加载地址...</text>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:elif="{{isEmpty}}">
    <view class="empty-container">
      <view class="empty-icon">
        <image class="empty-image" src="/images/empty-address.png" mode="aspectFit" />
      </view>
      <text class="empty-title">还没有收货地址</text>
      <text class="empty-desc">添加收货地址，享受便捷配送服务</text>
      <button class="add-address-btn" bindtap="addAddress">
        <text class="btn-text">添加地址</text>
      </button>
    </view>
  </view>

  <!-- 地址列表 -->
  <view class="address-container" wx:else>
    <view class="address-list">
      <view class="address-item {{item.isDefault ? 'default-address' : ''}}"
            wx:for="{{addresses}}"
            wx:key="id"
            bindtap="{{isSelectMode ? 'selectAddress' : ''}}"
            data-address="{{item}}">

        <!-- 默认标签 -->
        <view class="default-badge" wx:if="{{item.isDefault === 1}}">
          <text class="badge-text">默认</text>
        </view>

        <!-- 地址信息 -->
        <view class="address-info">
          <view class="recipient-section">
            <view class="recipient-info">
              <text class="recipient-name">{{item.recipientName}}</text>
              <text class="recipient-phone">{{item.recipientPhone}}</text>
            </view>
          </view>

          <view class="address-section">
            <view class="address-detail">
              <text class="address-text">{{item.province}} {{item.city}} {{item.district}}</text>
              <text class="detailed-address">{{item.detailedAddress}}</text>
              <text class="postal-code" wx:if="{{item.postalCode}}">邮编：{{item.postalCode}}</text>
            </view>
          </view>
        </view>

        <!-- 操作按钮 -->
        <view class="address-actions" wx:if="{{!isSelectMode}}">
          <view class="action-btn default-btn {{item.isDefault === 1 ? 'active' : ''}}"
                bindtap="setDefault"
                data-id="{{item.id}}"
                catchtap="setDefault">
            <text class="action-icon">{{item.isDefault === 1 ? '★' : '☆'}}</text>
            <text class="action-text">{{item.isDefault === 1 ? '默认地址' : '设为默认'}}</text>
          </view>
          <view class="action-btn edit-btn" bindtap="editAddress" data-id="{{item.id}}" catchtap="editAddress">
            <text class="action-icon">✏️</text>
            <text class="action-text">编辑</text>
          </view>
          <view class="action-btn delete-btn" bindtap="showDeleteConfirm" data-id="{{item.id}}" catchtap="showDeleteConfirm">
            <text class="action-icon">🗑️</text>
            <text class="action-text">删除</text>
          </view>
        </view>

        <!-- 选择模式下的选中图标 -->
        <view class="select-indicator" wx:if="{{isSelectMode}}">
          <view class="select-icon">
            <text class="icon-text">选择</text>
            <text class="arrow-icon">→</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 添加地址按钮 -->
    <view class="add-button-container">
      <button class="add-new-address-btn" bindtap="addAddress">
        <text class="add-icon">+</text>
        <text class="add-text">添加新地址</text>
      </button>
    </view>
  </view>

  <!-- 删除确认弹窗 -->
  <view class="modal-overlay" wx:if="{{showDeleteModal}}" bindtap="hideDeleteModal">
    <view class="modal-container" catchtap="">
      <view class="modal-header">
        <text class="modal-title">删除地址</text>
      </view>
      <view class="modal-body">
        <text class="modal-text">确定要删除这个收货地址吗？删除后无法恢复。</text>
      </view>
      <view class="modal-footer">
        <button class="modal-btn cancel-btn" bindtap="hideDeleteModal">取消</button>
        <button class="modal-btn confirm-btn" bindtap="confirmDelete">删除</button>
      </view>
    </view>
  </view>
</view>
