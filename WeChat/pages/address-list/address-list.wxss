/* pages/address-list/address-list.wxss */

/* 页面容器 */
.page-container {
  min-height: 100vh;
  background: linear-gradient(180deg, #fef7f0 0%, #f8f8f8 100%);
}

/* 页面头部 */
.page-header {
  background: white;
  padding: 25rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.address-count {
  font-size: 24rpx;
  color: #999;
  background: #f5f5f5;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
}

/* 加载状态 */
.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
}

.loading-container {
  text-align: center;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f0f0f0;
  border-top: 4rpx solid #ff6b9d;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 30rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* 空状态 */
.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
}

.empty-container {
  text-align: center;
  padding: 0 40rpx;
}

.empty-icon {
  margin-bottom: 40rpx;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  opacity: 0.6;
}

.empty-title {
  display: block;
  font-size: 32rpx;
  color: #666;
  font-weight: 500;
  margin-bottom: 15rpx;
}

.empty-desc {
  display: block;
  font-size: 26rpx;
  color: #999;
  margin-bottom: 50rpx;
  line-height: 1.4;
}

.add-address-btn {
  background: linear-gradient(135deg, #ff6b9d, #ff8fab);
  color: white;
  border: none;
  border-radius: 50rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
  font-weight: 500;
  box-shadow: 0 8rpx 20rpx rgba(255, 107, 157, 0.3);
  transition: all 0.2s ease;
}

.add-address-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 157, 0.3);
}

.btn-text {
  color: white;
}

/* 地址容器 */
.address-container {
  flex: 1;
  padding-bottom: 160rpx; /* 增加底部空间，确保内容不被按钮遮挡 */
}

.address-list {
  padding: 20rpx;
}

.address-item {
  position: relative;
  background: white;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.address-item:active {
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.12);
}

.default-address {
  border: 2rpx solid #ff6b9d;
  box-shadow: 0 4rpx 20rpx rgba(255, 107, 157, 0.15);
}

.default-badge {
  position: absolute;
  top: 0;
  right: 0;
  background: linear-gradient(135deg, #ff6b9d, #ff8fab);
  padding: 12rpx 24rpx;
  border-radius: 0 20rpx 0 20rpx;
  z-index: 10;
}

.badge-text {
  color: white;
  font-size: 22rpx;
  font-weight: 600;
}

.address-info {
  padding: 30rpx;
  padding-bottom: 20rpx;
}

.recipient-section {
  margin-bottom: 20rpx;
}

.recipient-info {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.recipient-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.recipient-phone {
  font-size: 28rpx;
  color: #666;
  background: rgba(255, 107, 157, 0.1);
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
}

.address-section {
  margin-bottom: 20rpx;
}

.address-detail {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.address-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
  font-weight: 500;
}

.detailed-address {
  font-size: 24rpx;
  color: #999;
  line-height: 1.4;
}

.postal-code {
  font-size: 22rpx;
  color: #999;
  background: #f5f5f5;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  display: inline-block;
  margin-top: 8rpx;
}

/* 操作按钮 */
.address-actions {
  display: flex;
  gap: 12rpx;
  padding: 0 30rpx 25rpx;
}

.action-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  padding: 16rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  font-weight: 500;
  transition: all 0.2s ease;
}

.default-btn {
  background: rgba(255, 107, 157, 0.1);
  color: #ff6b9d;
}

.default-btn.active {
  background: linear-gradient(135deg, #ff6b9d, #ff8fab);
  color: white;
}

.edit-btn {
  background: rgba(52, 199, 89, 0.1);
  color: #34c759;
}

.delete-btn {
  background: rgba(255, 59, 48, 0.1);
  color: #ff3b30;
}

.action-btn:active {
  transform: scale(0.95);
}

.action-icon {
  font-size: 18rpx;
}

.action-text {
  font-size: 22rpx;
  font-weight: 500;
}

/* 选择模式 */
.select-indicator {
  position: absolute;
  right: 30rpx;
  top: 50%;
  transform: translateY(-50%);
}

.select-icon {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 16rpx;
  background: rgba(255, 107, 157, 0.1);
  border-radius: 20rpx;
}

.icon-text {
  font-size: 22rpx;
  color: #ff6b9d;
  font-weight: 500;
}

.arrow-icon {
  font-size: 20rpx;
  color: #ff6b9d;
  font-weight: bold;
}

/* 添加按钮 */
.add-button-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx 30rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
  background: white;
  border-top: 1rpx solid #f0f0f0;
  z-index: 100;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.add-new-address-btn {
  width: 100%;
  background: linear-gradient(135deg, #ff6b9d, #ff8fab);
  color: white;
  border: none;
  border-radius: 25rpx;
  padding: 20rpx;
  font-size: 28rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  box-shadow: 0 8rpx 20rpx rgba(255, 107, 157, 0.3);
  transition: all 0.2s ease;
}

.add-new-address-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 157, 0.3);
}

.add-icon {
  font-size: 32rpx;
  font-weight: bold;
  color: white;
}

.add-text {
  color: white;
  font-size: 28rpx;
  font-weight: 500;
}

/* 删除确认弹窗 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.modal-container {
  background: white;
  border-radius: 24rpx;
  width: 600rpx;
  max-width: 85%;
  overflow: hidden;
  animation: slideUp 0.3s ease;
}

@keyframes slideUp {
  from {
    transform: translateY(50rpx);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.modal-header {
  padding: 40rpx 30rpx 20rpx;
  text-align: center;
}

.modal-title {
  font-size: 34rpx;
  font-weight: 600;
  color: #333;
}

.modal-body {
  padding: 20rpx 30rpx 40rpx;
  text-align: center;
}

.modal-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

.modal-footer {
  display: flex;
  border-top: 1rpx solid #f0f0f0;
}

.modal-btn {
  flex: 1;
  padding: 32rpx;
  font-size: 28rpx;
  border: none;
  background: white;
  border-radius: 0;
  font-weight: 500;
  transition: all 0.2s ease;
}

.cancel-btn {
  color: #666;
  border-right: 1rpx solid #f0f0f0;
}

.confirm-btn {
  color: #ff3b30;
  font-weight: 600;
}

.modal-btn:active {
  background: #f8f8f8;
}
