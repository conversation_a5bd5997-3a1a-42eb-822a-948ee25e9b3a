// pages/address-list/address-list.js
const app = getApp()

Page({
  data: {
    addresses: [],
    isLoading: false,
    isEmpty: false,
    showDeleteModal: false,
    deleteAddressId: null,
    isSelectMode: false // 是否为选择地址模式
  },

  onLoad(options) {
    // 检查是否为选择地址模式
    if (options.select === 'true') {
      this.setData({
        isSelectMode: true
      })
      wx.setNavigationBarTitle({
        title: '选择收货地址'
      })
    }
    this.loadAddresses()
  },

  onShow() {
    this.loadAddresses()
  },

  onPullDownRefresh() {
    this.loadAddresses()
    wx.stopPullDownRefresh()
  },

  // 加载地址列表
  loadAddresses() {
    if (!app.globalData.userId) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
      setTimeout(() => {
        wx.navigateTo({
          url: '/pages/login/login'
        })
      }, 1500)
      return
    }

    this.setData({
      isLoading: true
    })

    app.request({
      url: `/address/list/${app.globalData.userId}`,
      method: 'GET'
    }).then(res => {
      this.setData({
        addresses: res.data || [],
        isEmpty: (res.data || []).length === 0,
        isLoading: false
      })
    }).catch(err => {
      console.error('加载地址列表失败', err)
      this.setData({
        isLoading: false,
        isEmpty: true
      })
    })
  },

  // 选择地址（选择模式下）
  selectAddress(e) {
    if (!this.data.isSelectMode) return
    
    const address = e.currentTarget.dataset.address
    const pages = getCurrentPages()
    const prevPage = pages[pages.length - 2]
    
    if (prevPage) {
      prevPage.setData({
        selectedAddress: address
      })
    }
    
    wx.navigateBack()
  },

  // 设置默认地址
  setDefault(e) {
    console.log('setDefault clicked', e)
    const addressId = e.currentTarget.dataset.id
    console.log('addressId:', addressId, 'userId:', app.globalData.userId)

    if (!app.globalData.userId) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
      return
    }

    // 检查是否已经是默认地址
    const currentAddress = this.data.addresses.find(addr => addr.id === parseInt(addressId))
    if (currentAddress && currentAddress.isDefault === 1) {
      wx.showToast({
        title: '已经是默认地址',
        icon: 'none'
      })
      return
    }

    wx.showLoading({
      title: '设置中...'
    })

    app.request({
      url: '/address/setDefault',
      method: 'POST',
      data: {
        id: addressId,
        userId: app.globalData.userId
      }
    }).then(res => {
      wx.hideLoading()
      wx.showToast({
        title: '设置成功',
        icon: 'success'
      })
      // 立即更新本地数据，提升用户体验
      this.updateLocalDefaultAddress(parseInt(addressId))
      // 然后重新加载数据确保同步
      setTimeout(() => {
        this.loadAddresses()
      }, 500)
    }).catch(err => {
      wx.hideLoading()
      console.error('设置默认地址失败', err)
      wx.showToast({
        title: '设置失败',
        icon: 'none'
      })
    })
  },

  // 更新本地默认地址状态
  updateLocalDefaultAddress(newDefaultId) {
    const addresses = this.data.addresses.map(addr => ({
      ...addr,
      isDefault: addr.id === newDefaultId ? 1 : 0
    }))
    this.setData({
      addresses: addresses
    })
  },

  // 编辑地址
  editAddress(e) {
    console.log('editAddress clicked', e)
    const addressId = e.currentTarget.dataset.id
    console.log('编辑地址ID:', addressId)
    wx.navigateTo({
      url: `/pages/address-edit/address-edit?id=${addressId}`
    })
  },

  // 显示删除确认弹窗
  showDeleteConfirm(e) {
    console.log('showDeleteConfirm clicked', e)
    const addressId = e.currentTarget.dataset.id
    console.log('删除地址ID:', addressId)
    this.setData({
      showDeleteModal: true,
      deleteAddressId: addressId
    })
  },

  // 隐藏删除弹窗
  hideDeleteModal() {
    this.setData({
      showDeleteModal: false,
      deleteAddressId: null
    })
  },

  // 确认删除地址
  confirmDelete() {
    if (!this.data.deleteAddressId) return

    app.request({
      url: '/address/delete',
      method: 'DELETE',
      data: {
        id: this.data.deleteAddressId,
        userId: app.globalData.userId
      }
    }).then(res => {
      wx.showToast({
        title: '删除成功',
        icon: 'success'
      })
      this.hideDeleteModal()
      this.loadAddresses()
    }).catch(err => {
      console.error('删除地址失败', err)
      wx.showToast({
        title: '删除失败',
        icon: 'none'
      })
      this.hideDeleteModal()
    })
  },

  // 添加新地址
  addAddress() {
    wx.navigateTo({
      url: '/pages/address-edit/address-edit'
    })
  }
})
