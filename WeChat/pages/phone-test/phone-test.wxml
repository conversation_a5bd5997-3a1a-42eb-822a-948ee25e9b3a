<!--pages/phone-test/phone-test.wxml-->
<view class="container">
  <view class="header">
    <text class="title">手机号获取测试</text>
    <text class="subtitle">测试各种手机号获取方式</text>
  </view>

  <!-- 测试结果显示 -->
  <view class="result-section" wx:if="{{testResult}}">
    <view class="result-title">测试结果</view>
    <view class="result-content">{{testResult}}</view>
  </view>

  <!-- 测试按钮组 -->
  <view class="test-buttons">
    <!-- 新版微信API测试 -->
    <button 
      class="test-btn primary"
      open-type="getPhoneNumber"
      bindgetphonenumber="testNewAPI"
    >
      测试新版微信API
    </button>

    <!-- 手动输入测试 -->
    <button 
      class="test-btn secondary"
      bindtap="testManualInput"
    >
      测试手动输入方式
    </button>

    <!-- 配置检查 -->
    <button 
      class="test-btn info"
      bindtap="checkConfig"
    >
      检查后端配置
    </button>

    <!-- 清除结果 -->
    <button 
      class="test-btn clear"
      bindtap="clearResult"
    >
      清除测试结果
    </button>
  </view>

  <!-- 配置信息显示 -->
  <view class="config-section">
    <view class="config-title">当前配置状态</view>
    <view class="config-item">
      <text class="config-label">用户ID:</text>
      <text class="config-value">{{userId || '未登录'}}</text>
    </view>
    <view class="config-item">
      <text class="config-label">后端地址:</text>
      <text class="config-value">{{baseUrl}}</text>
    </view>
    <view class="config-item">
      <text class="config-label">当前环境:</text>
      <text class="config-value">{{environment}}</text>
    </view>
  </view>

  <!-- 说明信息 -->
  <view class="tips-section">
    <view class="tips-title">测试说明</view>
    <text class="tip-item">• 新版微信API：需要小程序认证和权限申请</text>
    <text class="tip-item">• 手动输入方式：适用于所有开发者</text>
    <text class="tip-item">• 开发环境会自动使用模拟数据</text>
    <text class="tip-item">• 如果测试失败，请检查配置和权限</text>
  </view>
</view>
