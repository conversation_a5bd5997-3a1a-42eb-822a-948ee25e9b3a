/* pages/phone-test/phone-test.wxss */
.container {
  padding: 30rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
}

.title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.subtitle {
  display: block;
  font-size: 24rpx;
  color: #666;
}

.result-section {
  background: white;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.result-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.result-content {
  font-size: 24rpx;
  color: #666;
  line-height: 1.6;
  background: #f8f9fa;
  padding: 20rpx;
  border-radius: 8rpx;
  word-break: break-all;
}

.test-buttons {
  margin-bottom: 30rpx;
}

.test-btn {
  width: 100%;
  height: 80rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  margin-bottom: 20rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.test-btn.primary {
  background: #007aff;
  color: white;
}

.test-btn.secondary {
  background: #34c759;
  color: white;
}

.test-btn.info {
  background: #ff9500;
  color: white;
}

.test-btn.clear {
  background: #ff3b30;
  color: white;
}

.config-section {
  background: white;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.config-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.config-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.config-item:last-child {
  border-bottom: none;
}

.config-label {
  font-size: 26rpx;
  color: #666;
}

.config-value {
  font-size: 24rpx;
  color: #333;
  max-width: 400rpx;
  text-align: right;
  word-break: break-all;
}

.tips-section {
  background: white;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.tips-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.tip-item {
  display: block;
  font-size: 24rpx;
  color: #666;
  line-height: 2;
  margin-bottom: 10rpx;
}
