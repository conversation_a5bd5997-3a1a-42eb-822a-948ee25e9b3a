// pages/phone-test/phone-test.js
const app = getApp()

Page({
  data: {
    testResult: '',
    userId: '',
    baseUrl: '',
    environment: '开发环境'
  },

  onLoad() {
    this.setData({
      userId: app.globalData.userId || '未登录',
      baseUrl: app.globalData.baseUrl || 'localhost:8080'
    })
  },

  // 测试新版微信API
  testNewAPI(e) {
    console.log('测试新版微信API:', e)
    
    if (e.detail.errMsg === 'getPhoneNumber:ok') {
      wx.showLoading({
        title: '测试中...',
        mask: true
      })

      // 调用后端接口
      app.request({
        url: '/user/phone',
        method: 'POST',
        data: {
          userId: app.globalData.userId || 1,
          code: e.detail.code
        }
      }).then(response => {
        wx.hideLoading()
        
        const result = `✅ 新版API测试成功\n\n响应数据:\n${JSON.stringify(response, null, 2)}`
        this.setData({
          testResult: result
        })
        
        wx.showToast({
          title: '测试成功',
          icon: 'success'
        })
      }).catch(err => {
        wx.hideLoading()
        
        const result = `❌ 新版API测试失败\n\n错误信息:\n${JSON.stringify(err, null, 2)}`
        this.setData({
          testResult: result
        })
        
        wx.showToast({
          title: '测试失败',
          icon: 'error'
        })
      })
    } else {
      const result = `❌ 微信授权失败\n\n错误信息:\n${e.detail.errMsg}\n\n可能原因:\n1. 小程序未认证\n2. 未申请手机号权限\n3. 权限审核未通过`
      this.setData({
        testResult: result
      })
    }
  },

  // 测试手动输入方式
  testManualInput() {
    wx.navigateTo({
      url: '/pages/phone-auth/phone-auth?from=test'
    })
  },

  // 检查后端配置
  checkConfig() {
    wx.showLoading({
      title: '检查中...',
      mask: true
    })

    // 先检查基础连接
    app.request({
      url: '/user/send-sms',
      method: 'POST',
      data: {
        phoneNumber: '13800138000'
      }
    }).then(response => {
      // 基础连接正常，继续检查微信配置
      return this.checkWeChatConfig()
    }).then(wechatResult => {
      wx.hideLoading()

      const result = `✅ 配置检查完成\n\n基础服务: ✅ 正常\n微信配置: ${wechatResult.status}\n\n详细信息:\n${wechatResult.details}\n\n建议:\n${wechatResult.suggestions}`
      this.setData({
        testResult: result
      })

      wx.showToast({
        title: wechatResult.status === '✅ 正常' ? '配置正常' : '需要配置',
        icon: wechatResult.status === '✅ 正常' ? 'success' : 'none'
      })
    }).catch(err => {
      wx.hideLoading()

      const result = `❌ 后端配置检查失败\n\n错误信息:\n${JSON.stringify(err, null, 2)}\n\n可能原因:\n1. 后端服务未启动\n2. 网络连接问题\n3. 接口地址配置错误\n\n解决方案:\n1. 检查后端服务是否启动\n2. 确认网络连接正常\n3. 验证API地址配置`
      this.setData({
        testResult: result
      })

      wx.showToast({
        title: '配置异常',
        icon: 'error'
      })
    })
  },

  // 检查微信配置
  checkWeChatConfig() {
    return new Promise((resolve) => {
      // 模拟检查微信配置的逻辑
      // 实际项目中可以调用一个专门的配置检查接口

      const suggestions = [
        '1. 确保小程序已在微信公众平台认证',
        '2. 申请"手机号快速验证组件"权限',
        '3. 配置正确的AppID和AppSecret',
        '4. 在开发环境可以使用模拟数据测试'
      ].join('\n')

      // 检查是否为开发环境
      const isDev = app.globalData.baseUrl.includes('localhost') ||
                   app.globalData.baseUrl.includes('127.0.0.1')

      if (isDev) {
        resolve({
          status: '⚠️ 开发环境',
          details: '当前为开发环境，会自动使用模拟数据\n无需配置真实的微信权限',
          suggestions: '开发环境建议:\n1. 测试基础功能流程\n2. 验证UI交互\n3. 准备生产环境配置'
        })
      } else {
        resolve({
          status: '❓ 需要验证',
          details: '生产环境需要配置真实的微信权限\n请确保已完成相关配置',
          suggestions: suggestions
        })
      }
    })
  },

  // 清除测试结果
  clearResult() {
    this.setData({
      testResult: ''
    })
    
    wx.showToast({
      title: '已清除',
      icon: 'success'
    })
  }
})
