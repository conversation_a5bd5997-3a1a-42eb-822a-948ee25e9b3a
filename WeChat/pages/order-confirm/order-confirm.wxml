<!-- pages/order-confirm/order-confirm.wxml -->
<view class="container">

  <scroll-view class="content" scroll-y="true">
    <!-- 商品列表 -->
    <view class="section">
      <view class="section-title">商品信息</view>
      <view class="product-list">
        <view class="product-item" wx:for="{{items}}" wx:key="index">
          <image class="product-image" src="{{item.flower.mainImage}}" mode="aspectFill" />
          <view class="product-info">
            <text class="product-name">{{item.flower.name}}</text>
            <text class="product-desc">{{item.flower.description}}</text>
            <view class="product-footer">
              <text class="product-price">¥{{item.flower.price}}</text>
              <text class="product-quantity">x{{item.quantity}}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 配送方式选择 -->
    <view class="section">
      <view class="section-title">配送方式</view>
      <radio-group bindchange="onDeliveryTypeChange">
        <view class="delivery-option">
          <radio value="1" checked="{{deliveryType === 1}}" color="#ff6b9d" />
          <text class="option-text">外卖配送</text>
        </view>
        <view class="delivery-option">
          <radio value="2" checked="{{deliveryType === 2}}" color="#ff6b9d" />
          <text class="option-text">到店自取</text>
        </view>
      </radio-group>
    </view>

    <!-- 外卖配送信息 -->
    <view class="section" wx:if="{{deliveryType === 1}}">
      <view class="section-title">收货地址</view>
      <view class="address-section" bindtap="selectAddress">
        <view class="address-content" wx:if="{{selectedAddress}}">
          <view class="address-header">
            <text class="recipient-name">{{selectedAddress.recipientName}}</text>
            <text class="recipient-phone">{{selectedAddress.recipientPhone}}</text>
            <view class="default-badge" wx:if="{{selectedAddress.isDefault === 1}}">
              <text class="badge-text">默认</text>
            </view>
          </view>
          <text class="address-detail">{{formattedAddress}}</text>
        </view>
        <view class="no-address" wx:else>
          <text class="no-address-text">请选择收货地址</text>
        </view>
        <text class="arrow-icon">></text>
      </view>

      <!-- 收货日期 -->
      <view class="form-item">
        <text class="form-label">收货日期: <text class="required">*</text></text>
        <view class="form-input-wrapper" bindtap="selectDeliveryTime">
          <input class="form-input" placeholder="请选择收货日期" value="{{deliveryTime}}" disabled />
          <text class="arrow-icon">></text>
        </view>
      </view>

      <!-- 外卖配送备注 -->
      <view class="form-item">
        <text class="form-label">配送备注</text>
        <textarea class="form-textarea" placeholder="请输入配送备注（可选）" value="{{deliveryRemark}}" bindinput="onDeliveryRemarkInput" />
      </view>
    </view>

    <!-- 自取信息 -->
    <view class="section" wx:if="{{deliveryType === 2}}">
      <view class="section-title">自取信息</view>
      <view class="form-group">
        <view class="form-item">
          <text class="form-label">取货人姓名： <text class="required">*</text></text>
          <input class="form-input" placeholder="请输入取货人姓名" value="{{pickupInfo.pickupName}}" bindinput="onPickupNameInput" />
        </view>
        <view class="form-item">
          <text class="form-label">取货人电话: <text class="required">*</text></text>
          <input class="form-input" placeholder="请输入取货人电话" value="{{pickupInfo.pickupPhone}}" bindinput="onPickupPhoneInput" type="number" />
        </view>
        <view class="form-item">
          <text class="form-label">取货日期: <text class="required">*</text></text>
          <view class="form-input-wrapper" bindtap="selectPickupTime">
            <input class="form-input" placeholder="请选择取货日期" value="{{pickupInfo.pickupTime}}" disabled />
            <text class="arrow-icon">></text>
          </view>
        </view>
        <view class="form-item">
          <text class="form-label">备注信息:</text>
          <textarea class="form-textarea" placeholder="请输入备注信息（可选）" value="{{pickupInfo.remark}}" bindinput="onRemarkInput" />
        </view>
        <view class="form-item">
          <text class="form-label">备用地址:</text>
          <text class="form-desc">突发情况需要外卖派送时使用（可选）</text>

          <!-- 省市区选择 -->
          <view class="region-selector">
            <view class="region-item" bindtap="showRegionPicker">
              <text class="region-label">省市区：</text>
              <text class="region-value" wx:if="{{backupRegion.length > 0 && backupRegion[0]}}">{{backupRegion[0]}}{{backupRegion[1] ? ' ' + backupRegion[1] : ''}}{{backupRegion[2] ? ' ' + backupRegion[2] : ''}}</text>
              <text class="region-placeholder" wx:else>请选择省市区</text>
              <text class="arrow-icon">></text>
            </view>
          </view>

          <!-- 详细地址 -->

          <text class="form-label">详细地址:</text>
          <textarea class="form-textarea" placeholder="请输入详细地址" value="{{pickupInfo.backupDetailAddress}}" bindinput="onBackupDetailAddressInput" />
        </view>
      </view>
    </view>

    <!-- 订单金额 -->
    <view class="section">
      <view class="section-title">订单金额</view>
      <view class="amount-detail">
        <view class="amount-item">
          <text class="amount-label">商品总价</text>
          <text class="amount-value">¥{{totalAmount}}</text>
        </view>
        <view class="amount-item">
          <text class="amount-label">配送费</text>
          <text class="amount-value">{{deliveryType === 1 ? '¥0.00' : '免费'}}</text>
        </view>
        <view class="amount-item total">
          <text class="amount-label">实付金额</text>
          <text class="amount-value total-price">¥{{totalAmount}}</text>
        </view>
      </view>
    </view>
  </scroll-view>

  <!-- 底部提交按钮 -->
  <view class="submit-container">
    <view class="submit-info">
      <text class="total-text">总计：¥{{totalAmount}}</text>
    </view>
    <view class="submit-btn {{isSubmitting ? 'disabled' : ''}}" bindtap="submitOrder">
      <text class="submit-text">{{isSubmitting ? '提交中...' : '提交订单'}}</text>
    </view>
  </view>
</view>

<!-- 地址选择器 -->
<view class="modal-overlay" wx:if="{{addressPickerShow}}" bindtap="closeAddressPicker">
  <view class="address-picker" catchtap="stopPropagation">
    <view class="picker-header">
      <text class="picker-title">选择收货地址</text>
      <text class="picker-close" bindtap="closeAddressPicker">×</text>
    </view>
    <scroll-view class="picker-content" scroll-y="true">
      <view class="address-picker-item {{selectedAddress && selectedAddress.id === item.id ? 'selected' : ''}}"
            wx:for="{{addresses}}" wx:key="id"
            bindtap="onAddressSelect" data-index="{{index}}">
        <view class="address-item-wrapper">
          <view class="address-item-content">
            <view class="address-header">
              <view class="recipient-info">
                <text class="recipient-name">{{item.recipientName}}</text>
                <text class="recipient-phone">{{item.recipientPhone}}</text>
              </view>
              <view class="address-badges">
                <view class="default-badge" wx:if="{{item.isDefault === 1}}">
                  <text class="badge-text">默认</text>
                </view>
                <view class="selected-badge" wx:if="{{selectedAddress && selectedAddress.id === item.id}}">
                  <text class="selected-icon">✓</text>
                </view>
              </view>
            </view>
            <view class="address-detail-wrapper">
              <text class="address-detail">{{item.province}} {{item.city}} {{item.district}}</text>
              <text class="detailed-address">{{item.detailedAddress}}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 添加新地址选项 -->
      <view class="add-address-item" bindtap="goToAddAddress">
        <view class="add-address-content">
          <view class="add-icon">+</view>
          <text class="add-text">添加新地址</text>
        </view>
      </view>
    </scroll-view>
  </view>
</view>

<!-- 取货时间选择器 -->
<view class="picker-overlay" wx:if="{{timePickerShow}}" bindtap="onTimePickerCancel">
  <view class="picker-container" catchtap="">
    <view class="picker-header">
      <text class="picker-cancel" bindtap="onTimePickerCancel">取消</text>
      <text class="picker-title">选择取货日期</text>
      <text class="picker-confirm" bindtap="onTimePickerConfirm">确定</text>
    </view>
    <view class="time-picker-content">
      <text class="time-picker-desc">请选择您方便的取货日期</text>
      <picker-view class="time-picker" value="{{timePickerIndex}}" bindchange="onTimePickerChange">
        <picker-view-column>
          <view wx:for="{{dateOptions}}" wx:key="index" class="picker-item">{{item.label}}</view>
        </picker-view-column>
        <picker-view-column>
          <view wx:for="{{timeOptions}}" wx:key="*this" class="picker-item">{{item}}</view>
        </picker-view-column>
      </picker-view>
    </view>
  </view>
</view>

<!-- 收货日期选择器 -->
<view class="picker-overlay" wx:if="{{deliveryTimePickerShow}}" bindtap="onDeliveryTimePickerCancel">
  <view class="picker-container" catchtap="">
    <view class="picker-header">
      <text class="picker-cancel" bindtap="onDeliveryTimePickerCancel">取消</text>
      <text class="picker-title">选择收货日期</text>
      <text class="picker-confirm" bindtap="onDeliveryTimePickerConfirm">确定</text>
    </view>
    <view class="time-picker-content">
      <text class="time-picker-desc">请选择您方便的收货日期</text>
      <picker-view class="time-picker" value="{{deliveryTimePickerIndex}}" bindchange="onDeliveryTimePickerChange">
        <picker-view-column>
          <view wx:for="{{deliveryDateOptions}}" wx:key="index" class="picker-item">{{item.label}}</view>
        </picker-view-column>
        <picker-view-column>
          <view wx:for="{{deliveryTimeOptions}}" wx:key="*this" class="picker-item">{{item}}</view>
        </picker-view-column>
      </picker-view>
    </view>
  </view>
</view>

<!-- 地区选择器遮罩 -->
<view class="picker-overlay" wx:if="{{showRegionPicker}}" bindtap="onRegionCancel">
  <view class="picker-container" catchtap="">
    <view class="picker-header">
      <text class="picker-cancel" bindtap="onRegionCancel">取消</text>
      <text class="picker-title">选择地区</text>
      <text class="picker-confirm" bindtap="onRegionConfirm">确定</text>
    </view>

    <picker-view class="region-picker"
                 value="{{regionIndex}}"
                 bindchange="onRegionChange">
      <picker-view-column>
        <view wx:for="{{regionArray[0]}}" wx:key="*this" class="picker-item">{{item}}</view>
      </picker-view-column>
      <picker-view-column>
        <view wx:for="{{regionArray[1]}}" wx:key="*this" class="picker-item">{{item}}</view>
      </picker-view-column>
      <picker-view-column>
        <view wx:for="{{regionArray[2]}}" wx:key="*this" class="picker-item">{{item}}</view>
      </picker-view-column>
    </picker-view>
  </view>
</view>




