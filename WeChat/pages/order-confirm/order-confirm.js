// pages/order-confirm/order-confirm.js
const app = getApp()

Page({
  data: {
    orderData: {},
    items: [],
    totalAmount: 0,
    deliveryType: 1, // 1: 外卖配送, 2: 自取
    
    // 配送信息
    selectedAddress: null,
    addresses: [],
    deliveryTime: '', // 派送时间
    deliveryRemark: '', // 外卖配送备注
    
    // 自取信息
    pickupInfo: {
      pickupName: '',
      pickupPhone: '',
      pickupTime: '',
      remark: '',
      backupAddress: '',
      backupDetailAddress: ''
    },

    // 备用地址的省市区
    backupRegion: [],

    // 自取时间选择器
    timePickerShow: false,
    timePickerValue: '',
    timePickerIndex: [0, 0],
    dateOptions: [],
    timeOptions: [],

    // 派送时间选择器
    deliveryTimePickerShow: false,
    deliveryTimePickerValue: '',
    deliveryTimePickerIndex: [0, 0],
    deliveryDateOptions: [],
    deliveryTimeOptions: [],

    // 地址选择器
    addressPickerShow: false,

    // 省市区选择器
    regionPickerShow: false,
    showRegionPicker: false,
    regionArray: [[], [], []],
    regionIndex: [0, 0, 0],
    regionData: {
      provinces: [],
      cities: {},
      districts: {}
    },

    isSubmitting: false
  },

  onLoad(options) {
    if (options.data) {
      try {
        const orderData = JSON.parse(decodeURIComponent(options.data))
        this.setData({
          orderData: orderData,
          items: orderData.items || []
        })
        this.calculateTotal()
        this.loadUserAddresses()
        // 先初始化地区数据，然后再加载用户自取信息
        this.initRegionData().then(() => {
          this.loadUserPickupInfo()
        })
      } catch (e) {
        console.error('解析订单数据失败', e)
        wx.showToast({
          title: '数据错误',
          icon: 'none'
        })
      }
    }
  },

  onShow() {
    // 页面显示时重新加载地址列表，确保新添加的地址能及时显示
    this.loadUserAddresses()
  },

  // 计算总金额
  calculateTotal() {
    let total = 0
    this.data.items.forEach(item => {
      total += item.flower.price * item.quantity
    })
    this.setData({
      totalAmount: total.toFixed(2)
    })
  },

  // 加载用户地址
  loadUserAddresses() {
    if (!app.globalData.userId) return

    app.request({
      url: `/address/list/${app.globalData.userId}`,
      method: 'GET'
    }).then(res => {
      const addresses = res.data || []
      const defaultAddress = addresses.find(addr => addr.isDefault === 1)
      
      const selectedAddr = defaultAddress || (addresses.length > 0 ? addresses[0] : null)
      this.setData({
        addresses: addresses,
        selectedAddress: selectedAddr,
        formattedAddress: this.formatAddressForDisplay(selectedAddr)
      })
    }).catch(err => {
      console.error('加载地址失败', err)
    })
  },

  // 加载用户自取信息
  loadUserPickupInfo() {
    if (!app.globalData.userId) return

    app.request({
      url: `/pickup-info/${app.globalData.userId}`,
      method: 'GET'
    }).then(res => {
      if (res.data) {
        console.log('加载的自取信息:', res.data)

        // 直接使用后端返回的数据，不需要解析
        // 因为后端应该已经分别存储了省市区和详细地址
        const backupAddress = res.data.backupAddress || ''

        // 如果backupAddress包含完整地址，尝试智能解析
        let backupRegion = []
        let backupDetailAddress = ''

        // 使用更简单可靠的地址解析方法
        if (backupAddress) {
          console.log('开始解析地址:', backupAddress)

          // 使用简单的字符串分割方法
          // 先尝试识别常见的省市区模式
          let tempRegion = []
          let tempDetail = backupAddress

          // 处理直辖市
          const municipalities = ['北京市', '上海市', '天津市', '重庆市']
          let foundMunicipality = null

          for (const municipality of municipalities) {
            if (backupAddress.startsWith(municipality)) {
              foundMunicipality = municipality
              tempDetail = backupAddress.substring(municipality.length).trim()
              break
            }
          }

          if (foundMunicipality) {
            // 直辖市情况 - 注意：地区数据中直辖市可能不带"市"字
            const municipalityName = foundMunicipality.replace('市', '')
            tempRegion[0] = municipalityName  // 省份：如"上海"
            tempRegion[1] = municipalityName  // 城市：如"上海"

            // 查找区县
            const districtMatch = tempDetail.match(/^([^0-9\s]+?区|[^0-9\s]+?县)(.*)$/)
            if (districtMatch) {
              tempRegion[2] = districtMatch[1]
              tempDetail = districtMatch[2].trim()
            } else {
              tempRegion[2] = ''
            }
          } else {
            // 非直辖市情况
            // 查找省份
            const provinceMatch = backupAddress.match(/^(.+?省)(.*)$/)
            if (provinceMatch) {
              tempRegion[0] = provinceMatch[1]
              tempDetail = provinceMatch[2].trim()

              // 查找城市
              const cityMatch = tempDetail.match(/^(.+?市)(.*)$/)
              if (cityMatch) {
                tempRegion[1] = cityMatch[1]
                tempDetail = cityMatch[2].trim()

                // 查找区县
                const districtMatch = tempDetail.match(/^(.+?区|.+?县)(.*)$/)
                if (districtMatch) {
                  tempRegion[2] = districtMatch[1]
                  tempDetail = districtMatch[2].trim()
                } else {
                  tempRegion[2] = ''
                }
              } else {
                tempRegion[1] = ''
                tempRegion[2] = ''
              }
            } else {
              // 没有省份，可能只有市区
              const cityMatch = backupAddress.match(/^(.+?市)(.*)$/)
              if (cityMatch) {
                tempRegion[0] = ''
                tempRegion[1] = cityMatch[1]
                tempDetail = cityMatch[2].trim()

                // 查找区县
                const districtMatch = tempDetail.match(/^(.+?区|.+?县)(.*)$/)
                if (districtMatch) {
                  tempRegion[2] = districtMatch[1]
                  tempDetail = districtMatch[2].trim()
                } else {
                  tempRegion[2] = ''
                }
              }
            }
          }

          // 如果成功解析出省市区，则使用解析结果
          if (tempRegion.some(item => item && item.trim())) {
            backupRegion = tempRegion.filter(item => item && item.trim()) // 过滤空值
            backupDetailAddress = tempDetail
            console.log('地址解析成功:', {
              原始地址: backupAddress,
              解析出的省市区: backupRegion,
              详细地址: backupDetailAddress
            })
          } else {
            // 解析失败，整个作为详细地址
            backupRegion = []
            backupDetailAddress = backupAddress
            console.log('地址解析失败，作为详细地址处理:', backupAddress)
          }

          console.log('地址解析结果:', {
            原始地址: backupAddress,
            省市区: backupRegion,
            详细地址: backupDetailAddress
          })
        }

        console.log('解析后的地址信息:', {
          原始地址: backupAddress,
          省市区: backupRegion,
          详细地址: backupDetailAddress
        })

        this.setData({
          pickupInfo: {
            ...res.data,
            backupDetailAddress: backupDetailAddress
          },
          backupRegion: backupRegion
        })

        console.log('设置backupRegion后的状态:', {
          backupRegion: this.data.backupRegion,
          backupRegionLength: this.data.backupRegion ? this.data.backupRegion.length : 0,
          hasRegionData: !!(this.data.regionData && this.data.regionData.provinces)
        })

        // 如果地区数据已经加载完成，立即更新选择器
        if (backupRegion.length > 0 && this.data.regionData && this.data.regionData.provinces) {
          console.log('立即更新选择器')
          this.updateRegionIndex(backupRegion[0], backupRegion[1], backupRegion[2])
        } else {
          console.log('延迟更新选择器，等待地区数据加载')
        }
      }
    }).catch(err => {
      console.log('暂无自取信息', err)
    })
  },

  // 切换配送方式
  onDeliveryTypeChange(e) {
    this.setData({
      deliveryType: parseInt(e.detail.value)
    })
  },

  // 选择地址
  selectAddress() {
    if (this.data.addresses.length === 0) {
      wx.showModal({
        title: '提示',
        content: '您还没有收货地址，是否前往添加？',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/address-edit/address-edit'
            })
          }
        }
      })
      return
    }

    this.setData({
      addressPickerShow: true
    })
  },

  // 地址选择确认
  onAddressSelect(e) {
    const index = e.currentTarget.dataset.index
    const selectedAddr = this.data.addresses[index]
    this.setData({
      selectedAddress: selectedAddr,
      addressPickerShow: false,
      formattedAddress: this.formatAddressForDisplay(selectedAddr)
    })
  },

  // 格式化地址用于显示
  formatAddressForDisplay(address) {
    if (!address) return ''
    return app.formatFullAddress(address.province, address.city, address.district, address.detailedAddress)
  },

  // 自取信息输入
  onPickupNameInput(e) {
    this.setData({
      'pickupInfo.pickupName': e.detail.value
    })
  },

  onPickupPhoneInput(e) {
    this.setData({
      'pickupInfo.pickupPhone': e.detail.value
    })
  },

  onPickupTimeInput(e) {
    this.setData({
      'pickupInfo.pickupTime': e.detail.value
    })
  },

  onRemarkInput(e) {
    this.setData({
      'pickupInfo.remark': e.detail.value
    })
  },

  // 配送备注输入
  onDeliveryRemarkInput(e) {
    this.setData({
      deliveryRemark: e.detail.value
    })
  },

  onBackupDetailAddressInput(e) {
    this.setData({
      'pickupInfo.backupDetailAddress': e.detail.value
    })
    this.updateBackupAddress()
  },

  // 初始化地区数据（从数据库API获取完整的中国行政区划数据）
  initRegionData() {
    console.log('开始加载地区数据...')

    // 从后端API获取完整的省市区数据
    return app.request({
      url: '/flower/region/data',
      method: 'GET'
    }).then(res => {
      console.log('API返回数据:', res)
      if (res.data && res.data.provinces && res.data.cities && res.data.districts) {
        const regionData = res.data
        this.setRegionData(regionData.provinces, regionData.cities, regionData.districts)
        console.log('地区数据加载成功:', {
          provinces: regionData.provinces.length,
          cities: Object.keys(regionData.cities).length,
          districts: Object.keys(regionData.districts).length
        })
        return Promise.resolve()
      } else {
        console.error('API返回数据格式错误:', res)
        wx.showToast({
          title: '地区数据加载失败',
          icon: 'none'
        })
        return Promise.reject('数据格式错误')
      }
    }).catch(err => {
      console.error('加载地区数据失败:', err)
      wx.showToast({
        title: '网络错误，请检查连接',
        icon: 'none'
      })
      return Promise.reject(err)
    })
  },



  // 设置地区数据
  setRegionData(provinces, cities, districts) {
    const defaultProvince = provinces[0] || ''
    const defaultCities = cities[defaultProvince] || []
    const defaultCity = defaultCities[0] || ''
    const defaultDistricts = districts[defaultCity] || []

    console.log('设置地区数据:', {
      provinces: provinces.length,
      defaultProvince,
      defaultCities: defaultCities.length,
      defaultCity,
      defaultDistricts: defaultDistricts.length
    })

    this.setData({
      regionArray: [provinces, defaultCities, defaultDistricts],
      regionData: { provinces, cities, districts },
      regionIndex: [0, 0, 0]
    })

    // 如果已经有备用地址的省市区数据，更新选择器
    if (this.data.backupRegion && this.data.backupRegion.length > 0) {
      this.updateRegionIndex(this.data.backupRegion[0], this.data.backupRegion[1], this.data.backupRegion[2])
    }
  },

  // 更新地区选择器索引
  updateRegionIndex(province, city, district) {
    const { regionData } = this.data

    console.log('开始更新地区选择器:', {
      输入参数: { province, city, district },
      地区数据状态: {
        hasRegionData: !!regionData,
        hasProvinces: !!(regionData && regionData.provinces),
        provincesLength: regionData && regionData.provinces ? regionData.provinces.length : 0
      }
    })

    if (!regionData || !regionData.provinces) {
      console.log('地区数据未加载完成')
      return
    }

    // 打印前几个省份名称用于调试
    console.log('地区数据中的前5个省份:', regionData.provinces.slice(0, 5))

    // 找到省份索引
    const provinceIndex = regionData.provinces.indexOf(province)
    if (provinceIndex === -1) {
      console.log('未找到省份:', {
        查找的省份: province,
        可用省份: regionData.provinces.filter(p => p.includes('上海') || p.includes('北京') || p.includes('新疆'))
      })
      return
    }

    // 更新城市和区县数组
    const cities = regionData.cities[province] || []
    const districts = regionData.districts[city] || []

    // 找到城市和区县索引
    const cityIndex = cities.indexOf(city)
    const districtIndex = districts.indexOf(district)

    console.log('更新地区选择器详细信息:', {
      province, city, district,
      provinceIndex, cityIndex, districtIndex,
      citiesLength: cities.length,
      districtsLength: districts.length,
      前3个城市: cities.slice(0, 3),
      前3个区县: districts.slice(0, 3)
    })

    this.setData({
      regionIndex: [
        provinceIndex >= 0 ? provinceIndex : 0,
        cityIndex >= 0 ? cityIndex : 0,
        districtIndex >= 0 ? districtIndex : 0
      ],
      regionArray: [
        regionData.provinces,
        cities,
        districts
      ]
    })

    console.log('地区选择器更新完成:', {
      最终索引: [
        provinceIndex >= 0 ? provinceIndex : 0,
        cityIndex >= 0 ? cityIndex : 0,
        districtIndex >= 0 ? districtIndex : 0
      ]
    })
  },

  // 显示地区选择器
  showRegionPicker() {
    console.log('显示地区选择器')
    this.setData({
      showRegionPicker: true
    })
  },

  // 地区选择变化
  onRegionChange(e) {
    const { value } = e.detail
    const { regionArray, regionData, regionIndex } = this.data

    console.log('地区选择变化:', { value, regionIndex })

    // 检查哪一列发生了变化
    let changedColumn = -1
    for (let i = 0; i < value.length; i++) {
      if (value[i] !== regionIndex[i]) {
        changedColumn = i
        break
      }
    }

    console.log('变化的列:', changedColumn)

    // 如果是省份变化，需要更新城市和区县数组
    if (changedColumn === 0) {
      const province = regionArray[0][value[0]]
      const cities = regionData.cities[province] || []
      const districts = regionData.districts[cities[0]] || []

      console.log('省份变化:', { province, cities: cities.length, districts: districts.length })

      const newRegionArray = [...regionArray]
      newRegionArray[1] = cities
      newRegionArray[2] = districts

      const newValue = [value[0], 0, 0] // 重置城市和区县选择

      this.setData({
        regionIndex: newValue,
        regionArray: newRegionArray
      })
    }
    // 如果是城市变化，需要更新区县数组
    else if (changedColumn === 1) {
      const province = regionArray[0][value[0]]
      const city = regionArray[1][value[1]]
      const districts = regionData.districts[city] || []

      console.log('城市变化:', { city, districts: districts.length })

      const newRegionArray = [...regionArray]
      newRegionArray[2] = districts

      const newValue = [value[0], value[1], 0] // 重置区县选择

      this.setData({
        regionIndex: newValue,
        regionArray: newRegionArray
      })
    }
    // 如果是区县变化，直接更新
    else {
      this.setData({
        regionIndex: value
      })
    }
  },

  // 确认地区选择
  onRegionConfirm() {
    const { regionArray, regionIndex } = this.data
    const province = regionArray[0][regionIndex[0]] || ''
    const city = regionArray[1][regionIndex[1]] || ''
    const district = regionArray[2][regionIndex[2]] || ''

    this.setData({
      backupRegion: [province, city, district],
      showRegionPicker: false
    })

    this.updateBackupAddress()
  },

  // 取消地区选择
  onRegionCancel() {
    this.setData({
      showRegionPicker: false
    })
  },



  // 更新完整的备用地址
  updateBackupAddress() {
    const region = this.data.backupRegion
    const detail = this.data.pickupInfo.backupDetailAddress
    let fullAddress = ''

    if (region.length > 0) {
      fullAddress = `${region[0]} ${region[1]} ${region[2]}`
      if (detail) {
        fullAddress += ` ${detail}`
      }
    } else if (detail) {
      fullAddress = detail
    }

    this.setData({
      'pickupInfo.backupAddress': fullAddress
    })
  },

  // 选择取货时间
  selectPickupTime() {
    console.log('点击了取货时间选择')
    this.initTimePickerData()

    // 延迟显示弹窗，确保数据初始化完成
    setTimeout(() => {
      this.setData({
        timePickerShow: true
      })
      console.log('时间选择器弹窗已显示')
    }, 100)
  },

  // 选择派送时间
  selectDeliveryTime() {
    console.log('点击了派送时间选择')
    this.initDeliveryTimePickerData()

    // 延迟显示弹窗，确保数据初始化完成
    setTimeout(() => {
      this.setData({
        deliveryTimePickerShow: true
      })
      console.log('派送时间选择器弹窗已显示')
    }, 100)
  },

  // 初始化时间选择器数据
  initTimePickerData() {
    const now = new Date()
    const dateOptions = []
    const allTimeOptions = ['09:00', '10:00', '11:00', '12:00', '13:00', '14:00', '15:00', '16:00', '17:00', '18:00']

    // 生成未来30天的日期选项
    for (let i = 0; i < 30; i++) {
      const date = new Date()
      date.setDate(now.getDate() + i)

      const year = date.getFullYear()
      const month = date.getMonth() + 1
      const day = date.getDate()
      const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
      const weekday = weekdays[date.getDay()]

      let dateStr
      if (i === 0) {
        dateStr = `今天 ${month}月${day}日(${weekday})`
      } else if (i === 1) {
        dateStr = `明天 ${month}月${day}日(${weekday})`
      } else {
        dateStr = `${month}月${day}日(${weekday})`
      }

      dateOptions.push({
        label: dateStr,
        value: date,
        index: i,
        year: year,
        month: month,
        day: day,
        weekday: weekday
      })
    }

    // 计算今天可用的时间选项
    const currentHour = now.getHours()
    let availableTimeOptions = allTimeOptions

    // 如果是今天且当前时间已过9点，过滤掉已经过去的时间
    if (currentHour >= 9) {
      availableTimeOptions = allTimeOptions.filter(time => {
        const hour = parseInt(time.split(':')[0])
        return hour > currentHour
      })
    }

    // 如果今天没有可用时间，使用全部时间选项
    if (availableTimeOptions.length === 0) {
      availableTimeOptions = allTimeOptions
    }

    this.setData({
      dateOptions: dateOptions,
      timeOptions: availableTimeOptions,
      timePickerIndex: [0, 0]
    })

    console.log('时间选择器数据初始化完成:', {
      dateOptions: dateOptions.length,
      timeOptions: availableTimeOptions.length,
      currentIndex: [0, 0]
    })
  },

  // 初始化派送时间选择器数据
  initDeliveryTimePickerData() {
    const now = new Date()
    const dateOptions = []
    const allTimeOptions = ['09:00', '10:00', '11:00', '12:00', '13:00', '14:00', '15:00', '16:00', '17:00', '18:00']

    // 生成未来30天的日期选项
    for (let i = 0; i < 30; i++) {
      const date = new Date()
      date.setDate(now.getDate() + i)

      const year = date.getFullYear()
      const month = date.getMonth() + 1
      const day = date.getDate()
      const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
      const weekday = weekdays[date.getDay()]

      let dateStr
      if (i === 0) {
        dateStr = `今天 ${month}月${day}日(${weekday})`
      } else if (i === 1) {
        dateStr = `明天 ${month}月${day}日(${weekday})`
      } else {
        dateStr = `${month}月${day}日(${weekday})`
      }

      dateOptions.push({
        label: dateStr,
        value: date,
        index: i,
        year: year,
        month: month,
        day: day,
        weekday: weekday
      })
    }

    // 计算今天可用的时间选项
    const currentHour = now.getHours()
    let availableTimeOptions = allTimeOptions

    // 如果是今天且当前时间已过9点，过滤掉已经过去的时间
    if (currentHour >= 9) {
      availableTimeOptions = allTimeOptions.filter(time => {
        const hour = parseInt(time.split(':')[0])
        return hour > currentHour
      })
    }

    // 如果今天没有可用时间，使用全部时间选项
    if (availableTimeOptions.length === 0) {
      availableTimeOptions = allTimeOptions
    }

    this.setData({
      deliveryDateOptions: dateOptions,
      deliveryTimeOptions: availableTimeOptions,
      deliveryTimePickerIndex: [0, 0]
    })

    console.log('派送时间选择器数据初始化完成:', {
      dateOptions: dateOptions.length,
      timeOptions: availableTimeOptions.length,
      currentIndex: [0, 0]
    })
  },

  // 时间选择器变化
  onTimePickerChange(e) {
    const { value } = e.detail
    const { dateOptions } = this.data
    const allTimeOptions = ['09:00', '10:00', '11:00', '12:00', '13:00', '14:00', '15:00', '16:00', '17:00', '18:00']

    console.log('时间选择器变化:', {
      oldValue: this.data.timePickerIndex,
      newValue: value,
      dateOptionsLength: dateOptions.length
    })

    // 确保索引在有效范围内
    const dateIndex = Math.max(0, Math.min(value[0], dateOptions.length - 1))
    const timeIndex = Math.max(0, Math.min(value[1], allTimeOptions.length - 1))

    // 如果日期变化，需要重新计算可用时间
    if (dateIndex !== this.data.timePickerIndex[0]) {
      const selectedDate = dateOptions[dateIndex]
      const now = new Date()
      const isToday = selectedDate && selectedDate.index === 0

      let availableTimeOptions = allTimeOptions

      if (isToday) {
        const currentHour = now.getHours()
        if (currentHour >= 9) {
          availableTimeOptions = allTimeOptions.filter(time => {
            const hour = parseInt(time.split(':')[0])
            return hour > currentHour
          })
        }
      }

      // 如果没有可用时间，使用全部时间选项
      if (availableTimeOptions.length === 0) {
        availableTimeOptions = allTimeOptions
      }

      // 重置时间选择为第一个可用时间
      const newTimeIndex = 0

      this.setData({
        timeOptions: availableTimeOptions,
        timePickerIndex: [dateIndex, newTimeIndex]
      })

      console.log('日期变化，更新时间选项:', {
        selectedDate: selectedDate ? selectedDate.label : 'undefined',
        availableTimeOptions: availableTimeOptions.length,
        newIndex: [dateIndex, newTimeIndex]
      })
    } else {
      // 只是时间变化
      this.setData({
        timePickerIndex: [dateIndex, timeIndex]
      })

      console.log('时间变化:', {
        newIndex: [dateIndex, timeIndex]
      })
    }
  },

  // 确认时间选择
  onTimePickerConfirm() {
    const { dateOptions, timeOptions, timePickerIndex } = this.data

    console.log('确认时间选择:', {
      timePickerIndex,
      dateOptionsLength: dateOptions.length,
      timeOptionsLength: timeOptions.length
    })

    // 确保索引在有效范围内
    const dateIndex = Math.max(0, Math.min(timePickerIndex[0], dateOptions.length - 1))
    const timeIndex = Math.max(0, Math.min(timePickerIndex[1], timeOptions.length - 1))

    const selectedDate = dateOptions[dateIndex]
    const selectedTime = timeOptions[timeIndex]

    if (selectedDate && selectedTime) {
      // 格式化最终显示的时间
      let displayDate = selectedDate.label
      if (displayDate.includes('今天 ')) {
        displayDate = displayDate.replace('今天 ', '')
      } else if (displayDate.includes('明天 ')) {
        displayDate = displayDate.replace('明天 ', '')
      }

      const finalDateTime = `${displayDate} ${selectedTime}`

      console.log('选择的时间:', {
        selectedDate: selectedDate.label,
        selectedTime: selectedTime,
        finalDateTime: finalDateTime
      })

      this.setData({
        'pickupInfo.pickupTime': finalDateTime,
        timePickerShow: false
      })

      wx.showToast({
        title: '取货日期选择成功',
        icon: 'success'
      })
    } else {
      console.error('时间选择错误:', {
        selectedDate,
        selectedTime,
        dateIndex,
        timeIndex
      })

      wx.showToast({
        title: '请选择有效时间',
        icon: 'none'
      })
    }
  },

  // 取消时间选择
  onTimePickerCancel() {
    this.setData({
      timePickerShow: false
    })
  },

  // 派送时间选择器变化
  onDeliveryTimePickerChange(e) {
    const { value } = e.detail
    const { deliveryDateOptions } = this.data
    const allTimeOptions = ['09:00', '10:00', '11:00', '12:00', '13:00', '14:00', '15:00', '16:00', '17:00', '18:00']

    console.log('派送时间选择器变化:', {
      oldValue: this.data.deliveryTimePickerIndex,
      newValue: value,
      dateOptionsLength: deliveryDateOptions.length
    })

    // 确保索引在有效范围内
    const dateIndex = Math.max(0, Math.min(value[0], deliveryDateOptions.length - 1))
    const timeIndex = Math.max(0, Math.min(value[1], allTimeOptions.length - 1))

    // 如果日期变化，需要重新计算可用时间
    if (dateIndex !== this.data.deliveryTimePickerIndex[0]) {
      const selectedDate = deliveryDateOptions[dateIndex]
      const now = new Date()
      const isToday = selectedDate && selectedDate.index === 0

      let availableTimeOptions = allTimeOptions

      if (isToday) {
        const currentHour = now.getHours()
        if (currentHour >= 9) {
          availableTimeOptions = allTimeOptions.filter(time => {
            const hour = parseInt(time.split(':')[0])
            return hour > currentHour
          })
        }
      }

      // 如果没有可用时间，使用全部时间选项
      if (availableTimeOptions.length === 0) {
        availableTimeOptions = allTimeOptions
      }

      // 重置时间选择为第一个可用时间
      const newTimeIndex = 0

      this.setData({
        deliveryTimeOptions: availableTimeOptions,
        deliveryTimePickerIndex: [dateIndex, newTimeIndex]
      })

      console.log('派送日期变化，更新时间选项:', {
        selectedDate: selectedDate ? selectedDate.label : 'undefined',
        availableTimeOptions: availableTimeOptions.length,
        newIndex: [dateIndex, newTimeIndex]
      })
    } else {
      // 只是时间变化
      this.setData({
        deliveryTimePickerIndex: [dateIndex, timeIndex]
      })

      console.log('派送时间变化:', {
        newIndex: [dateIndex, timeIndex]
      })
    }
  },

  // 确认派送时间选择
  onDeliveryTimePickerConfirm() {
    const { deliveryDateOptions, deliveryTimeOptions, deliveryTimePickerIndex } = this.data

    console.log('确认派送时间选择:', {
      deliveryTimePickerIndex,
      dateOptionsLength: deliveryDateOptions.length,
      timeOptionsLength: deliveryTimeOptions.length
    })

    // 确保索引在有效范围内
    const dateIndex = Math.max(0, Math.min(deliveryTimePickerIndex[0], deliveryDateOptions.length - 1))
    const timeIndex = Math.max(0, Math.min(deliveryTimePickerIndex[1], deliveryTimeOptions.length - 1))

    const selectedDate = deliveryDateOptions[dateIndex]
    const selectedTime = deliveryTimeOptions[timeIndex]

    if (selectedDate && selectedTime) {
      // 格式化最终显示的时间
      let displayDate = selectedDate.label
      if (displayDate.includes('今天 ')) {
        displayDate = displayDate.replace('今天 ', '')
      } else if (displayDate.includes('明天 ')) {
        displayDate = displayDate.replace('明天 ', '')
      }

      const finalDateTime = `${displayDate} ${selectedTime}`

      console.log('选择的派送时间:', {
        selectedDate: selectedDate.label,
        selectedTime: selectedTime,
        finalDateTime: finalDateTime
      })

      this.setData({
        deliveryTime: finalDateTime,
        deliveryTimePickerShow: false
      })

      wx.showToast({
        title: '收货日期选择成功',
        icon: 'success'
      })
    } else {
      console.error('派送时间选择错误:', {
        selectedDate,
        selectedTime,
        dateIndex,
        timeIndex
      })

      wx.showToast({
        title: '请选择有效时间',
        icon: 'none'
      })
    }
  },

  // 取消派送时间选择
  onDeliveryTimePickerCancel() {
    this.setData({
      deliveryTimePickerShow: false
    })
  },
  
  // 提交订单
  submitOrder() {
    if (this.data.isSubmitting) return

    // 验证表单
    if (!this.validateForm()) return

    this.setData({
      isSubmitting: true
    })

    wx.showLoading({
      title: '提交中...'
    })

    // 准备订单数据
    const orderData = this.prepareOrderData()

    app.request({
      url: '/order/create-direct',
      method: 'POST',
      data: orderData
    }).then(res => {
      wx.hideLoading()
      wx.showToast({
        title: '下单成功',
        icon: 'success'
      })

      // 如果是从购物车下单，清除购物车中对应的商品
      if (this.data.orderData && this.data.orderData.type === 'cart') {
        this.clearCartItems()
      }

      // 跳转到订单详情页面
      setTimeout(() => {
        wx.redirectTo({
          url: `/pages/order-detail/order-detail?id=${res.data.id}`
        })
      }, 1500)
    }).catch(err => {
      wx.hideLoading()
      this.setData({
        isSubmitting: false
      })
      console.error('下单失败', err)
      wx.showToast({
        title: err.message || '下单失败',
        icon: 'none'
      })
    })
  },

  // 验证表单
  validateForm() {
    if (this.data.deliveryType === 1) {
      // 外卖配送验证
      if (!this.data.selectedAddress) {
        wx.showToast({
          title: '请选择收货地址',
          icon: 'none'
        })
        return false
      }
      if (!this.data.deliveryTime.trim()) {
        wx.showToast({
          title: '请选择收货日期',
          icon: 'none'
        })
        return false
      }
    } else {
      // 自取验证
      const { pickupName, pickupPhone, pickupTime } = this.data.pickupInfo
      if (!pickupName.trim()) {
        wx.showToast({
          title: '请填写取货人姓名',
          icon: 'none'
        })
        return false
      }
      if (!pickupPhone.trim()) {
        wx.showToast({
          title: '请填写取货人电话',
          icon: 'none'
        })
        return false
      }
      if (!pickupTime.trim()) {
        wx.showToast({
          title: '请选择取货日期',
          icon: 'none'
        })
        return false
      }
    }
    return true
  },

  // 准备订单数据
  prepareOrderData() {
    const data = {
      userId: app.globalData.userId,
      deliveryType: this.data.deliveryType,
      items: this.data.items.map(item => ({
        flowerId: item.flowerId,
        quantity: item.quantity
      }))
    }

    if (this.data.deliveryType === 1) {
      // 外卖配送
      const addr = this.data.selectedAddress
      data.recipientName = addr.recipientName
      data.recipientPhone = addr.recipientPhone
      data.recipientAddress = app.formatFullAddress(addr.province, addr.city, addr.district, addr.detailedAddress)
      data.deliveryTime = this.data.deliveryTime // 派送时间
      data.remark = this.data.deliveryRemark // 配送备注
    } else {
      // 自取
      data.pickupName = this.data.pickupInfo.pickupName
      data.pickupPhone = this.data.pickupInfo.pickupPhone
      data.pickupTime = this.data.pickupInfo.pickupTime
      data.remark = this.data.pickupInfo.remark
      data.backupAddress = this.data.pickupInfo.backupAddress
    }

    return data
  },

  // 返回上一页
  goBack() {
    wx.navigateBack()
  },

  // 关闭地址选择器
  closeAddressPicker() {
    this.setData({
      addressPickerShow: false
    })
  },

  // 跳转到添加地址页面
  goToAddAddress() {
    this.setData({
      addressPickerShow: false
    })
    wx.navigateTo({
      url: '/pages/address-edit/address-edit'
    })
  },

  // 清除购物车中已下单的商品
  clearCartItems() {
    if (!app.globalData.userId) return

    // 获取已下单的商品ID列表
    const flowerIds = this.data.items.map(item => item.flowerId)

    if (flowerIds.length === 0) return

    console.log('清除购物车商品:', flowerIds)

    // 批量删除购物车商品
    app.request({
      url: '/cart/batch-remove',
      method: 'DELETE',
      data: {
        userId: app.globalData.userId,
        flowerIds: flowerIds
      }
    }).then(res => {
      const removedCount = res.data || 0
      console.log(`成功清除 ${removedCount} 个购物车商品`)

      if (removedCount > 0) {
        // 通知购物车页面刷新（如果存在）
        const pages = getCurrentPages()
        const cartPage = pages.find(page => page.route === 'pages/cart/cart')
        if (cartPage && cartPage.loadCartItems) {
          cartPage.loadCartItems()
        }

        // 更新全局购物车数量
        if (app.updateCartCount) {
          app.updateCartCount()
        }
      }
    }).catch(err => {
      console.error('批量清除购物车商品失败', err)
      // 如果批量删除失败，回退到单个删除
      this.fallbackClearCartItems(flowerIds)
    })
  },

  // 回退方案：单个删除购物车商品
  fallbackClearCartItems(flowerIds) {
    console.log('使用回退方案单个删除购物车商品')

    const deletePromises = flowerIds.map(flowerId => {
      return app.request({
        url: '/cart/remove',
        method: 'DELETE',
        data: {
          userId: app.globalData.userId,
          flowerId: flowerId
        }
      }).catch(err => {
        console.error(`删除购物车商品失败 flowerId: ${flowerId}`, err)
        return null
      })
    })

    Promise.all(deletePromises).then(results => {
      const successCount = results.filter(result => result !== null).length
      console.log(`回退方案成功清除 ${successCount}/${flowerIds.length} 个购物车商品`)

      if (successCount > 0) {
        // 通知购物车页面刷新
        const pages = getCurrentPages()
        const cartPage = pages.find(page => page.route === 'pages/cart/cart')
        if (cartPage && cartPage.loadCartItems) {
          cartPage.loadCartItems()
        }

        // 更新全局购物车数量
        if (app.updateCartCount) {
          app.updateCartCount()
        }
      }
    })
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 空函数，用于阻止事件冒泡
  }
})
