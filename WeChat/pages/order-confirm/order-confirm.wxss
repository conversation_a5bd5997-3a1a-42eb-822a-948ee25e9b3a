/* pages/order-confirm/order-confirm.wxss */

/* 页面容器 */
.container {
  min-height: 100vh;
  background: #f8f8f8;
  display: flex;
  flex-direction: column;
}



/* 内容区域 */
.content {
  flex: 1;
  padding: 20rpx 0 160rpx 0;
}

/* 通用区块 */
.section {
  background: white;
  margin-bottom: 20rpx;
  padding: 30rpx;
}

.section-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

/* 商品列表 */
.product-list {
  border-radius: 12rpx;
  overflow: hidden;
}

.product-item {
  display: flex;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.product-item:last-child {
  border-bottom: none;
}

.product-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.product-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
}

.product-desc {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 15rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.product-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.product-price {
  font-size: 28rpx;
  font-weight: 600;
  color: #ff6b9d;
}

.product-quantity {
  font-size: 24rpx;
  color: #666;
}

/* 配送方式 */
.delivery-option {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
}

.option-text {
  font-size: 28rpx;
  color: #333;
  margin-left: 20rpx;
}

/* 地址选择 */
.address-section {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background: #f8f8f8;
  border-radius: 12rpx;
  border: 1rpx solid #e0e0e0;
}

.address-content {
  flex: 1;
}

.address-header {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.recipient-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-right: 20rpx;
}

.recipient-phone {
  font-size: 26rpx;
  color: #666;
  margin-right: 20rpx;
}

.default-badge {
  background: linear-gradient(135deg, #ff6b9d, #ff8fab);
  color: white;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
}

.badge-text {
  font-size: 20rpx;
}

.address-detail {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

.no-address {
  flex: 1;
}

.no-address-text {
  font-size: 28rpx;
  color: #999;
}

.arrow-icon {
  font-size: 28rpx;
  color: #999;
  margin-left: 20rpx;
}

/* 表单 */
.form-group {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.form-item {
  display: flex;
  flex-direction: column;
  margin-bottom: 30rpx;
}

.form-label {
  font-size: 26rpx;
  color: #333;
  margin-bottom: 10rpx;
  margin-left: 2%;
  display: block;
}

.form-desc {
  font-size: 22rpx;
  color: #999;
  margin-bottom: 15rpx;
  line-height: 1.4;
  display: block;
}

.required {
  color: #ff6b9d;
}

.form-input {
  padding: 20rpx;
  background: #f8f8f8;
  border-radius: 8rpx;
  border: 1rpx solid #e0e0e0;
  font-size: 28rpx;
  color: #333;
  width: 100%;
  box-sizing: border-box;
  min-height: 80rpx;
}

.form-input::placeholder {
  color: #999;
}

.form-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
  box-sizing: border-box;
}

.form-input-wrapper .form-input {
  flex: 1;
  padding-right: 60rpx;
  width: 100%;
  box-sizing: border-box;
}

.form-input-wrapper .arrow-icon {
  position: absolute;
  right: 20rpx;
  margin: 0;
  font-size: 24rpx;
  color: #999;
}

.form-textarea {
  padding: 20rpx;
  background: #f8f8f8;
  border-radius: 8rpx;
  border: 1rpx solid #e0e0e0;
  font-size: 28rpx;
  color: #333;
  width: 100%;
  box-sizing: border-box;
  min-height: 80rpx;
  max-height: 120rpx;
  resize: none;
}

/* 省市区选择器 */
.region-selector {
  margin-bottom: 20rpx;
  width: 100%;
  box-sizing: border-box;
}

.region-selector picker {
  width: 100%;
  display: block;
}

.region-item {
  position: relative;
  display: flex;
  align-items: center;
  padding: 20rpx;
  background: #f8f8f8;
  border-radius: 8rpx;
  border: 1rpx solid #e0e0e0;
  width: 100%;
  box-sizing: border-box;
  min-height: 80rpx;
}

.region-label {
  font-size: 26rpx;
  color: #333;
  width: 120rpx;
  flex-shrink: 0;
}

.region-value {
  font-size: 26rpx;
  color: #333;
  flex: 1;
  padding-right: 40rpx;
}

.region-placeholder {
  font-size: 26rpx;
  color: #999;
  flex: 1;
  padding-right: 40rpx;
}

.region-item .arrow-icon {
  position: absolute;
  right: 20rpx;
  margin: 0;
  font-size: 24rpx;
  color: #999;
}

/* 地区选择器弹窗 */
.picker-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
}

.picker-container {
  width: 100%;
  background: white;
  border-radius: 20rpx 20rpx 0 0;
  max-height: 85vh;
  min-height: 700rpx;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.picker-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 40rpx;
  border-bottom: 1rpx solid #eee;
}

.picker-cancel,
.picker-confirm {
  font-size: 28rpx;
  color: #ff6b9d;
}

.picker-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

/* 时间选择器 */
.time-picker-content {
  padding: 20rpx 0;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.time-picker-desc {
  display: block;
  text-align: center;
  font-size: 26rpx;
  color: #666;
  margin-bottom: 20rpx;
  padding: 0 40rpx;
  flex-shrink: 0;
}

.time-picker {
  height: 500rpx;
  width: 100%;
  flex: 1;
}

.region-picker {
  height: 500rpx;
  width: 100%;
  flex: 1;
}

.picker-item {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100rpx;
  font-size: 26rpx;
  color: #333;
  line-height: 1.3;
  text-align: center;
  box-sizing: border-box;
  padding: 15rpx 8rpx;
  word-break: keep-all;
  white-space: nowrap;
  overflow: visible;
  min-width: 0;
  flex-shrink: 0;
}

/* 修复picker-view滚动问题 */
picker-view {
  width: 100%;
  height: 100%;
  background: transparent;
}

picker-view-column {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 0 8rpx;
  min-width: 0;
  flex: 1;
}

/* 针对地区选择器的特殊样式 */
.region-picker picker-view-column {
  width: 33.33%;
  min-width: 200rpx;
}

.region-picker .picker-item {
  font-size: 24rpx;
  padding: 12rpx 5rpx;
  height: 90rpx;
  line-height: 1.2;
}





/* 金额详情 */
.amount-detail {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.amount-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.amount-item.total {
  padding-top: 15rpx;
  margin-bottom: 30rpx;
  border-top: 1rpx solid #f0f0f0;
}

.amount-label {
  font-size: 26rpx;
  color: #666;
}

.amount-item.total .amount-label {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.amount-value {
  font-size: 26rpx;
  color: #333;
}

.total-price {
  font-size: 32rpx;
  font-weight: 600;
  color: #ff6b9d;
}

/* 底部提交 */
.submit-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 30rpx 30rpx;
  padding-bottom: calc(30rpx + env(safe-area-inset-bottom));
  border-top: 1rpx solid #f0f0f0;
  display: flex;
  align-items: center;
  gap: 20rpx;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 20rpx;
}

.submit-info {
  flex: 1;
}

.total-text {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.submit-btn {
  background: linear-gradient(135deg, #ff6b9d, #ff8fab);
  color: white;
  padding: 25rpx 50rpx;
  border-radius: 50rpx;
  font-size: 28rpx;
  font-weight: 600;
  transition: all 0.2s ease;
}

.submit-btn.disabled {
  background: #ccc;
  color: #999;
}

.submit-btn:not(.disabled):active {
  transform: scale(0.95);
}

.submit-text {
  font-size: 28rpx;
}

/* 弹窗 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
}

.address-picker {
  background: white;
  border-radius: 20rpx 20rpx 0 0;
  width: 100%;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  background: #fafafa;
  border-radius: 20rpx 20rpx 0 0;
}

.picker-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.picker-close {
  font-size: 36rpx;
  color: #999;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: #f5f5f5;
  transition: all 0.2s ease;
}

.picker-close:active {
  background: #e8e8e8;
  transform: scale(0.95);
}

.picker-content {
  flex: 1;
  max-height: 500rpx;
  padding: 0;
}

/* 地址选择项样式 */
.address-picker-item {
  margin: 0;
  transition: all 0.2s ease;
}

.address-picker-item:active {
  background: #f8f8f8;
}

.address-picker-item.selected {
  background: rgba(255, 107, 157, 0.05);
  border-left: 4rpx solid #ff6b9d;
}

.address-item-wrapper {
  padding: 24rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

/* 地址项内容样式 */
.address-item-content {
  width: 100%;
}

.address-item-content .address-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12rpx;
}

.recipient-info {
  display: flex;
  align-items: center;
  gap: 16rpx;
  flex: 1;
}

.recipient-info .recipient-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.recipient-info .recipient-phone {
  font-size: 26rpx;
  color: #666;
}

.address-badges {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.default-badge {
  background: linear-gradient(135deg, #ff6b9d, #ff8fab);
  color: white;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
}

.selected-badge {
  background: #ff6b9d;
  color: white;
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.selected-icon {
  font-size: 18rpx;
  font-weight: bold;
}

.address-detail-wrapper {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.address-detail-wrapper .address-detail {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

.detailed-address {
  font-size: 24rpx;
  color: #999;
  line-height: 1.4;
}

/* 添加新地址项 */
.add-address-item {
  padding: 24rpx 30rpx;
  border-top: 1rpx solid #f0f0f0;
  background: #fafafa;
  transition: all 0.2s ease;
}

.add-address-item:active {
  background: #f0f0f0;
}

.add-address-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
}

.add-icon {
  font-size: 32rpx;
  color: #ff6b9d;
  font-weight: bold;
}

.add-text {
  font-size: 28rpx;
  color: #ff6b9d;
  font-weight: 500;
}

.address-item-content .address-header {
  margin-bottom: 10rpx;
}
