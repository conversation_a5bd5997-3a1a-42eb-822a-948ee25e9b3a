/* pages/phone-auth/phone-auth.wxss */
.container {
  padding: 40rpx 30rpx;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #333;
}

.header {
  text-align: center;
  margin-bottom: 80rpx;
}

.title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 20rpx;
}

.subtitle {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.5;
}

.phone-section, .verify-section {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
}

.input-group {
  margin-bottom: 20rpx;
}

.label {
  display: block;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
  font-weight: 500;
}

.phone-input, .verify-input {
  width: 100%;
  height: 80rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  padding: 0 20rpx;
  font-size: 32rpx;
  box-sizing: border-box;
  transition: border-color 0.3s;
}

.phone-input:focus, .verify-input:focus {
  border-color: #667eea;
}

.phone-input.error, .verify-input.error {
  border-color: #ff4757;
}

.verify-input-wrapper {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.verify-input {
  flex: 1;
}

.send-code-btn {
  width: 180rpx;
  height: 80rpx;
  background: #667eea;
  color: white;
  border: none;
  border-radius: 12rpx;
  font-size: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.send-code-btn.disabled {
  background: #ccc;
  color: #999;
}

.error-text {
  color: #ff4757;
  font-size: 24rpx;
  margin-top: 10rpx;
}

.wechat-auth-section {
  margin: 50rpx 0;
}

.divider {
  text-align: center;
  margin: 40rpx 0;
  position: relative;
}

.divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1rpx;
  background: rgba(255, 255, 255, 0.3);
}

.divider-text {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: rgba(255, 255, 255, 0.8);
  padding: 0 20rpx;
  font-size: 24rpx;
  position: relative;
  z-index: 1;
}

.wechat-auth-btn {
  width: 100%;
  height: 100rpx;
  background: white;
  border: 2rpx solid #e0e0e0;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20rpx;
  box-shadow: 0 5rpx 15rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
}

.wechat-auth-btn:active {
  transform: scale(0.98);
}

.wechat-auth-btn.secondary {
  background: #f8f9fa;
  border-color: #dee2e6;
}

.wechat-icon {
  font-size: 32rpx;
  margin-right: 20rpx;
}

.btn-text {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
}

.action-buttons {
  margin: 60rpx 0 40rpx;
}

.confirm-btn {
  width: 100%;
  height: 100rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 20rpx;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  box-shadow: 0 10rpx 30rpx rgba(102, 126, 234, 0.3);
  transition: all 0.3s;
}

.confirm-btn:active {
  transform: scale(0.98);
}

.confirm-btn.disabled {
  background: #ccc;
  color: #999;
  box-shadow: none;
}

.skip-btn {
  width: 100%;
  height: 80rpx;
  background: transparent;
  color: rgba(255, 255, 255, 0.8);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 16rpx;
  font-size: 28rpx;
}

.tips {
  margin-top: 40rpx;
}

.tip-text {
  display: block;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
  line-height: 2;
  margin-bottom: 10rpx;
}
