<!--pages/phone-auth/phone-auth.wxml-->
<view class="container">
  <!-- 顶部标题 -->
  <view class="header">
    <text class="title">绑定手机号</text>
    <text class="subtitle">为了更好地为您提供服务，请绑定您的手机号</text>
  </view>

  <!-- 手机号输入区域 -->
  <view class="phone-section">
    <view class="input-group">
      <text class="label">手机号</text>
      <input 
        class="phone-input {{phoneError ? 'error' : ''}}"
        type="number"
        placeholder="请输入11位手机号"
        value="{{phoneNumber}}"
        maxlength="11"
        bindinput="onPhoneInput"
      />
    </view>
    <view class="error-text" wx:if="{{phoneError}}">{{phoneError}}</view>
  </view>

  <!-- 验证码区域 -->
  <view class="verify-section" wx:if="{{showVerifyCode}}">
    <view class="input-group">
      <text class="label">验证码</text>
      <view class="verify-input-wrapper">
        <input 
          class="verify-input {{verifyError ? 'error' : ''}}"
          type="number"
          placeholder="请输入6位验证码"
          value="{{verifyCode}}"
          maxlength="6"
          bindinput="onVerifyInput"
        />
        <button 
          class="send-code-btn {{canSendCode ? '' : 'disabled'}}"
          bindtap="sendVerifyCode"
          disabled="{{!canSendCode}}"
        >
          {{sendCodeText}}
        </button>
      </view>
    </view>
    <view class="error-text" wx:if="{{verifyError}}">{{verifyError}}</view>
  </view>

  <!-- 微信授权方式 -->
  <view class="wechat-auth-section">
    <view class="divider">
      <text class="divider-text">或使用微信授权</text>
    </view>
    
    <!-- 新版手机号快速验证组件 -->
    <button
      class="wechat-auth-btn"
      open-type="getRealtimePhoneNumber"
      bindgetrealtimephonenumber="getRealtimePhoneNumber"
    >
      <text class="wechat-icon">💬</text>
      <text class="btn-text">微信手机号快速验证</text>
    </button>

    <!-- 兼容旧版授权方式 -->
    <button
      class="wechat-auth-btn secondary"
      open-type="getPhoneNumber"
      bindgetphonenumber="getPhoneNumber"
    >
      <text class="wechat-icon">💬</text>
      <text class="btn-text">微信手机号授权（兼容）</text>
    </button>
  </view>

  <!-- 操作按钮 -->
  <view class="action-buttons">
    <button 
      class="confirm-btn {{canConfirm ? '' : 'disabled'}}"
      bindtap="confirmBind"
      disabled="{{!canConfirm || isLoading}}"
    >
      {{isLoading ? '绑定中...' : '确认绑定'}}
    </button>
    
    <button 
      class="skip-btn"
      bindtap="skipBind"
    >
      暂时跳过
    </button>
  </view>

  <!-- 提示信息 -->
  <view class="tips">
    <text class="tip-text">• 绑定手机号后可以享受更多服务</text>
    <text class="tip-text">• 您的手机号信息将被严格保护</text>
    <text class="tip-text">• 可随时在个人中心修改手机号</text>
  </view>
</view>
