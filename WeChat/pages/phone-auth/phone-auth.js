// pages/phone-auth/phone-auth.js
const app = getApp()

Page({
  data: {
    phoneNumber: '',
    verifyCode: '',
    phoneError: '',
    verifyError: '',
    showVerifyCode: false,
    canSendCode: false,
    canConfirm: false,
    isLoading: false,
    sendCodeText: '发送验证码',
    countdown: 0
  },

  onLoad(options) {
    // 检查是否从其他页面传入了参数
    if (options.from) {
      this.setData({
        from: options.from
      })
    }
  },

  // 手机号输入
  onPhoneInput(e) {
    const phoneNumber = e.detail.value
    const isValid = this.validatePhone(phoneNumber)
    
    this.setData({
      phoneNumber: phoneNumber,
      phoneError: phoneNumber && !isValid ? '请输入正确的11位手机号' : '',
      canSendCode: isValid,
      showVerifyCode: isValid,
      canConfirm: isValid && this.data.verifyCode.length === 6
    })
  },

  // 验证码输入
  onVerifyInput(e) {
    const verifyCode = e.detail.value
    const isValid = verifyCode.length === 6
    
    this.setData({
      verifyCode: verifyCode,
      verifyError: verifyCode && !isValid ? '请输入6位验证码' : '',
      canConfirm: this.validatePhone(this.data.phoneNumber) && isValid
    })
  },

  // 验证手机号格式
  validatePhone(phone) {
    return /^1[3-9]\d{9}$/.test(phone)
  },

  // 发送验证码
  sendVerifyCode() {
    if (!this.data.canSendCode || this.data.countdown > 0) {
      return
    }

    const { phoneNumber } = this.data

    wx.showLoading({
      title: '发送中...',
      mask: true
    })

    app.request({
      url: '/user/send-sms',
      method: 'POST',
      data: {
        phoneNumber: phoneNumber
      }
    }).then(response => {
      wx.hideLoading()
      
      if (response.code === 200) {
        wx.showToast({
          title: '验证码已发送',
          icon: 'success'
        })
        
        // 开始倒计时
        this.startCountdown()
      } else {
        wx.showToast({
          title: response.message || '发送失败',
          icon: 'none'
        })
      }
    }).catch(err => {
      wx.hideLoading()
      console.error('发送验证码失败:', err)
      wx.showToast({
        title: '发送失败，请重试',
        icon: 'none'
      })
    })
  },

  // 开始倒计时
  startCountdown() {
    let countdown = 60
    this.setData({
      countdown: countdown,
      sendCodeText: `${countdown}s后重发`,
      canSendCode: false
    })

    const timer = setInterval(() => {
      countdown--
      if (countdown <= 0) {
        clearInterval(timer)
        this.setData({
          countdown: 0,
          sendCodeText: '发送验证码',
          canSendCode: this.validatePhone(this.data.phoneNumber)
        })
      } else {
        this.setData({
          countdown: countdown,
          sendCodeText: `${countdown}s后重发`
        })
      }
    }, 1000)
  },

  // 确认绑定
  confirmBind() {
    if (!this.data.canConfirm || this.data.isLoading) {
      return
    }

    const { phoneNumber, verifyCode } = this.data

    this.setData({
      isLoading: true
    })

    app.request({
      url: '/user/phone-verify',
      method: 'POST',
      data: {
        userId: app.globalData.userId,
        phoneNumber: phoneNumber,
        verifyCode: verifyCode
      }
    }).then(response => {
      this.setData({
        isLoading: false
      })

      if (response.code === 200) {
        // 更新全局用户信息
        app.globalData.userInfo.phone = phoneNumber
        
        wx.showToast({
          title: '绑定成功',
          icon: 'success'
        })

        setTimeout(() => {
          this.goBack()
        }, 1500)
      } else {
        wx.showToast({
          title: response.message || '绑定失败',
          icon: 'none'
        })
      }
    }).catch(err => {
      this.setData({
        isLoading: false
      })
      console.error('绑定手机号失败:', err)
      wx.showToast({
        title: '绑定失败，请重试',
        icon: 'none'
      })
    })
  },

  // 新版微信手机号快速验证
  getRealtimePhoneNumber(e) {
    console.log('新版手机号快速验证:', e)

    if (e.detail.errMsg === 'getRealtimePhoneNumber:ok') {
      console.log('用户同意新版手机号授权')

      wx.showLoading({
        title: '正在获取手机号...',
        mask: true
      })

      // 使用新版API的code
      app.request({
        url: '/user/phone',
        method: 'POST',
        data: {
          userId: app.globalData.userId,
          code: e.detail.code
        }
      }).then(response => {
        wx.hideLoading()
        this.handlePhoneAuthSuccess(response)
      }).catch(err => {
        wx.hideLoading()
        this.handlePhoneAuthError(err)
      })
    } else {
      console.log('新版手机号授权失败:', e.detail.errMsg)
      this.handlePhoneAuthDeny()
    }
  },

  // 兼容旧版微信手机号授权
  getPhoneNumber(e) {
    console.log('旧版手机号授权:', e)

    if (e.detail.errMsg === 'getPhoneNumber:ok') {
      console.log('用户同意旧版手机号授权')

      wx.showLoading({
        title: '正在获取手机号...',
        mask: true
      })

      // 使用旧版API的加密数据
      app.request({
        url: '/user/phone',
        method: 'POST',
        data: {
          userId: app.globalData.userId,
          encryptedData: e.detail.encryptedData,
          iv: e.detail.iv
        }
      }).then(response => {
        wx.hideLoading()
        this.handlePhoneAuthSuccess(response)
      }).catch(err => {
        wx.hideLoading()
        this.handlePhoneAuthError(err)
      })
    } else {
      console.log('旧版手机号授权失败:', e.detail.errMsg)
      this.handlePhoneAuthDeny()
    }
  },

  // 处理手机号授权成功
  handlePhoneAuthSuccess(response) {
    if (response.code === 200 && response.data) {
      const phoneNumber = response.data.purePhoneNumber || response.data.phoneNumber
      
      // 更新全局用户信息
      app.globalData.userInfo.phone = phoneNumber
      
      wx.showToast({
        title: '手机号授权成功',
        icon: 'success'
      })

      setTimeout(() => {
        this.goBack()
      }, 1500)
    } else {
      throw new Error(response.message || '手机号获取失败')
    }
  },

  // 处理手机号授权错误
  handlePhoneAuthError(err) {
    console.error('手机号授权失败:', err)
    wx.showModal({
      title: '授权失败',
      content: '获取手机号失败，您可以手动输入手机号进行验证',
      showCancel: false,
      confirmText: '知道了'
    })
  },

  // 处理用户拒绝授权
  handlePhoneAuthDeny() {
    wx.showModal({
      title: '提示',
      content: '您可以手动输入手机号进行验证，或者暂时跳过此步骤',
      showCancel: false,
      confirmText: '知道了'
    })
  },

  // 跳过绑定
  skipBind() {
    wx.showModal({
      title: '确认跳过',
      content: '跳过手机号绑定可能会影响部分功能的使用，确定要跳过吗？',
      success: (res) => {
        if (res.confirm) {
          this.goBack()
        }
      }
    })
  },

  // 返回上一页
  goBack() {
    const pages = getCurrentPages()
    if (pages.length > 1) {
      wx.navigateBack()
    } else {
      wx.switchTab({
        url: '/pages/index/index'
      })
    }
  }
})
